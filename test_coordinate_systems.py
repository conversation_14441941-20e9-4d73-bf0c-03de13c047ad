#!/usr/bin/env python3

from step_loader import STEPLoader
import os

print("=== TESTING COORDINATE SYSTEM PRESERVATION ===")

# Test original file
print("\n1. ORIGINAL FILE:")
loader1 = STEPLoader()
result1 = loader1.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')
axis_data1 = loader1.get_original_axis2_placement()
if axis_data1:
    print(f"   Point: {axis_data1['point']}")
    print(f"   Dir1: {axis_data1['dir1']}")
    print(f"   Dir2: {axis_data1['dir2']}")
else:
    print("   No coordinate system data found")

# Test saved file
print("\n2. SAVED FILE (test-opt1.step):")
if os.path.exists('test-opt1.step'):
    loader2 = STEPLoader()
    result2 = loader2.load_step_file('test-opt1.step')
    axis_data2 = loader2.get_original_axis2_placement()
    if axis_data2:
        print(f"   Point: {axis_data2['point']}")
        print(f"   Dir1: {axis_data2['dir1']}")
        print(f"   Dir2: {axis_data2['dir2']}")
    else:
        print("   No coordinate system data found")
        
    # Compare
    print("\n3. COMPARISON:")
    if axis_data1 and axis_data2:
        if axis_data1 == axis_data2:
            print("   ✅ COORDINATE SYSTEMS MATCH PERFECTLY!")
        else:
            print("   ❌ COORDINATE SYSTEMS ARE DIFFERENT:")
            print(f"      Point diff: {axis_data1['point']} vs {axis_data2['point']}")
            print(f"      Dir1 diff: {axis_data1['dir1']} vs {axis_data2['dir1']}")
            print(f"      Dir2 diff: {axis_data1['dir2']} vs {axis_data2['dir2']}")
    else:
        print("   ❌ Cannot compare - missing data")
else:
    print("   File not found")

print("\n=== TEST COMPLETE ===")
