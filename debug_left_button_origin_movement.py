#!/usr/bin/env python3
"""
AUTOMATED DEBUG PROGRAM: Left Button Origin Movement Issue
==========================================================

This program automatically tests and debugs the left button origin marker movement
issue without requiring any manual interaction. It will:

1. Load the step_viewer program
2. Load a STEP file automatically
3. Enable origin overlays
4. Test left button rotations
5. Compare origin marker positions before/after rotation
6. Identify exactly where the issue occurs
7. Provide detailed diagnostic output

Run this program and it will show you exactly what's wrong with the origin movement.
"""

import sys
import os
import time
import subprocess
import threading
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
import vtk

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class OriginMovementDebugger(QThread):
    """Automated debugger for left button origin movement issues"""
    
    debug_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.step_viewer = None
        self.test_results = []
        
    def run(self):
        """Main debug routine - runs automatically"""
        try:
            self.debug_signal.emit("=" * 80)
            self.debug_signal.emit("AUTOMATED LEFT BUTTON ORIGIN MOVEMENT DEBUG")
            self.debug_signal.emit("=" * 80)
            
            # Step 1: Import and initialize step viewer
            self.debug_signal.emit("\n1. IMPORTING STEP VIEWER...")
            self.import_step_viewer()
            
            # Step 2: Create step viewer instance
            self.debug_signal.emit("\n2. CREATING STEP VIEWER INSTANCE...")
            self.create_step_viewer()
            
            # Step 3: Load a test STEP file
            self.debug_signal.emit("\n3. LOADING TEST STEP FILE...")
            self.load_test_file()
            
            # Step 4: Enable origin overlays
            self.debug_signal.emit("\n4. ENABLING ORIGIN OVERLAYS...")
            self.enable_origin_overlays()
            
            # Step 5: Test initial origin positions
            self.debug_signal.emit("\n5. RECORDING INITIAL ORIGIN POSITIONS...")
            initial_positions = self.record_origin_positions("INITIAL")
            
            # Step 6: Test left button rotation
            self.debug_signal.emit("\n6. TESTING LEFT BUTTON ROTATION (X+ 15 degrees)...")
            self.test_left_button_rotation('x', 15)
            
            # Step 7: Check origin positions after rotation
            self.debug_signal.emit("\n7. RECORDING ORIGIN POSITIONS AFTER ROTATION...")
            after_positions = self.record_origin_positions("AFTER ROTATION")
            
            # Step 8: Compare positions and analyze
            self.debug_signal.emit("\n8. ANALYZING POSITION CHANGES...")
            self.analyze_position_changes(initial_positions, after_positions)
            
            # Step 9: Test right button for comparison
            self.debug_signal.emit("\n9. TESTING RIGHT BUTTON FOR COMPARISON...")
            self.test_right_button_movement()
            
            # Step 10: Final diagnosis
            self.debug_signal.emit("\n10. FINAL DIAGNOSIS...")
            self.provide_diagnosis()
            
        except Exception as e:
            self.debug_signal.emit(f"ERROR in debug routine: {str(e)}")
            import traceback
            self.debug_signal.emit(f"Traceback: {traceback.format_exc()}")
    
    def import_step_viewer(self):
        """Import the step viewer module"""
        try:
            global step_viewer
            import step_viewer
            self.debug_signal.emit("✓ Step viewer imported successfully")
        except Exception as e:
            self.debug_signal.emit(f"✗ Failed to import step viewer: {e}")
            raise
    
    def create_step_viewer(self):
        """Create step viewer instance"""
        try:
            # Create QApplication if it doesn't exist
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            # Create step viewer instance
            self.step_viewer = step_viewer.StepViewerTDK()
            self.debug_signal.emit("✓ Step viewer instance created")
            
            # Show the window
            self.step_viewer.show()
            self.debug_signal.emit("✓ Step viewer window shown")
            
        except Exception as e:
            self.debug_signal.emit(f"✗ Failed to create step viewer: {e}")
            raise
    
    def load_test_file(self):
        """Load a test STEP file"""
        try:
            # Look for any STEP files in the current directory
            step_files = [f for f in os.listdir('.') if f.lower().endswith(('.step', '.stp'))]
            
            if not step_files:
                self.debug_signal.emit("✗ No STEP files found in current directory")
                self.debug_signal.emit("   Creating a simple test geometry...")
                self.create_test_geometry()
                return
            
            # Use the first STEP file found
            test_file = step_files[0]
            self.debug_signal.emit(f"   Found STEP file: {test_file}")
            
            # Load the file
            success = self.step_viewer.load_step_file(test_file)
            if success:
                self.debug_signal.emit(f"✓ Successfully loaded: {test_file}")
            else:
                self.debug_signal.emit(f"✗ Failed to load: {test_file}")
                self.create_test_geometry()
                
        except Exception as e:
            self.debug_signal.emit(f"✗ Error loading test file: {e}")
            self.create_test_geometry()
    
    def create_test_geometry(self):
        """Create a simple test geometry if no STEP file is available"""
        try:
            self.debug_signal.emit("   Creating simple cube geometry for testing...")
            
            # Create a simple cube using VTK
            cube_source = vtk.vtkCubeSource()
            cube_source.SetXLength(10)
            cube_source.SetYLength(10) 
            cube_source.SetZLength(10)
            cube_source.Update()
            
            # Create mapper and actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(cube_source.GetOutputPort())
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(0.8, 0.8, 0.9)  # Light blue
            
            # Add to both renderers
            if hasattr(self.step_viewer, 'vtk_renderer_left'):
                self.step_viewer.vtk_renderer_left.AddActor(actor)
                self.step_viewer.vtk_widget_left.GetRenderWindow().Render()
            
            if hasattr(self.step_viewer, 'vtk_renderer_right'):
                self.step_viewer.vtk_renderer_right.AddActor(actor)
                self.step_viewer.vtk_widget_right.GetRenderWindow().Render()
            
            self.debug_signal.emit("✓ Test cube geometry created")
            
        except Exception as e:
            self.debug_signal.emit(f"✗ Failed to create test geometry: {e}")
    
    def enable_origin_overlays(self):
        """Enable origin overlays to make markers visible"""
        try:
            # Try to find and click the toggle origin overlay button
            if hasattr(self.step_viewer, 'toggle_origin_overlay'):
                self.step_viewer.toggle_origin_overlay()
                self.debug_signal.emit("✓ Origin overlays enabled via toggle method")
            else:
                # Try to create origin overlays manually
                self.step_viewer.create_origin_overlay()
                self.debug_signal.emit("✓ Origin overlays created manually")
                
        except Exception as e:
            self.debug_signal.emit(f"✗ Failed to enable origin overlays: {e}")
    
    def record_origin_positions(self, phase_name):
        """Record current positions of all origin markers"""
        positions = {
            'phase': phase_name,
            'world_origin_actors': [],
            'part_origin_actors': [],
            'current_pos_left': None,
            'current_pos_right': None
        }
        
        try:
            # Record current_pos values
            if hasattr(self.step_viewer, 'current_pos_left'):
                positions['current_pos_left'] = dict(self.step_viewer.current_pos_left)
                self.debug_signal.emit(f"   current_pos_left: {positions['current_pos_left']}")
            
            if hasattr(self.step_viewer, 'current_pos_right'):
                positions['current_pos_right'] = dict(self.step_viewer.current_pos_right)
                self.debug_signal.emit(f"   current_pos_right: {positions['current_pos_right']}")
            
            # Record world origin actor positions (red markers)
            for renderer_name, renderer in [('left', self.step_viewer.vtk_renderer_left), 
                                          ('right', self.step_viewer.vtk_renderer_right)]:
                if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                    for i, actor in enumerate(renderer.origin_actors):
                        pos = actor.GetPosition()
                        positions['world_origin_actors'].append({
                            'renderer': renderer_name,
                            'actor_index': i,
                            'position': pos
                        })
                        self.debug_signal.emit(f"   {renderer_name} world origin actor {i}: {pos}")
            
            # Record part origin actor positions (green markers)
            for renderer_name, renderer in [('left', self.step_viewer.vtk_renderer_left), 
                                          ('right', self.step_viewer.vtk_renderer_right)]:
                part_actors = []
                if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                    part_actors.append(('sphere', renderer.part_origin_sphere))
                if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
                    part_actors.append(('x_arrow', renderer.part_origin_x_arrow))
                if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
                    part_actors.append(('y_arrow', renderer.part_origin_y_arrow))
                if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
                    part_actors.append(('z_arrow', renderer.part_origin_z_arrow))
                
                for actor_name, actor in part_actors:
                    pos = actor.GetPosition()
                    positions['part_origin_actors'].append({
                        'renderer': renderer_name,
                        'actor_name': actor_name,
                        'position': pos
                    })
                    self.debug_signal.emit(f"   {renderer_name} part origin {actor_name}: {pos}")
            
        except Exception as e:
            self.debug_signal.emit(f"✗ Error recording positions: {e}")
        
        return positions
    
    def test_left_button_rotation(self, axis, degrees):
        """Test left button rotation"""
        try:
            self.debug_signal.emit(f"   Calling rotate_shape('{axis}', {degrees})...")
            
            # Call the rotate_shape method directly
            self.step_viewer.rotate_shape(axis, degrees)
            
            # Allow time for rendering
            time.sleep(0.5)
            
            self.debug_signal.emit("✓ Left button rotation completed")
            
        except Exception as e:
            self.debug_signal.emit(f"✗ Error during left button rotation: {e}")
    
    def test_right_button_movement(self):
        """Test right button movement for comparison"""
        try:
            self.debug_signal.emit("   Testing right button movement (X+ 5mm)...")
            
            # Record positions before right button
            before_right = self.record_origin_positions("BEFORE RIGHT BUTTON")
            
            # Call move_shape method
            self.step_viewer.move_shape('x', 5.0)
            time.sleep(0.5)
            
            # Record positions after right button
            after_right = self.record_origin_positions("AFTER RIGHT BUTTON")
            
            # Compare
            self.analyze_position_changes(before_right, after_right, "RIGHT BUTTON")
            
        except Exception as e:
            self.debug_signal.emit(f"✗ Error during right button test: {e}")
    
    def analyze_position_changes(self, before, after, test_name="LEFT BUTTON"):
        """Analyze position changes between before and after"""
        self.debug_signal.emit(f"\n   ANALYSIS: {test_name} POSITION CHANGES")
        self.debug_signal.emit("   " + "-" * 50)
        
        # Check current_pos changes
        if before['current_pos_left'] and after['current_pos_left']:
            left_changed = before['current_pos_left'] != after['current_pos_left']
            self.debug_signal.emit(f"   current_pos_left changed: {left_changed}")
            if left_changed:
                self.debug_signal.emit(f"     Before: {before['current_pos_left']}")
                self.debug_signal.emit(f"     After:  {after['current_pos_left']}")
        
        # Check world origin actor movements
        world_moved = False
        for before_actor in before['world_origin_actors']:
            # Find corresponding after actor
            after_actor = None
            for a in after['world_origin_actors']:
                if (a['renderer'] == before_actor['renderer'] and 
                    a['actor_index'] == before_actor['actor_index']):
                    after_actor = a
                    break
            
            if after_actor:
                pos_changed = before_actor['position'] != after_actor['position']
                if pos_changed:
                    world_moved = True
                    self.debug_signal.emit(f"   World origin actor moved: {before_actor['renderer']} #{before_actor['actor_index']}")
                    self.debug_signal.emit(f"     Before: {before_actor['position']}")
                    self.debug_signal.emit(f"     After:  {after_actor['position']}")
        
        if not world_moved:
            self.debug_signal.emit("   ✗ World origin actors did NOT move")
        
        # Check part origin actor movements
        part_moved = False
        for before_actor in before['part_origin_actors']:
            # Find corresponding after actor
            after_actor = None
            for a in after['part_origin_actors']:
                if (a['renderer'] == before_actor['renderer'] and 
                    a['actor_name'] == before_actor['actor_name']):
                    after_actor = a
                    break
            
            if after_actor:
                pos_changed = before_actor['position'] != after_actor['position']
                if pos_changed:
                    part_moved = True
                    self.debug_signal.emit(f"   Part origin actor moved: {before_actor['renderer']} {before_actor['actor_name']}")
                    self.debug_signal.emit(f"     Before: {before_actor['position']}")
                    self.debug_signal.emit(f"     After:  {after_actor['position']}")
        
        if not part_moved:
            self.debug_signal.emit("   ✗ Part origin actors did NOT move")
        
        # Store results for final diagnosis
        self.test_results.append({
            'test_name': test_name,
            'current_pos_changed': left_changed if 'left_changed' in locals() else False,
            'world_actors_moved': world_moved,
            'part_actors_moved': part_moved
        })
    
    def provide_diagnosis(self):
        """Provide final diagnosis based on test results"""
        self.debug_signal.emit("\n" + "=" * 80)
        self.debug_signal.emit("FINAL DIAGNOSIS")
        self.debug_signal.emit("=" * 80)
        
        for result in self.test_results:
            self.debug_signal.emit(f"\n{result['test_name']} TEST RESULTS:")
            self.debug_signal.emit(f"  current_pos values updated: {result['current_pos_changed']}")
            self.debug_signal.emit(f"  World origin actors moved: {result['world_actors_moved']}")
            self.debug_signal.emit(f"  Part origin actors moved: {result['part_actors_moved']}")
        
        # Determine the issue
        left_result = next((r for r in self.test_results if 'LEFT' in r['test_name']), None)
        
        if left_result:
            if left_result['current_pos_changed'] and not left_result['world_actors_moved']:
                self.debug_signal.emit("\n🎯 ISSUE IDENTIFIED:")
                self.debug_signal.emit("   The current_pos values are being updated correctly,")
                self.debug_signal.emit("   but the visual origin actors are NOT being moved to")
                self.debug_signal.emit("   the new positions after rotation.")
                self.debug_signal.emit("\n💡 SOLUTION:")
                self.debug_signal.emit("   The rotate_shape() method needs to call SetPosition()")
                self.debug_signal.emit("   on the origin actors after rotation to move them to")
                self.debug_signal.emit("   the new transformed position.")
            elif not left_result['current_pos_changed']:
                self.debug_signal.emit("\n🎯 ISSUE IDENTIFIED:")
                self.debug_signal.emit("   The current_pos values are NOT being updated after rotation.")
                self.debug_signal.emit("   This means _update_origin_position_after_rotation() is not working.")
            else:
                self.debug_signal.emit("\n✓ LEFT BUTTON ORIGIN MOVEMENT APPEARS TO BE WORKING CORRECTLY")


class DebugWindow:
    """Simple debug output window"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        # Create debugger thread
        self.debugger = OriginMovementDebugger()
        self.debugger.debug_signal.connect(self.print_debug)
        
        print("Starting automated left button origin movement debug...")
        print("This will run completely automatically and show detailed results.")
        print("=" * 80)
        
        # Start debug thread
        self.debugger.start()
        
        # Keep the application running
        QTimer.singleShot(30000, self.app.quit)  # Auto-quit after 30 seconds
        
    def print_debug(self, message):
        """Print debug message to console"""
        print(message)
        
    def run(self):
        """Run the debug application"""
        return self.app.exec_()


if __name__ == "__main__":
    debug_window = DebugWindow()
    debug_window.run()
