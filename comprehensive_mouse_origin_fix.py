#!/usr/bin/env python3
"""
Comprehensive Mouse Origin Update Fix
This script will properly diagnose and fix the mouse origin update issue
without breaking existing mouse rotation functionality.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer import StepViewerTDK

class ComprehensiveMouseOriginFix:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_comprehensive_test(self):
        print("🔧 COMPREHENSIVE MOUSE ORIGIN UPDATE FIX")
        print("=" * 80)
        
        # Step 1: Create viewer and load file
        print("1. Creating viewer and loading STEP file...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.load_and_test)
        self.app.exec_()
        
    def load_and_test(self):
        # Load STEP file
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print("✅ STEP file loaded successfully")
        QTimer.singleShot(1000, self.diagnose_current_state)
        
    def diagnose_current_state(self):
        print("\n2. Diagnosing current mouse rotation behavior...")
        
        # Check VTK interactor style
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                style = self.viewer.vtk_renderer_left.interactor.GetInteractorStyle()
                style_name = style.__class__.__name__
                print(f"   Current VTK interactor style: {style_name}")
                
                # Check if it's the problematic TrackballActor style
                if 'TrackballActor' in style_name:
                    print("   ⚠️ PROBLEM FOUND: Using TrackballActor - this breaks normal mouse rotation!")
                    print("   🔧 FIXING: Reverting to TrackballCamera for proper mouse rotation")
                    self.fix_interactor_style()
                elif 'TrackballCamera' in style_name:
                    print("   ✅ Good: Using TrackballCamera - mouse rotation should work normally")
                else:
                    print(f"   ❓ Unknown style: {style_name}")
        
        QTimer.singleShot(1000, self.test_button_rotation)
        
    def fix_interactor_style(self):
        """Revert to proper TrackballCamera style for normal mouse rotation"""
        try:
            import vtk
            
            # Fix TOP viewer
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                    # Create proper camera-based rotation style
                    camera_style = vtk.vtkInteractorStyleTrackballCamera()
                    self.viewer.vtk_renderer_left.interactor.SetInteractorStyle(camera_style)
                    print("   ✅ Fixed TOP viewer interactor style")
            
            # Fix BOTTOM viewer
            if hasattr(self.viewer, 'vtk_renderer_right') and self.viewer.vtk_renderer_right:
                if hasattr(self.viewer.vtk_renderer_right, 'interactor'):
                    camera_style = vtk.vtkInteractorStyleTrackballCamera()
                    self.viewer.vtk_renderer_right.interactor.SetInteractorStyle(camera_style)
                    print("   ✅ Fixed BOTTOM viewer interactor style")
                    
        except Exception as e:
            print(f"   ❌ Error fixing interactor style: {e}")
    
    def test_button_rotation(self):
        print("\n3. Testing button rotation (should work and update origin)...")
        
        # Get initial origin position
        if hasattr(self.viewer, 'current_pos_left'):
            initial_pos = self.viewer.current_pos_left.copy()
            print(f"   Initial origin: {initial_pos}")
            
            # Apply button rotation
            print("   Applying 30° X rotation via button...")
            self.viewer.rotate_shape('x', 30)
            
            # Check if origin updated
            QTimer.singleShot(500, lambda: self.check_origin_update(initial_pos, "button rotation"))
        else:
            print("   ❌ No initial origin position found")
            QTimer.singleShot(1000, self.implement_proper_fix)
    
    def check_origin_update(self, initial_pos, test_type):
        if hasattr(self.viewer, 'current_pos_left'):
            updated_pos = self.viewer.current_pos_left
            print(f"   Updated origin after {test_type}: {updated_pos}")
            
            # Check if values changed
            x_changed = abs(updated_pos['x'] - initial_pos['x']) > 0.001
            y_changed = abs(updated_pos['y'] - initial_pos['y']) > 0.001
            z_changed = abs(updated_pos['z'] - initial_pos['z']) > 0.001
            
            if x_changed or y_changed or z_changed:
                print(f"   ✅ Origin values updated during {test_type}")
            else:
                print(f"   ❌ Origin values did NOT update during {test_type}")
        
        if test_type == "button rotation":
            QTimer.singleShot(1000, self.implement_proper_fix)
    
    def implement_proper_fix(self):
        print("\n4. Implementing PROPER mouse origin update fix...")
        print("   Strategy: Keep normal mouse rotation, add origin update to mouse handlers")
        
        # The fix should be: Add origin position update calls to existing mouse interaction handlers
        # WITHOUT changing the interactor style from TrackballCamera
        
        try:
            # Check if mouse interaction handlers exist
            if hasattr(self.viewer, 'on_mouse_interaction_left'):
                print("   ✅ Found existing mouse interaction handlers")
                
                # Store original handlers
                original_left_handler = self.viewer.on_mouse_interaction_left
                original_right_handler = self.viewer.on_mouse_interaction_right
                
                # Create enhanced handlers that also update origin
                def enhanced_left_handler(obj, event):
                    # Call original handler first
                    original_left_handler(obj, event)
                    
                    # Add origin update logic
                    self.update_origin_from_camera_rotation("top")
                
                def enhanced_right_handler(obj, event):
                    # Call original handler first  
                    original_right_handler(obj, event)
                    
                    # Add origin update logic
                    self.update_origin_from_camera_rotation("bottom")
                
                # Replace handlers
                self.viewer.on_mouse_interaction_left = enhanced_left_handler
                self.viewer.on_mouse_interaction_right = enhanced_right_handler
                
                print("   ✅ Enhanced mouse handlers with origin updates")
                
            else:
                print("   ❌ No mouse interaction handlers found")
                
        except Exception as e:
            print(f"   ❌ Error implementing fix: {e}")
        
        QTimer.singleShot(1000, self.test_mouse_rotation)
    
    def update_origin_from_camera_rotation(self, viewer):
        """Update origin position based on camera rotation (for mouse interactions)"""
        try:
            # This is a simplified approach - we need to track cumulative rotations
            # and apply them to the original origin position
            
            if viewer == "top":
                renderer = getattr(self.viewer, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'renderer'):
                    camera = renderer.renderer.GetActiveCamera()
                    if camera:
                        # Get camera orientation
                        orientation = camera.GetOrientation()
                        
                        # Update rotation tracking
                        self.viewer.current_rot_left = {
                            'x': orientation[0],
                            'y': orientation[1], 
                            'z': orientation[2]
                        }
                        
                        # For now, just trigger text update
                        # TODO: Implement proper origin transformation based on camera rotation
                        self.viewer.update_text_overlays()
                        
                        print(f"   🔧 Updated origin tracking for {viewer} viewer")
            
        except Exception as e:
            print(f"   ❌ Error updating origin from camera rotation: {e}")
    
    def test_mouse_rotation(self):
        print("\n5. Testing mouse rotation simulation...")
        print("   Note: This simulates what happens when user drags mouse")
        
        try:
            # Simulate camera rotation (what mouse dragging does)
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'renderer'):
                    camera = self.viewer.vtk_renderer_left.renderer.GetActiveCamera()
                    if camera:
                        print("   Simulating mouse drag rotation...")
                        
                        # Get initial camera position
                        initial_pos = camera.GetPosition()
                        initial_focal = camera.GetFocalPoint()
                        print(f"   Initial camera position: {initial_pos}")
                        
                        # Simulate rotation by changing camera position
                        camera.Azimuth(30)  # This is what mouse dragging does
                        camera.OrthogonalizeViewUp()
                        
                        # Get new camera position
                        new_pos = camera.GetPosition()
                        print(f"   New camera position: {new_pos}")
                        
                        # Trigger interaction event
                        if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                            self.viewer.vtk_renderer_left.interactor.InvokeEvent('InteractionEvent')
                        
                        # Force render
                        if hasattr(self.viewer.vtk_renderer_left, 'render_window'):
                            self.viewer.vtk_renderer_left.render_window.Render()
                        
                        print("   ✅ Mouse rotation simulation complete")
                        
        except Exception as e:
            print(f"   ❌ Error during mouse rotation test: {e}")
        
        QTimer.singleShot(2000, self.final_assessment)
    
    def final_assessment(self):
        print("\n6. FINAL ASSESSMENT")
        print("=" * 50)
        
        print("✅ PROPER SOLUTION IDENTIFIED:")
        print("   1. Keep VTK TrackballCamera style for normal mouse rotation")
        print("   2. Enhance mouse interaction handlers to update origin position")
        print("   3. Track camera rotations and apply to origin calculations")
        print("   4. Do NOT change interactor style to TrackballActor")
        
        print("\n🔧 NEXT STEPS:")
        print("   1. Revert VTK renderer to use TrackballCamera")
        print("   2. Implement proper origin tracking in mouse handlers")
        print("   3. Test that both mouse rotation AND origin updates work")
        
        print("\n" + "=" * 80)
        print("COMPREHENSIVE TEST COMPLETE - Ready to implement proper fix")
        
        QTimer.singleShot(3000, self.app.quit)

if __name__ == '__main__':
    test = ComprehensiveMouseOriginFix()
    test.run_comprehensive_test()
