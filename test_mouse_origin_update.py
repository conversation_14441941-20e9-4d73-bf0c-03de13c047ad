#!/usr/bin/env python3
"""
Test script to verify that origin values update during mouse rotation
This tests the fix for mouse rotation not updating origin position values
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import vtk

# Import the main viewer
from step_viewer import StepViewerTDK

class MouseOriginUpdateTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_test(self):
        print("🧪 TESTING MOUSE ORIGIN UPDATE FIX")
        print("=" * 60)
        
        # Create viewer
        print("1. Creating viewer...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Load STEP file
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the application
        self.app.exec_()
        
    def load_step_file(self):
        print("2. Loading STEP file...")
        
        # Load the test STEP file
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if os.path.exists(step_file):
            success = self.viewer.load_step_file_direct(step_file)
            if success:
                print("✅ STEP file loaded successfully")
                QTimer.singleShot(1000, self.check_initial_state)
            else:
                print("❌ Failed to load STEP file")
                self.app.quit()
        else:
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            
    def check_initial_state(self):
        print("\n3. Checking initial state...")
        
        # Check if we have origin position values
        if hasattr(self.viewer, 'current_pos_left'):
            self.initial_pos = self.viewer.current_pos_left.copy()
            print(f"   Initial origin position: {self.initial_pos}")
        else:
            print("   ❌ No initial origin position found")
            self.app.quit()
            return
            
        # Check VTK interactor style
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                style = self.viewer.vtk_renderer_left.interactor.GetInteractorStyle()
                style_name = style.__class__.__name__
                print(f"   VTK interactor style: {style_name}")
                
                if 'TrackballActor' in style_name:
                    print("   ✅ Using TrackballActor style - actors will rotate")
                elif 'TrackballCamera' in style_name:
                    print("   ⚠️ Using TrackballCamera style - only camera rotates")
                else:
                    print(f"   ❓ Unknown style: {style_name}")
        
        # Simulate mouse rotation
        QTimer.singleShot(2000, self.simulate_mouse_rotation)
        
    def simulate_mouse_rotation(self):
        print("\n4. Simulating mouse rotation...")
        
        try:
            # Get the VTK actor
            actor = None
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
                    actor = self.viewer.vtk_renderer_left.step_actor
                elif hasattr(self.viewer.vtk_renderer_left, 'step_actors') and self.viewer.vtk_renderer_left.step_actors:
                    actor = self.viewer.vtk_renderer_left.step_actors[0]
            
            if actor:
                print("   Found VTK actor")
                
                # Get initial orientation
                initial_orientation = actor.GetOrientation()
                print(f"   Initial actor orientation: {initial_orientation}")
                
                # Apply rotation directly to the actor (simulating mouse drag)
                print("   Applying 30° Y rotation to actor...")
                actor.RotateY(30.0)
                
                # Force a render
                if hasattr(self.viewer.vtk_renderer_left, 'render_window'):
                    self.viewer.vtk_renderer_left.render_window.Render()
                
                # Check new orientation
                new_orientation = actor.GetOrientation()
                print(f"   New actor orientation: {new_orientation}")
                
                # Trigger interaction event to update origin values
                print("   Triggering interaction event...")
                if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                    self.viewer.vtk_renderer_left.interactor.InvokeEvent('InteractionEvent')
                
                # Check if origin position changed
                QTimer.singleShot(500, self.check_origin_update)
                
            else:
                print("   ❌ No VTK actor found")
                self.app.quit()
                
        except Exception as e:
            print(f"   ❌ Error during simulation: {e}")
            self.app.quit()
            
    def check_origin_update(self):
        print("\n5. Checking if origin position updated...")
        
        if hasattr(self.viewer, 'current_pos_left'):
            updated_pos = self.viewer.current_pos_left
            print(f"   Updated origin position: {updated_pos}")
            
            # Check if values actually changed (allowing for small floating point differences)
            if hasattr(self, 'initial_pos'):
                x_changed = abs(updated_pos['x'] - self.initial_pos['x']) > 0.001
                y_changed = abs(updated_pos['y'] - self.initial_pos['y']) > 0.001
                z_changed = abs(updated_pos['z'] - self.initial_pos['z']) > 0.001
                
                print(f"   X changed: {'✅' if x_changed else '❌'}")
                print(f"   Y changed: {'✅' if y_changed else '❌'}")
                print(f"   Z changed: {'✅' if z_changed else '❌'}")
                
                if x_changed or y_changed or z_changed:
                    print("\n🎉 SUCCESS: Origin position values updated during mouse rotation!")
                else:
                    print("\n❌ FAILURE: Origin position values did not change")
            else:
                print("   ❌ No initial position to compare with")
        else:
            print("   ❌ No updated origin position found")
            
        # Test complete
        print("\n" + "=" * 60)
        print("MOUSE ORIGIN UPDATE TEST COMPLETE")
        QTimer.singleShot(2000, self.app.quit)

if __name__ == '__main__':
    test = MouseOriginUpdateTest()
    test.run_test()
