#!/usr/bin/env python3
"""
Force restart script to clear all Python module caches
"""

import sys
import os
import subprocess

def force_restart():
    """Force restart with cache clearing"""
    print("🔥🔥🔥 FORCE RESTART WITH CACHE CLEARING 🔥🔥🔥")
    
    # Clear Python cache
    print("🔧 Clearing Python cache...")
    if os.path.exists("__pycache__"):
        import shutil
        shutil.rmtree("__pycache__")
        print("✅ Cleared __pycache__ directory")
    
    # Clear .pyc files
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                os.remove(os.path.join(root, file))
                print(f"✅ Removed {file}")
    
    print("🔧 Starting fresh Python process...")
    
    # Start fresh Python process
    subprocess.run([sys.executable, "step_viewer_tdk_modular_fixed.py"])

if __name__ == "__main__":
    force_restart()
