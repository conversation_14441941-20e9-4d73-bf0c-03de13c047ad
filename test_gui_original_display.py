#!/usr/bin/env python3
"""
Test what the GUI displays for original values when loading a STEP file
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_gui_original_display():
    """Test GUI original value display"""
    print("🔧 Testing GUI original value display...")
    
    try:
        # Import the GUI class with proper encoding handling
        sys.path.insert(0, '.')
        
        # Try to import with error handling
        try:
            from step_viewer_tdk_modular_fixed import StepViewerTDK
            print("✅ Successfully imported StepViewerTDK from fixed version")
        except Exception as e:
            print(f"❌ Failed to import from fixed version: {e}")
            try:
                from step_viewer_tdk_modular import StepViewerTDK
                print("✅ Successfully imported StepViewerTDK from main version")
            except Exception as e2:
                print(f"❌ Failed to import from main version: {e2}")
                return False
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load the SOIC file (this is what happens when user clicks Load)
        print("\n🔧 Loading SOIC file...")
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the file
        success = viewer.load_step_file_left(step_file)
        
        if success:
            print("✅ STEP file loaded in GUI")
            
            # Check if step_loader_left exists and has the method
            if hasattr(viewer, 'step_loader_left'):
                print("✅ step_loader_left exists")
                
                if hasattr(viewer.step_loader_left, 'get_original_axis2_placement'):
                    print("✅ get_original_axis2_placement method exists")
                    
                    # Get the original AXIS2_PLACEMENT_3D data
                    axis_data = viewer.step_loader_left.get_original_axis2_placement()
                    
                    if axis_data:
                        print("✅ AXIS2_PLACEMENT_3D data retrieved from GUI:")
                        print(f"   Point: {axis_data.get('point', 'NOT FOUND')}")
                        print(f"   Dir1: {axis_data.get('dir1', 'NOT FOUND')}")
                        print(f"   Dir2: {axis_data.get('dir2', 'NOT FOUND')}")
                        
                        # Test the GUI text formatting
                        original_axis_text = f"""
Original top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                        print(f"\n🔧 GUI would display:{original_axis_text}")
                        
                        # Check if the GUI has the text overlay update method
                        if hasattr(viewer, 'update_text_overlays'):
                            print("✅ update_text_overlays method exists")
                            # Call it to see what happens
                            viewer.update_text_overlays()
                            print("✅ update_text_overlays called successfully")
                        else:
                            print("❌ update_text_overlays method not found")
                        
                        return True
                    else:
                        print("❌ No AXIS2_PLACEMENT_3D data retrieved from GUI")
                        return False
                else:
                    print("❌ get_original_axis2_placement method not found in step_loader_left")
                    return False
            else:
                print("❌ step_loader_left not found in GUI")
                return False
        else:
            print("❌ STEP file loading failed in GUI")
            return False
            
    except Exception as e:
        print(f"❌ Error testing GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== GUI ORIGINAL VALUE DISPLAY TEST ===")
    success = test_gui_original_display()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
