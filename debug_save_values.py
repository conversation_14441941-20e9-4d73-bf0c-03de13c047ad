#!/usr/bin/env python3
"""
Debug Save Values - Check what values are being used during save
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class SaveValueDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_debug(self):
        print("🔧 DEBUGGING SAVE VALUES")
        print("=" * 50)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_file)
        self.app.exec_()
        
    def step1_load_file(self):
        print("\n1. Loading STEP file...")
        
        success = self.viewer.load_step_file_direct('SOIC16P127_1270X940X610L89X51.STEP')
        if not success:
            print("❌ Failed to load file")
            self.app.quit()
            return
            
        print("✅ File loaded")
        QTimer.singleShot(1000, self.step2_apply_rotation)
        
    def step2_apply_rotation(self):
        print("\n2. Applying rotation...")
        
        # Apply some rotation to change the position
        self.viewer.rotate_shape('x', 45)
        
        QTimer.singleShot(1000, self.step3_check_values)
        
    def step3_check_values(self):
        print("\n3. Checking current values...")
        
        # Check what the display shows
        if hasattr(self.viewer, 'combined_text_actor_left'):
            try:
                display_text = self.viewer.combined_text_actor_left.GetInput()
                print(f"📺 DISPLAY TEXT: {display_text}")
            except:
                print("❌ Could not get display text")
        
        # Check internal variables
        print(f"🔧 INTERNAL VALUES:")
        if hasattr(self.viewer, 'current_pos_left'):
            print(f"   current_pos_left: {self.viewer.current_pos_left}")
        else:
            print("   current_pos_left: NOT SET")
            
        if hasattr(self.viewer, 'orig_pos_left'):
            print(f"   orig_pos_left: {self.viewer.orig_pos_left}")
        else:
            print("   orig_pos_left: NOT SET")
            
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"   model_rot_left: {self.viewer.model_rot_left}")
        else:
            print("   model_rot_left: NOT SET")
            
        # Check what save would use
        print(f"\n🎯 SAVE WOULD USE:")
        if self.viewer.active_viewer == "top":
            # Test the new extraction method
            current_pos = self.viewer._extract_position_from_display("top")
            orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}

            print(f"   Save current_pos (from display): {current_pos}")
            print(f"   Save orig_pos: {orig_pos}")

            # Calculate delta
            delta_pos = {
                'x': current_pos['x'] - orig_pos['x'],
                'y': current_pos['y'] - orig_pos['y'],
                'z': current_pos['z'] - orig_pos['z']
            }
            print(f"   Save delta_pos: {delta_pos}")
        
        print("\n" + "=" * 50)
        print("ANALYSIS COMPLETE")
        print("=" * 50)
        
        QTimer.singleShot(3000, self.app.quit)

if __name__ == '__main__':
    debugger = SaveValueDebugger()
    debugger.run_debug()
