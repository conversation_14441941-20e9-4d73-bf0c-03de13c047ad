#!/usr/bin/env python3
"""
Test to capture exact coordinates and rotations before and after button click
to see if the green origin markers are actually moving visually.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import <PERSON><PERSON>iewerTDK

def main():
    print("🔧 TESTING EXACT COORDINATES BEFORE/AFTER BUTTON CLICK")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    def capture_before_state():
        print("\n3. CAPTURING STATE BEFORE BUTTON CLICK")
        print("-" * 40)
        
        # Get renderer
        renderer = viewer.vtk_renderer_left
        
        # Capture green origin markers positions
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            sphere_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Green sphere position: {sphere_pos}")
            
            # Check if sphere has UserTransform
            sphere_transform = renderer.part_origin_sphere.GetUserTransform()
            if sphere_transform:
                sphere_matrix = sphere_transform.GetMatrix()
                print(f"   Green sphere UserTransform matrix:")
                for i in range(4):
                    row = [sphere_matrix.GetElement(i, j) for j in range(4)]
                    print(f"     {row}")
            else:
                print(f"   Green sphere UserTransform: None")
        else:
            print("   ❌ No green sphere found")
            
        # Capture green arrows positions
        if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
            x_arrow_pos = renderer.part_origin_x_arrow.GetPosition()
            print(f"   Green X arrow position: {x_arrow_pos}")
        else:
            print("   ❌ No green X arrow found")
            
        # Capture model actor position/transform
        if hasattr(viewer, 'step_actors_left') and viewer.step_actors_left:
            model_actor = viewer.step_actors_left[0]
            model_pos = model_actor.GetPosition()
            model_orient = model_actor.GetOrientation()
            print(f"   Model actor position: {model_pos}")
            print(f"   Model actor orientation: {model_orient}")
            
            # Check model UserTransform
            model_transform = model_actor.GetUserTransform()
            if model_transform:
                model_matrix = model_transform.GetMatrix()
                print(f"   Model UserTransform matrix:")
                for i in range(4):
                    row = [model_matrix.GetElement(i, j) for j in range(4)]
                    print(f"     {row}")
            else:
                print(f"   Model UserTransform: None")
        else:
            print("   ❌ No model actor found")
            
        # Capture yellow text values
        current_pos = viewer.current_pos_left
        print(f"   Yellow text Origin values: X={current_pos['x']:.6f}, Y={current_pos['y']:.6f}, Z={current_pos['z']:.6f}")
        
        # Now click the button
        print("\n4. CLICKING X+ BUTTON...")
        buttons = viewer.findChildren(QPushButton)
        x_plus_button = None
        for button in buttons:
            if button.text() == "X+":
                x_plus_button = button
                break
        
        if x_plus_button:
            x_plus_button.click()
            QTimer.singleShot(1000, capture_after_state)
        else:
            print("   ❌ Could not find X+ button")
    
    def capture_after_state():
        print("\n5. CAPTURING STATE AFTER BUTTON CLICK")
        print("-" * 40)
        
        # Get renderer
        renderer = viewer.vtk_renderer_left
        
        # Capture green origin markers positions
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            sphere_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Green sphere position: {sphere_pos}")
            
            # Check if sphere has UserTransform
            sphere_transform = renderer.part_origin_sphere.GetUserTransform()
            if sphere_transform:
                sphere_matrix = sphere_transform.GetMatrix()
                print(f"   Green sphere UserTransform matrix:")
                for i in range(4):
                    row = [sphere_matrix.GetElement(i, j) for j in range(4)]
                    print(f"     {row}")
            else:
                print(f"   Green sphere UserTransform: None")
        else:
            print("   ❌ No green sphere found")
            
        # Capture green arrows positions
        if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
            x_arrow_pos = renderer.part_origin_x_arrow.GetPosition()
            print(f"   Green X arrow position: {x_arrow_pos}")
        else:
            print("   ❌ No green X arrow found")
            
        # Capture model actor position/transform
        if hasattr(viewer, 'step_actors_left') and viewer.step_actors_left:
            model_actor = viewer.step_actors_left[0]
            model_pos = model_actor.GetPosition()
            model_orient = model_actor.GetOrientation()
            print(f"   Model actor position: {model_pos}")
            print(f"   Model actor orientation: {model_orient}")
            
            # Check model UserTransform
            model_transform = model_actor.GetUserTransform()
            if model_transform:
                model_matrix = model_transform.GetMatrix()
                print(f"   Model UserTransform matrix:")
                for i in range(4):
                    row = [model_matrix.GetElement(i, j) for j in range(4)]
                    print(f"     {row}")
            else:
                print(f"   Model UserTransform: None")
        else:
            print("   ❌ No model actor found")
            
        # Capture yellow text values
        current_pos = viewer.current_pos_left
        print(f"   Yellow text Origin values: X={current_pos['x']:.6f}, Y={current_pos['y']:.6f}, Z={current_pos['z']:.6f}")
        
        print("\n6. ANALYSIS:")
        print("-" * 40)
        print("   Compare the BEFORE and AFTER values above.")
        print("   If the green origin markers are working correctly:")
        print("   - Green sphere/arrow positions should CHANGE")
        print("   - Model actor should have a UserTransform matrix")
        print("   - Yellow text Origin values should CHANGE")
        print("   - Green markers should have moved to follow the rotated model")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(3000, capture_before_state)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will show exact coordinates before and after button click")
    print("   - Look for changes in green marker positions")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
