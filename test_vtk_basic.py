#!/usr/bin/env python3
"""
Test basic VTK functionality
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

def test_vtk_basic():
    """Test basic VTK functionality"""
    print("🔍 Testing basic VTK functionality...")
    
    try:
        # Import VTK
        import vtk
        print("✅ VTK imported successfully")
        
        # Import Qt VTK
        try:
            from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
            print("✅ QVTKRenderWindowInteractor imported successfully")
        except ImportError:
            from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
            print("✅ QVTKRenderWindowInteractor imported from fallback")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create main window
        window = QMainWindow()
        window.setWindowTitle("VTK Basic Test")
        window.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create VTK widget
        print("🔧 Creating VTK widget...")
        vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(vtk_widget)
        
        # Get render window and renderer
        render_window = vtk_widget.GetRenderWindow()
        renderer = vtk.vtkRenderer()
        
        # Set dark blue background
        renderer.SetBackground(0.1, 0.1, 0.2)  # Dark blue
        print("✅ Set dark blue background")
        
        # Add renderer to render window
        render_window.AddRenderer(renderer)
        
        # Create a simple sphere to test rendering
        sphere_source = vtk.vtkSphereSource()
        sphere_source.SetRadius(1.0)
        sphere_source.SetThetaResolution(30)
        sphere_source.SetPhiResolution(30)
        
        sphere_mapper = vtk.vtkPolyDataMapper()
        sphere_mapper.SetInputConnection(sphere_source.GetOutputPort())
        
        sphere_actor = vtk.vtkActor()
        sphere_actor.SetMapper(sphere_mapper)
        sphere_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red sphere
        
        renderer.AddActor(sphere_actor)
        print("✅ Added red sphere to renderer")
        
        # Get interactor
        interactor = render_window.GetInteractor()
        
        # Set interactor style
        style = vtk.vtkInteractorStyleTrackballCamera()
        interactor.SetInteractorStyle(style)
        print("✅ Set interactor style")
        
        # Initialize
        interactor.Initialize()
        print("✅ Interactor initialized")
        
        # Show window
        window.show()
        print("✅ Window shown")
        
        # Render
        render_window.Render()
        print("✅ Initial render complete")
        
        print("\n🎯 VTK test window should now be visible with:")
        print("   - Dark blue background")
        print("   - Red sphere in the center")
        print("   - Mouse interaction should work")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ VTK test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    sys.exit(test_vtk_basic())
