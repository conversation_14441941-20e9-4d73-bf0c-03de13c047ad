#!/usr/bin/env python3
"""
Verify STEP File Coordinates - Complete verification of save/load process
"""

import sys
import os
import re
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class StepFileCoordinateVerifier:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'test_coordinate_verification.STEP'
        self.top_display_values = None
        
    def run_verification(self):
        print("🔧 VERIFYING STEP FILE COORDINATE PRESERVATION")
        print("=" * 60)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_and_transform)
        self.app.exec_()
        
    def step1_load_and_transform(self):
        print("\n1. Loading and transforming model...")
        
        # Load file
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        
        # Apply transformation
        self.viewer.rotate_shape('x', 45)
        print("✅ Applied 45° X rotation")
        
        QTimer.singleShot(1000, self.step2_capture_top_display)
        
    def step2_capture_top_display(self):
        print("\n2. Capturing TOP display values...")
        
        # Get the actual display text from TOP viewer
        try:
            display_text = self.viewer.combined_text_actor_left.GetInput()
            print(f"📺 TOP DISPLAY TEXT: {display_text}")
            
            # Extract Origin values
            origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
            match = re.search(origin_pattern, display_text)
            
            if match:
                self.top_display_values = {
                    'x': float(match.group(1)),
                    'y': float(match.group(2)),
                    'z': float(match.group(3))
                }
                print(f"✅ CAPTURED TOP ORIGIN: {self.top_display_values}")
            else:
                print("❌ Could not parse TOP origin values")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Error capturing TOP display: {e}")
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.step3_save_file)
        
    def step3_save_file(self):
        print("\n3. Saving the transformed model...")

        # Remove existing test file
        if os.path.exists(self.saved_file):
            os.remove(self.saved_file)

        # Use the actual save method directly without dialog
        try:
            # Simulate clicking the save button
            self.viewer.active_viewer = "top"  # Ensure we're saving from TOP

            # Get all the required parameters for the save method
            loader = self.viewer.step_loader_left
            current_pos = self.viewer._extract_position_from_display("top")
            current_rot = self.viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            orig_rot = self.viewer.orig_rot_left if hasattr(self.viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}

            print(f"🔧 SAVE PARAMETERS:")
            print(f"   current_pos (from display): {current_pos}")
            print(f"   current_rot: {current_rot}")
            print(f"   orig_pos: {orig_pos}")
            print(f"   orig_rot: {orig_rot}")

            # Call the internal save method directly
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )

            if success:
                print(f"✅ File saved successfully to {self.saved_file}")
                QTimer.singleShot(1000, self.step4_examine_step_file)
            else:
                print("❌ Save method failed")
                self.app.quit()

        except Exception as e:
            print(f"❌ Error during save: {e}")
            import traceback
            traceback.print_exc()
            self.app.quit()
            
    def step4_examine_step_file(self):
        print("\n4. Examining the saved STEP file content...")
        
        # Check if file was created (might have different name due to dialog)
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') and 'test' in f.lower()]
        
        if not step_files:
            print("❌ No test STEP file found. Save dialog may have been cancelled.")
            print("Available STEP files:")
            all_step_files = [f for f in os.listdir('.') if f.endswith('.STEP')]
            for f in all_step_files:
                print(f"   {f}")
            self.app.quit()
            return
            
        # Use the most recently created test file
        test_file = max(step_files, key=os.path.getctime)
        print(f"📁 Examining saved file: {test_file}")
        
        try:
            with open(test_file, 'r') as f:
                content = f.read()
                
            # Look for AXIS2_PLACEMENT_3D entries
            axis_pattern = r'#\d+\s*=\s*AXIS2_PLACEMENT_3D\s*\([^)]+\)\s*;'
            axis_matches = re.findall(axis_pattern, content)
            
            print(f"🔧 Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries:")
            for i, match in enumerate(axis_matches[:3]):  # Show first 3
                print(f"   {i+1}: {match}")
                
            # Look for CARTESIAN_POINT entries
            point_pattern = r'#\d+\s*=\s*CARTESIAN_POINT\s*\([^)]+\)\s*;'
            point_matches = re.findall(point_pattern, content)
            
            print(f"🔧 Found {len(point_matches)} CARTESIAN_POINT entries:")
            for i, match in enumerate(point_matches[:5]):  # Show first 5
                print(f"   {i+1}: {match}")
                
            # Look for coordinate values that match our display values
            self.check_coordinate_matches(content)
            
            self.saved_file = test_file  # Update for next step
            QTimer.singleShot(1000, self.step5_load_and_verify)
            
        except Exception as e:
            print(f"❌ Error examining STEP file: {e}")
            self.app.quit()
            
    def check_coordinate_matches(self, content):
        """Check if the STEP file contains coordinates matching our display values"""
        print(f"\n🔍 SEARCHING FOR COORDINATES MATCHING DISPLAY VALUES:")
        print(f"   Looking for X≈{self.top_display_values['x']:.3f}")
        print(f"   Looking for Y≈{self.top_display_values['y']:.3f}")
        print(f"   Looking for Z≈{self.top_display_values['z']:.3f}")
        
        # Extract all numeric values from CARTESIAN_POINT entries
        point_pattern = r'CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d.E]+)\s*,\s*([-\d.E]+)\s*,\s*([-\d.E]+)\s*\)\s*\)'
        matches = re.findall(point_pattern, content)
        
        tolerance = 0.1  # Allow small differences
        found_matches = []
        
        for match in matches:
            x, y, z = float(match[0]), float(match[1]), float(match[2])
            
            # Check if this point is close to our display values
            if (abs(x - self.top_display_values['x']) < tolerance and
                abs(y - self.top_display_values['y']) < tolerance and
                abs(z - self.top_display_values['z']) < tolerance):
                found_matches.append((x, y, z))
                
        if found_matches:
            print(f"✅ FOUND {len(found_matches)} MATCHING COORDINATES:")
            for match in found_matches:
                print(f"   ({match[0]:.3f}, {match[1]:.3f}, {match[2]:.3f})")
        else:
            print(f"❌ NO MATCHING COORDINATES FOUND IN STEP FILE")
            print(f"   This indicates the save process is not preserving display values")
            
    def step5_load_and_verify(self):
        print("\n5. Loading saved file and verifying BOTTOM display...")
        
        # Reset viewer
        self.viewer.reset_to_original()
        
        QTimer.singleShot(1000, self.step5b_load_saved)
        
    def step5b_load_saved(self):
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        
        if not success:
            print(f"❌ Failed to load saved file {self.saved_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded saved file {self.saved_file}")
        QTimer.singleShot(1000, self.step6_verify_bottom_display)
        
    def step6_verify_bottom_display(self):
        print("\n6. Verifying BOTTOM display values...")
        
        try:
            # Get BOTTOM display text
            display_text = self.viewer.combined_text_actor_right.GetInput()
            print(f"📺 BOTTOM DISPLAY TEXT: {display_text}")
            
            # Extract Origin values
            origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
            match = re.search(origin_pattern, display_text)
            
            if match:
                bottom_values = {
                    'x': float(match.group(1)),
                    'y': float(match.group(2)),
                    'z': float(match.group(3))
                }
                print(f"✅ CAPTURED BOTTOM ORIGIN: {bottom_values}")
                
                # Compare with original TOP values
                print(f"\n📊 COMPARISON:")
                print(f"   TOP (before save):    {self.top_display_values}")
                print(f"   BOTTOM (after load):  {bottom_values}")
                
                tolerance = 0.01
                matches = (
                    abs(self.top_display_values['x'] - bottom_values['x']) < tolerance and
                    abs(self.top_display_values['y'] - bottom_values['y']) < tolerance and
                    abs(self.top_display_values['z'] - bottom_values['z']) < tolerance
                )
                
                if matches:
                    print(f"✅ SUCCESS: Values match within tolerance!")
                    print(f"✅ Save functionality is working correctly")
                else:
                    print(f"❌ FAILURE: Values do not match")
                    print(f"❌ Save functionality has issues")
                    
            else:
                print("❌ Could not parse BOTTOM origin values")
                
        except Exception as e:
            print(f"❌ Error verifying BOTTOM display: {e}")
            
        QTimer.singleShot(3000, self.cleanup_and_exit)
        
    def cleanup_and_exit(self):
        print("\n" + "=" * 60)
        print("COORDINATE VERIFICATION COMPLETE")
        print("=" * 60)
        
        self.app.quit()

if __name__ == '__main__':
    verifier = StepFileCoordinateVerifier()
    verifier.run_verification()
