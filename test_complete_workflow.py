#!/usr/bin/env python3
"""
Test the complete workflow: Start GUI -> Load SOIC file -> Check Original TOP values
This simulates exactly what happens when you use the GUI
"""

import sys
import os

def test_complete_workflow():
    """Test the complete workflow that happens in the GUI"""
    print("🔧 Testing complete GUI workflow...")
    
    try:
        # Import the actual GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Set active viewer to TOP (this is what happens when you click TOP button)
        viewer.active_viewer = "top"
        print("✅ Active viewer set to TOP")
        
        # Load the SOIC file using the correct method
        print("\n🔧 Loading SOIC file into TOP viewer...")
        success = viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded successfully in GUI")
            
            # Now check what the text overlay shows
            print("\n🔧 Checking text overlay content...")
            
            # Set up some mock cursor position (this happens when mouse moves)
            viewer.cursor_pos_left = {'x': 0.10, 'y': 0.05, 'z': -0.00}
            
            # Call update_text_overlays (this happens automatically in GUI)
            print("🔧 Calling update_text_overlays...")
            viewer.update_text_overlays()
            
            # Check what text was actually set
            if hasattr(viewer, 'cursor_text_actor_left'):
                actual_text = viewer.cursor_text_actor_left.GetInput()
                print(f"\n🔧 ACTUAL TEXT IN GUI:")
                print(f"'{actual_text}'")
                
                # Check if it contains the correct values
                if "Point: (-4.19, -3.6673, 0.4914)" in actual_text:
                    print("✅ SUCCESS: Text contains correct STEP file values!")
                    print("✅ The Original TOP values are now correct!")
                    return True
                elif "(-4.190000000000000, -3.667300000000000, 0.491400000000000)" in actual_text:
                    print("❌ FAILURE: Text still contains OLD HARDCODED values!")
                    return False
                elif "No coordinate system data found" in actual_text:
                    print("⚠️  WARNING: No AXIS2_PLACEMENT_3D data found - file might not be loaded properly")
                    
                    # Let's check if the step_loader actually has the data
                    if hasattr(viewer, 'step_loader_left') and hasattr(viewer.step_loader_left, 'get_original_axis2_placement'):
                        axis_data = viewer.step_loader_left.get_original_axis2_placement()
                        print(f"🔧 Direct check of step_loader data: {axis_data}")
                        if axis_data:
                            print("❌ FAILURE: Data exists but not being used in GUI!")
                        else:
                            print("❌ FAILURE: No data in step_loader!")
                    return False
                else:
                    print("⚠️  UNKNOWN: Text contains different values")
                    print(f"Looking for: 'Point: (-4.19, -3.6673, 0.4914)'")
                    return False
            else:
                print("❌ cursor_text_actor_left not found")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run complete workflow test"""
    print("🔥 COMPLETE WORKFLOW TEST 🔥")
    print("=" * 50)
    print("This test simulates:")
    print("1. Starting the GUI")
    print("2. Setting TOP viewer as active")
    print("3. Loading SOIC16P127_1270X940X610L89X51.STEP file")
    print("4. Checking if Original TOP values are correct")
    print("=" * 50)
    
    result = test_complete_workflow()
    
    print("\n" + "=" * 50)
    print("WORKFLOW TEST RESULT:")
    print("=" * 50)
    
    if result:
        print("🎉 SUCCESS: Original TOP values are now correct!")
        print("✅ The fix is working - STEP file data is being displayed")
        print("✅ No more hardcoded values")
        print("\nWhen you run the actual GUI and load the SOIC file,")
        print("you should see the correct Original TOP values.")
    else:
        print("❌ FAILURE: Original TOP values are still wrong")
        print("⚠️  The fix is not working properly")
        print("⚠️  More debugging needed")

if __name__ == "__main__":
    main()
