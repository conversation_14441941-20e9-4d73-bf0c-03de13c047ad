#!/usr/bin/env python3
"""
Final verification that both hardcoded issues are fixed:
1. Original top AXIS2_PLACEMENT_3D data comes from STEP file
2. Angle/axis rotation values are not hardcoded to 9° and (0,0,1)
"""

import sys
import os

def test_no_hardcoded_values_remain():
    """Test that no hardcoded values remain in the update_text_overlays method"""
    print("🔧 Testing for remaining hardcoded values...")
    
    try:
        with open('step_viewer_tdk_modular_fixed.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for all the old hardcoded patterns
        hardcoded_patterns = [
            # Old AXIS2_PLACEMENT_3D hardcoded values
            "(-4.190000000000000, -3.667300000000000, 0.491400000000000)",
            "(0.000000000000000, 0.910400000000000, -0.413800000000000)",
            "(0.000000000000000, 0.413800000000000, 0.910400000000000)",
            "DIRECT OVERRIDE: Forcing correct Original top values",
            
            # Old angle/axis hardcoded values
            "display_angle = 9.0",
            "display_axis_x = 0.00",
            "display_axis_y = 0.00", 
            "display_axis_z = 1.00",
            "USER REQUESTED: Angle should be 9° and axis should be (0,0,1)",
            "show corrected values as requested by user"
        ]
        
        found_hardcoded = []
        for pattern in hardcoded_patterns:
            if pattern in content:
                found_hardcoded.append(pattern)
        
        if found_hardcoded:
            print("❌ Found remaining hardcoded values:")
            for pattern in found_hardcoded:
                print(f"   - {pattern}")
            return False
        else:
            print("✅ No hardcoded values found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

def test_correct_logic_exists():
    """Test that the correct logic exists in the update_text_overlays method"""
    print("\n🔧 Testing for correct logic...")
    
    try:
        with open('step_viewer_tdk_modular_fixed.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the correct patterns
        correct_patterns = [
            # Correct AXIS2_PLACEMENT_3D logic
            "get_original_axis2_placement",
            "USING REAL STEP FILE: Getting actual Original top values from STEP file",
            "axis_data['point']",
            "axis_data['dir1']",
            "axis_data['dir2']",
            
            # Correct angle/axis logic
            "math.sqrt(self.current_rot_left['x']**2 + self.current_rot_left['y']**2 + self.current_rot_left['z']**2)",
            "display_axis_x = self.current_rot_left['x']",
            "display_axis_y = self.current_rot_left['y']",
            "display_axis_z = self.current_rot_left['z']",
            "USING REAL ROTATION VALUES"
        ]
        
        missing_patterns = []
        for pattern in correct_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print("❌ Missing correct logic patterns:")
            for pattern in missing_patterns:
                print(f"   - {pattern}")
            return False
        else:
            print("✅ All correct logic patterns found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

def test_step_file_integration():
    """Test that the STEP file integration works end-to-end"""
    print("\n🔧 Testing STEP file integration...")
    
    try:
        from step_loader import STEPLoader
        
        # Load the SOIC file
        loader = STEPLoader()
        success, message = loader.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Test AXIS2_PLACEMENT_3D extraction
            axis_data = loader.get_original_axis2_placement()
            if axis_data:
                print("✅ AXIS2_PLACEMENT_3D data extracted:")
                print(f"   Point: {axis_data['point']}")
                print(f"   Dir1: {axis_data['dir1']}")
                print(f"   Dir2: {axis_data['dir2']}")
                
                # Test the formatting that will be used in GUI
                original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                
                print(f"\n🔧 GUI will display:\n{original_axis_text}")
                
                # Verify these are NOT the old hardcoded values
                old_point = "(-4.190000000000000, -3.667300000000000, 0.491400000000000)"
                if str(axis_data['point']) != old_point:
                    print("✅ Point values are NOT hardcoded (good!)")
                else:
                    print("⚠️  Point values match old hardcoded format (but this might be coincidental)")
                
                return True
            else:
                print("❌ No AXIS2_PLACEMENT_3D data found")
                return False
        else:
            print(f"❌ STEP file loading failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing STEP integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run final verification tests"""
    print("🔥 FINAL FIX VERIFICATION 🔥")
    print("=" * 50)
    
    # Test 1: No hardcoded values remain
    print("1. CHECKING FOR HARDCODED VALUES")
    print("-" * 40)
    result1 = test_no_hardcoded_values_remain()
    
    # Test 2: Correct logic exists
    print("\n2. CHECKING FOR CORRECT LOGIC")
    print("-" * 40)
    result2 = test_correct_logic_exists()
    
    # Test 3: STEP file integration works
    print("\n3. TESTING STEP FILE INTEGRATION")
    print("-" * 40)
    result3 = test_step_file_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("FINAL VERIFICATION RESULTS:")
    print("=" * 50)
    print(f"No Hardcoded Values: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Correct Logic Exists: {'✅ PASS' if result2 else '❌ FAIL'}")
    print(f"STEP Integration: {'✅ PASS' if result3 else '❌ FAIL'}")
    
    if result1 and result2 and result3:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\nThe GUI should now show:")
        print("- Real AXIS2_PLACEMENT_3D data from STEP file (not hardcoded)")
        print("- Real rotation angles from model (not hardcoded to 9°)")
        print("- Real axis values from model (not hardcoded to (0,0,1))")
        print("\n✅ READY TO TEST IN GUI!")
    else:
        print("\n⚠️  SOME ISSUES REMAIN - Need further fixes")
    
    return result1 and result2 and result3

if __name__ == "__main__":
    main()
