#!/usr/bin/env python3
"""
Debug Direction Swap - Find why Direction and REF. Direction are swapped
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class DirectionSwapDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'debug_direction_swap.STEP'
        
    def run_debug(self):
        print("🔧 DEBUGGING DIRECTION SWAP ISSUE")
        print("=" * 60)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_and_check_original)
        self.app.exec_()
        
    def step1_load_and_check_original(self):
        print("\n1. Loading original file and checking STEP coordinate system...")
        
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        
        # Get original STEP file axis data
        if hasattr(self.viewer.step_loader_left, 'get_original_axis2_placement'):
            axis_data = self.viewer.step_loader_left.get_original_axis2_placement()
            if axis_data:
                print(f"📊 ORIGINAL STEP FILE AXIS2_PLACEMENT_3D:")
                print(f"   Point: {axis_data.get('point', 'Not found')}")
                print(f"   Dir1:  {axis_data.get('dir1', 'Not found')}")
                print(f"   Dir2:  {axis_data.get('dir2', 'Not found')}")
                
                # Store for comparison
                self.original_axis_data = axis_data
            else:
                print("❌ No AXIS2_PLACEMENT_3D data found in original file")
                self.app.quit()
                return
        
        # Get display values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        print(f"📺 ORIGINAL DISPLAY: {display_text}")
        
        QTimer.singleShot(1000, self.step2_transform_and_debug)
        
    def step2_transform_and_debug(self):
        print("\n2. Applying transformation and debugging coordinate calculation...")
        
        # Apply transformation
        self.viewer.rotate_shape('x', 45)
        print("✅ Applied 45° X rotation")
        
        # Get transformed display values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        print(f"📺 TRANSFORMED DISPLAY: {display_text}")
        
        # Debug the transformation calculation in detail
        print(f"\n🔧 DEBUGGING TRANSFORMATION CALCULATION:")

        # Get the VTK transformation matrix from multi-actors
        user_transform = None
        if hasattr(self.viewer, 'multi_actors_left') and self.viewer.multi_actors_left:
            # Check first multi-actor for transform
            for actor in self.viewer.multi_actors_left:
                if actor.GetUserTransform():
                    user_transform = actor.GetUserTransform()
                    print(f"✅ Found VTK UserTransform from multi-actor")
                    break
        elif hasattr(self.viewer, 'step_actor_left') and self.viewer.step_actor_left:
            user_transform = self.viewer.step_actor_left.GetUserTransform()
            if user_transform:
                print(f"✅ Found VTK UserTransform from single actor")

        if user_transform:
            # Get the rotation matrix
            rotation_matrix = user_transform.GetMatrix()
            print(f"🔧 VTK Rotation Matrix:")
            for i in range(4):
                row = [rotation_matrix.GetElement(i, j) for j in range(4)]
                print(f"   [{row[0]:8.6f} {row[1]:8.6f} {row[2]:8.6f} {row[3]:8.6f}]")

            # Get original direction vectors
            orig_dir1 = self.original_axis_data['dir1']
            orig_dir2 = self.original_axis_data['dir2']

            print(f"\n🔧 ORIGINAL DIRECTION VECTORS:")
            print(f"   Dir1 (should be Direction): {orig_dir1}")
            print(f"   Dir2 (should be REF. Direction): {orig_dir2}")

            # Transform dir1 manually
            import vtk
            dir1_vec = [orig_dir1[0], orig_dir1[1], orig_dir1[2], 0.0]
            transformed_dir1 = [0.0, 0.0, 0.0, 0.0]
            rotation_matrix.MultiplyPoint(dir1_vec, transformed_dir1)

            # Transform dir2 manually
            dir2_vec = [orig_dir2[0], orig_dir2[1], orig_dir2[2], 0.0]
            transformed_dir2 = [0.0, 0.0, 0.0, 0.0]
            rotation_matrix.MultiplyPoint(dir2_vec, transformed_dir2)

            print(f"\n🔧 MANUALLY CALCULATED TRANSFORMED VECTORS:")
            print(f"   Transformed Dir1: [{transformed_dir1[0]:.6f}, {transformed_dir1[1]:.6f}, {transformed_dir1[2]:.6f}]")
            print(f"   Transformed Dir2: [{transformed_dir2[0]:.6f}, {transformed_dir2[1]:.6f}, {transformed_dir2[2]:.6f}]")
                
            # Extract Direction values from display
            import re
            direction_pattern = r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
            ref_direction_pattern = r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'

            dir_match = re.search(direction_pattern, display_text)
            ref_match = re.search(ref_direction_pattern, display_text)

            if dir_match and ref_match:
                display_dir = [float(dir_match.group(1)), float(dir_match.group(2)), float(dir_match.group(3))]
                display_ref = [float(ref_match.group(1)), float(ref_match.group(2)), float(ref_match.group(3))]

                print(f"\n🔧 DISPLAY VALUES:")
                print(f"   Display Direction:     [{display_dir[0]:.6f}, {display_dir[1]:.6f}, {display_dir[2]:.6f}]")
                print(f"   Display REF. Direction: [{display_ref[0]:.6f}, {display_ref[1]:.6f}, {display_ref[2]:.6f}]")

                print(f"\n🔧 COMPARISON:")
                print(f"   Transformed Dir1 vs Display Direction:")
                print(f"     Dir1: [{transformed_dir1[0]:.6f}, {transformed_dir1[1]:.6f}, {transformed_dir1[2]:.6f}]")
                print(f"     Disp: [{display_dir[0]:.6f}, {display_dir[1]:.6f}, {display_dir[2]:.6f}]")
                print(f"     Match: {abs(transformed_dir1[0] - display_dir[0]) < 0.01 and abs(transformed_dir1[1] - display_dir[1]) < 0.01 and abs(transformed_dir1[2] - display_dir[2]) < 0.01}")

                print(f"   Transformed Dir2 vs Display REF. Direction:")
                print(f"     Dir2: [{transformed_dir2[0]:.6f}, {transformed_dir2[1]:.6f}, {transformed_dir2[2]:.6f}]")
                print(f"     Disp: [{display_ref[0]:.6f}, {display_ref[1]:.6f}, {display_ref[2]:.6f}]")
                print(f"     Match: {abs(transformed_dir2[0] - display_ref[0]) < 0.01 and abs(transformed_dir2[1] - display_ref[1]) < 0.01 and abs(transformed_dir2[2] - display_ref[2]) < 0.01}")

                # Check if they're swapped
                print(f"\n🔧 SWAP CHECK:")
                print(f"   Transformed Dir1 vs Display REF. Direction:")
                print(f"     Dir1: [{transformed_dir1[0]:.6f}, {transformed_dir1[1]:.6f}, {transformed_dir1[2]:.6f}]")
                print(f"     Disp: [{display_ref[0]:.6f}, {display_ref[1]:.6f}, {display_ref[2]:.6f}]")
                print(f"     Match: {abs(transformed_dir1[0] - display_ref[0]) < 0.01 and abs(transformed_dir1[1] - display_ref[1]) < 0.01 and abs(transformed_dir1[2] - display_ref[2]) < 0.01}")

                print(f"   Transformed Dir2 vs Display Direction:")
                print(f"     Dir2: [{transformed_dir2[0]:.6f}, {transformed_dir2[1]:.6f}, {transformed_dir2[2]:.6f}]")
                print(f"     Disp: [{display_dir[0]:.6f}, {display_dir[1]:.6f}, {display_dir[2]:.6f}]")
                print(f"     Match: {abs(transformed_dir2[0] - display_dir[0]) < 0.01 and abs(transformed_dir2[1] - display_dir[1]) < 0.01 and abs(transformed_dir2[2] - display_dir[2]) < 0.01}")

                # Store for save comparison
                self.expected_dir1 = transformed_dir1[:3]
                self.expected_dir2 = transformed_dir2[:3]
                self.display_dir = display_dir
                self.display_ref = display_ref
                
        else:
            print("❌ No UserTransform found in any actor")
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.step3_save_and_check)
        
    def step3_save_and_check(self):
        print("\n3. Saving file and checking what gets written...")
        
        # Save using Option 1
        try:
            success = self.viewer.save_step_file_option1(self.saved_file)
            if success:
                print(f"✅ Saved to {self.saved_file}")
            else:
                print("❌ Save failed")
                self.app.quit()
                return
        except Exception as e:
            print(f"❌ Save error: {e}")
            self.app.quit()
            return
            
        # Load the saved file and check its axis data
        QTimer.singleShot(1000, self.step4_load_saved_and_compare)
        
    def step4_load_saved_and_compare(self):
        print("\n4. Loading saved file and comparing Direction values...")
        
        # Set active viewer to bottom
        self.viewer.active_viewer = "bottom"
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        
        if not success:
            print(f"❌ Failed to load {self.saved_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.saved_file} into BOTTOM viewer")
        
        # Get saved file axis data
        if hasattr(self.viewer.step_loader_right, 'get_original_axis2_placement'):
            saved_axis_data = self.viewer.step_loader_right.get_original_axis2_placement()
            if saved_axis_data:
                print(f"📊 SAVED STEP FILE AXIS2_PLACEMENT_3D:")
                print(f"   Point: {saved_axis_data.get('point', 'Not found')}")
                print(f"   Dir1:  {saved_axis_data.get('dir1', 'Not found')}")
                print(f"   Dir2:  {saved_axis_data.get('dir2', 'Not found')}")
                
                saved_dir1 = saved_axis_data['dir1']
                saved_dir2 = saved_axis_data['dir2']
                
                print(f"\n🔧 FINAL COMPARISON:")
                print(f"   Expected Dir1 (from transform): [{self.expected_dir1[0]:.6f}, {self.expected_dir1[1]:.6f}, {self.expected_dir1[2]:.6f}]")
                print(f"   Saved Dir1:                     [{saved_dir1[0]:.6f}, {saved_dir1[1]:.6f}, {saved_dir1[2]:.6f}]")
                print(f"   Match: {abs(self.expected_dir1[0] - saved_dir1[0]) < 0.01 and abs(self.expected_dir1[1] - saved_dir1[1]) < 0.01 and abs(self.expected_dir1[2] - saved_dir1[2]) < 0.01}")
                
                print(f"   Expected Dir2 (from transform): [{self.expected_dir2[0]:.6f}, {self.expected_dir2[1]:.6f}, {self.expected_dir2[2]:.6f}]")
                print(f"   Saved Dir2:                     [{saved_dir2[0]:.6f}, {saved_dir2[1]:.6f}, {saved_dir2[2]:.6f}]")
                print(f"   Match: {abs(self.expected_dir2[0] - saved_dir2[0]) < 0.01 and abs(self.expected_dir2[1] - saved_dir2[1]) < 0.01 and abs(self.expected_dir2[2] - saved_dir2[2]) < 0.01}")
                
        # Get display values from both viewers
        top_display = self.viewer.combined_text_actor_left.GetInput()
        bottom_display = self.viewer.combined_text_actor_right.GetInput()
        
        print(f"\n📺 FINAL DISPLAY COMPARISON:")
        print(f"   TOP:    {top_display}")
        print(f"   BOTTOM: {bottom_display}")
        
        QTimer.singleShot(3000, self.cleanup_and_exit)
        
    def cleanup_and_exit(self):
        print("\n" + "=" * 60)
        print("DIRECTION SWAP DEBUG COMPLETE")
        print("=" * 60)
        
        self.app.quit()

if __name__ == '__main__':
    debugger = DirectionSwapDebugger()
    debugger.run_debug()
