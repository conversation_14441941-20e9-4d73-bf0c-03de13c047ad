#!/usr/bin/env python3
"""
Test to verify that GUI labels are being updated with correct Dir1/Dir2 values
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_gui_labels_update():
    """Test GUI labels update with STEP file"""
    print("🔧 Testing GUI labels update...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the SOIC file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file: {step_file}")
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Check the AXIS2_PLACEMENT_3D data directly
            if hasattr(viewer, 'step_loader_left'):
                axis_data = viewer.step_loader_left.get_original_axis2_placement()
                if axis_data:
                    print("\n✅ AXIS2_PLACEMENT_3D data from step_loader:")
                    print(f"   Point: {axis_data['point']}")
                    print(f"   Dir1: {axis_data['dir1']}")
                    print(f"   Dir2: {axis_data['dir2']}")
                    
                    # Test the update_original_top_labels method directly
                    print("\n🔧 Testing update_original_top_labels method...")
                    viewer.update_original_top_labels(axis_data)
                    
                    # Check if the GUI labels were updated
                    print("\n🔧 Checking GUI label values...")
                    
                    # Check Direction labels (Dir1)
                    if hasattr(viewer, 'lbl_orig_axis_x'):
                        dir_x_text = viewer.lbl_orig_axis_x.text()
                        dir_y_text = viewer.lbl_orig_axis_y.text()
                        dir_z_text = viewer.lbl_orig_axis_z.text()
                        print(f"✅ Direction labels (Dir1): {dir_x_text}, {dir_y_text}, {dir_z_text}")
                    else:
                        print("❌ Direction labels (lbl_orig_axis_*) not found")
                    
                    # Check REF. Direction labels (Dir2)
                    if hasattr(viewer, 'lbl_orig_ref_x'):
                        ref_x_text = viewer.lbl_orig_ref_x.text()
                        ref_y_text = viewer.lbl_orig_ref_y.text()
                        ref_z_text = viewer.lbl_orig_ref_z.text()
                        print(f"✅ REF. Direction labels (Dir2): {ref_x_text}, {ref_y_text}, {ref_z_text}")
                    else:
                        print("❌ REF. Direction labels (lbl_orig_ref_*) not found")
                    
                    # Check cursor text overlay (should NOT contain Original top data)
                    if hasattr(viewer, 'cursor_text_actor_left'):
                        cursor_text = viewer.cursor_text_actor_left.GetInput()
                        print(f"\n🔧 Cursor text overlay: {repr(cursor_text)}")
                        if "Original top:" in cursor_text:
                            print("❌ ERROR: Cursor overlay still contains 'Original top:' data!")
                        else:
                            print("✅ GOOD: Cursor overlay only shows cursor position")
                    
                    return True
                else:
                    print("❌ No AXIS2_PLACEMENT_3D data found")
                    return False
            else:
                print("❌ step_loader_left not found")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== GUI LABELS UPDATE TEST ===")
    success = test_gui_labels_update()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
