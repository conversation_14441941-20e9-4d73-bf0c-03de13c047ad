#!/usr/bin/env python3
"""
Test cursor text restoration after clear_view() during STEP file loading
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_cursor_clear_view_fix():
    """Test cursor text restoration after clear_view() removes all actors"""
    print("🔧 Testing cursor text restoration after clear_view()...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Check INITIAL cursor text actor state
        print("\n🔧 Checking INITIAL cursor text actor state...")
        if hasattr(viewer, 'cursor_text_actor_left'):
            visibility_initial = viewer.cursor_text_actor_left.GetVisibility()
            print(f"TOP cursor initial visibility: {visibility_initial}")
            
            # Check if actor is in renderer
            renderer = viewer.vtk_renderer_left.renderer
            if renderer:
                actors = renderer.GetActors2D()
                actor_count = actors.GetNumberOfItems()
                print(f"TOP renderer has {actor_count} 2D actors initially")
                
                # Check if cursor actor is in the renderer
                actors.InitTraversal()
                cursor_found = False
                for i in range(actor_count):
                    actor = actors.GetNextItem()
                    if actor == viewer.cursor_text_actor_left:
                        cursor_found = True
                        break
                
                if cursor_found:
                    print("✅ TOP cursor text actor found in renderer initially")
                else:
                    print("❌ TOP cursor text actor NOT found in renderer initially")
        else:
            print("❌ TOP cursor text actor not found")
        
        # Load STEP file to trigger clear_view()
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file (this will trigger clear_view()): {step_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Check cursor text actor state AFTER loading (after clear_view())
            print("\n🔧 Checking cursor text actor state AFTER loading...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility_after = viewer.cursor_text_actor_left.GetVisibility()
                input_text = viewer.cursor_text_actor_left.GetInput()
                font_size = viewer.cursor_text_actor_left.GetTextProperty().GetFontSize()
                layer = viewer.cursor_text_actor_left.GetLayerNumber()
                shadow = viewer.cursor_text_actor_left.GetTextProperty().GetShadow()
                
                print(f"TOP cursor visibility AFTER loading: {visibility_after} (should be 1)")
                print(f"TOP cursor text: {repr(input_text)}")
                print(f"TOP cursor font size: {font_size} (should be 16)")
                print(f"TOP cursor layer: {layer} (should be 10)")
                print(f"TOP cursor shadow: {shadow} (should be 1)")
                
                # Check if actor is still in renderer after clear_view()
                renderer = viewer.vtk_renderer_left.renderer
                if renderer:
                    actors = renderer.GetActors2D()
                    actor_count = actors.GetNumberOfItems()
                    print(f"TOP renderer has {actor_count} 2D actors after loading")
                    
                    # Check if cursor actor is in the renderer
                    actors.InitTraversal()
                    cursor_found = False
                    for i in range(actor_count):
                        actor = actors.GetNextItem()
                        if actor == viewer.cursor_text_actor_left:
                            cursor_found = True
                            break
                    
                    if cursor_found:
                        print("✅ CRITICAL SUCCESS: TOP cursor text actor found in renderer after clear_view()")
                    else:
                        print("❌ CRITICAL FAILURE: TOP cursor text actor NOT found in renderer after clear_view()")
                
                # Verify all enhanced properties
                if visibility_after == 1:
                    print("✅ GOOD: TOP cursor visible after loading")
                else:
                    print("❌ PROBLEM: TOP cursor hidden after loading")
                    
                if font_size == 16:
                    print("✅ GOOD: Font size enhanced to 16")
                else:
                    print(f"❌ PROBLEM: Font size is {font_size}, should be 16")
                    
                if layer == 10:
                    print("✅ GOOD: Layer set to 10 (top layer)")
                else:
                    print(f"❌ PROBLEM: Layer is {layer}, should be 10")
                    
                if shadow == 1:
                    print("✅ GOOD: Shadow enabled")
                else:
                    print("❌ PROBLEM: Shadow not enabled")
                    
                # Test text overlay update to see if cursor remains visible
                print("\n🔧 Testing text overlay update after loading...")
                viewer.update_text_overlays()
                
                visibility_after_update = viewer.cursor_text_actor_left.GetVisibility()
                print(f"TOP cursor visibility after text overlay update: {visibility_after_update}")
                
                if visibility_after_update == 1:
                    print("✅ EXCELLENT: Cursor remains visible after text overlay update")
                else:
                    print("❌ PROBLEM: Cursor becomes hidden after text overlay update")
                    
            else:
                print("❌ TOP cursor text actor not found after loading")
            
            return True
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== CURSOR CLEAR_VIEW FIX TEST ===")
    success = test_cursor_clear_view_fix()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
