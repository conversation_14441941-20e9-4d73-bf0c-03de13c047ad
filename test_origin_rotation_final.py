#!/usr/bin/env python3
"""
Test to verify that green origin markers rotate with the model when using rotation buttons.
This test will capture the exact positions before and after rotation to verify movement.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON><PERSON>

def test_origin_rotation():
    """Test that origin markers rotate with the model"""
    print("=" * 60)
    print("TESTING: Green origin markers rotation with model")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    viewer.show()
    
    # Wait for viewer to initialize
    QTimer.singleShot(500, lambda: run_rotation_test(viewer, app))
    
    app.exec_()

def run_rotation_test(viewer, app):
    """Run the actual rotation test"""
    try:
        print("\n1. Loading test STEP file...")
        
        # Load a STEP file (assuming there's one in the directory)
        test_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.stp')]
        if not test_files:
            print("ERROR: No STEP files found in current directory")
            app.quit()
            return
            
        test_file = test_files[0]
        print(f"   Loading: {test_file}")
        
        # Load the file
        viewer.load_step_file_direct(test_file)
        
        # Wait for loading to complete
        QTimer.singleShot(1000, lambda: perform_rotation_test(viewer, app))
        
    except Exception as e:
        print(f"ERROR in test setup: {e}")
        app.quit()

def perform_rotation_test(viewer, app):
    """Perform the actual rotation test"""
    try:
        print("\n2. Testing rotation button functionality...")
        
        # Check if green origin markers exist
        renderer = viewer.vtk_renderer_left
        if not hasattr(renderer, 'part_origin_sphere') or not renderer.part_origin_sphere:
            print("ERROR: No green origin sphere found!")
            app.quit()
            return
            
        print("   ✅ Green origin sphere found")
        
        # Get initial position
        initial_pos = renderer.part_origin_sphere.GetPosition()
        print(f"   Initial green sphere position: ({initial_pos[0]:.3f}, {initial_pos[1]:.3f}, {initial_pos[2]:.3f})")
        
        # Test X+ rotation (15 degrees)
        print("\n3. Testing X+ rotation (15 degrees)...")
        viewer.rotate_shape('x', 15)
        
        # Get position after rotation
        after_pos = renderer.part_origin_sphere.GetPosition()
        print(f"   After X+ rotation position: ({after_pos[0]:.3f}, {after_pos[1]:.3f}, {after_pos[2]:.3f})")
        
        # Calculate movement
        movement = [after_pos[i] - initial_pos[i] for i in range(3)]
        total_movement = sum(abs(m) for m in movement)
        
        print(f"   Movement vector: ({movement[0]:.3f}, {movement[1]:.3f}, {movement[2]:.3f})")
        print(f"   Total movement distance: {total_movement:.3f}")
        
        # Test result
        if total_movement > 0.001:  # More than 1mm movement
            print("   ✅ SUCCESS: Green origin markers moved with rotation!")
            print(f"   ✅ Movement detected: {total_movement:.3f} units")
        else:
            print("   ❌ FAILURE: Green origin markers did not move")
            print("   ❌ The rotation buttons are not moving the origin markers")
        
        # Test another rotation to confirm
        print("\n4. Testing Y+ rotation (15 degrees)...")
        prev_pos = after_pos
        viewer.rotate_shape('y', 15)
        
        final_pos = renderer.part_origin_sphere.GetPosition()
        movement2 = [final_pos[i] - prev_pos[i] for i in range(3)]
        total_movement2 = sum(abs(m) for m in movement2)
        
        print(f"   After Y+ rotation position: ({final_pos[0]:.3f}, {final_pos[1]:.3f}, {final_pos[2]:.3f})")
        print(f"   Second movement: {total_movement2:.3f} units")
        
        # Final result
        print("\n" + "=" * 60)
        if total_movement > 0.001 and total_movement2 > 0.001:
            print("🎉 FINAL RESULT: SUCCESS!")
            print("✅ Green origin markers are rotating with the model")
            print("✅ Both X and Y rotations move the markers correctly")
        elif total_movement > 0.001 or total_movement2 > 0.001:
            print("⚠️  FINAL RESULT: PARTIAL SUCCESS")
            print("✅ Some rotation works, but not all axes")
        else:
            print("❌ FINAL RESULT: FAILURE")
            print("❌ Green origin markers are NOT rotating with the model")
            print("❌ The fix is not working correctly")
        print("=" * 60)
        
        # Close after 3 seconds
        QTimer.singleShot(3000, app.quit)
        
    except Exception as e:
        print(f"ERROR in rotation test: {e}")
        app.quit()

if __name__ == "__main__":
    test_origin_rotation()
