#!/usr/bin/env python3
"""
Force the VTK interactor style to Trackball<PERSON><PERSON> for mouse rotation text updates
This will directly set the interactor style to ensure mouse rotations affect the actor
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import vtk

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class InteractorStyleForcer:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def start_test(self):
        """Start the test and force interactor style"""
        print("🔧 FORCING VTK INTERACTOR STYLE TO TRACKBALL ACTOR")
        print("=" * 55)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize, then force the style
        QTimer.singleShot(3000, self.force_interactor_style)
        
        return self.app.exec_()
        
    def force_interactor_style(self):
        """Force the interactor style to TrackballActor"""
        print("🔧 Forcing interactor style...")
        
        try:
            # Load a test file first
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                return
                
            # Wait a bit more for model to load
            QTimer.singleShot(2000, self.apply_style_fix)
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            
    def apply_style_fix(self):
        """Apply the interactor style fix"""
        print("\n🔧 Applying interactor style fix...")
        
        try:
            # Get the TOP viewer interactor
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                interactor = self.viewer.vtk_renderer_left.interactor
                if interactor:
                    print(f"Current style: {interactor.GetInteractorStyle().__class__.__name__}")
                    
                    # Create a simple TrackballActor style
                    style = vtk.vtkInteractorStyleTrackballActor()
                    
                    # Set it directly
                    interactor.SetInteractorStyle(style)
                    print("✅ Set interactor style to vtkInteractorStyleTrackballActor")
                    
                    # Verify it was set
                    new_style = interactor.GetInteractorStyle()
                    print(f"New style: {new_style.__class__.__name__}")
                    
                    # Add observer for interaction events to trigger text updates
                    def on_interaction(obj, event):
                        print(f"🖱️  Interaction event: {event}")
                        # Trigger text update
                        if hasattr(self.viewer, 'update_text_overlays'):
                            self.viewer.update_text_overlays()
                    
                    # Add observers for various interaction events
                    style.AddObserver("InteractionEvent", on_interaction)
                    style.AddObserver("EndInteractionEvent", on_interaction)
                    
                    print("✅ Added interaction observers for text updates")
                    
                else:
                    print("❌ No interactor found")
            else:
                print("❌ No VTK renderer found")
                
            # Also fix the BOTTOM viewer if it exists
            if hasattr(self.viewer, 'vtk_renderer_right') and self.viewer.vtk_renderer_right:
                interactor_right = self.viewer.vtk_renderer_right.interactor
                if interactor_right:
                    style_right = vtk.vtkInteractorStyleTrackballActor()
                    interactor_right.SetInteractorStyle(style_right)
                    print("✅ Set BOTTOM viewer style to TrackballActor")
                    
        except Exception as e:
            print(f"❌ Error applying style fix: {e}")
            
        # Start monitoring
        QTimer.singleShot(2000, self.start_monitoring)
        
    def start_monitoring(self):
        """Start monitoring for changes"""
        print("\n🖱️  MANUAL TEST:")
        print("1. Use your mouse to drag and rotate the model in the TOP viewer")
        print("2. The model should rotate (not the camera)")
        print("3. Watch the text overlay - it should update during rotation")
        print("4. Press Ctrl+C to exit when done testing")
        print()
        
        # Set up a timer to periodically check for rotation changes
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_rotation_changes)
        self.monitor_timer.start(1000)  # Check every second
        
        self.last_rotation = None
        
    def check_rotation_changes(self):
        """Check if rotation values have changed"""
        try:
            if hasattr(self.viewer, 'current_rot_left'):
                current = self.viewer.current_rot_left
                
                if self.last_rotation is None:
                    self.last_rotation = current.copy()
                    print(f"Initial rotation: X={current['x']:.1f}° Y={current['y']:.1f}° Z={current['z']:.1f}°")
                else:
                    # Check for changes
                    x_change = abs(current['x'] - self.last_rotation['x'])
                    y_change = abs(current['y'] - self.last_rotation['y'])
                    z_change = abs(current['z'] - self.last_rotation['z'])
                    
                    total_change = x_change + y_change + z_change
                    
                    if total_change > 0.5:  # Significant change
                        print(f"🔄 Rotation changed: X={current['x']:.1f}° Y={current['y']:.1f}° Z={current['z']:.1f}° (Δ{total_change:.1f}°)")
                        self.last_rotation = current.copy()
                        
        except Exception as e:
            # Don't spam errors
            pass

if __name__ == "__main__":
    forcer = InteractorStyleForcer()
    try:
        sys.exit(forcer.start_test())
    except KeyboardInterrupt:
        print("\n👋 Test completed by user")
        sys.exit(0)
