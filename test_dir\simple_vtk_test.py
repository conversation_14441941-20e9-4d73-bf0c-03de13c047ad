#!/usr/bin/env python3
"""
Simple VTK test - completely isolated
"""

import sys
import vtk
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
    print("✅ VTK Qt imported successfully")
except ImportError:
    print("❌ VTK Qt import failed")
    sys.exit(1)

class SimpleVTKTest(QMainWindow):
    def __init__(self):
        super().__init__()
        print("🎯 Simple VTK Test Starting...")
        
        self.setWindowTitle("Simple VTK Mouse Test")
        self.setGeometry(200, 200, 600, 500)
        
        # Central widget
        central = QWidget()
        self.setCentralWidget(central)
        layout = QVBoxLayout(central)
        
        # Status label
        self.status = QLabel("Click Load Cube, then drag mouse to rotate")
        layout.addWidget(self.status)
        
        # Rotation display
        self.rotation_display = QLabel("Rotation: X=0.00 Y=0.00 Z=0.00")
        layout.addWidget(self.rotation_display)
        
        # Load button
        btn = QPushButton("Load Test Cube")
        btn.clicked.connect(self.load_cube)
        layout.addWidget(btn)
        
        # VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # VTK setup
        self.renderer = vtk.vtkRenderer()
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        self.interactor = self.render_window.GetInteractor()
        
        # Actor storage
        self.cube_actor = None
        
        # Custom interaction style
        self.setup_interaction()
        
        # Update timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_rotation_display)
        self.timer.start(200)  # 200ms updates
        
        # Initialize
        self.interactor.Initialize()
        print("✅ VTK initialized")
        
    def setup_interaction(self):
        """Setup custom mouse interaction"""
        
        class CustomStyle(vtk.vtkInteractorStyleTrackballCamera):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.dragging = False
                self.last_pos = None
                
            def OnLeftButtonDown(self):
                super().OnLeftButtonDown()
                self.dragging = True
                interactor = self.GetInteractor()
                if interactor:
                    self.last_pos = interactor.GetEventPosition()
                print(f"🖱️ Mouse down: {self.last_pos}")
                
            def OnLeftButtonUp(self):
                super().OnLeftButtonUp()
                self.dragging = False
                print("🖱️ Mouse up")
                
            def OnMouseMove(self):
                if self.dragging and self.last_pos and self.parent.cube_actor:
                    interactor = self.GetInteractor()
                    if interactor:
                        current_pos = interactor.GetEventPosition()
                        dx = current_pos[0] - self.last_pos[0]
                        dy = current_pos[1] - self.last_pos[1]
                        
                        if abs(dx) > 2 or abs(dy) > 2:
                            # Rotate the cube actor directly
                            if dx != 0:
                                self.parent.cube_actor.RotateWXYZ(dx * 0.5, 0, 1, 0)
                            if dy != 0:
                                self.parent.cube_actor.RotateWXYZ(dy * 0.5, 1, 0, 0)
                            
                            self.parent.render_window.Render()
                            self.last_pos = current_pos
                            print(f"🔧 Rotated cube: dx={dx}, dy={dy}")
                else:
                    super().OnMouseMove()
        
        style = CustomStyle(self)
        self.interactor.SetInteractorStyle(style)
        print("✅ Custom interaction style set")
        
    def load_cube(self):
        """Load a test cube"""
        # Create cube
        cube = vtk.vtkCubeSource()
        cube.SetXLength(1.0)
        cube.SetYLength(1.0) 
        cube.SetZLength(1.0)
        
        # Mapper
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cube.GetOutputPort())
        
        # Actor
        self.cube_actor = vtk.vtkActor()
        self.cube_actor.SetMapper(mapper)
        self.cube_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red
        
        # Add to scene
        self.renderer.AddActor(self.cube_actor)
        self.renderer.ResetCamera()
        self.render_window.Render()
        
        self.status.setText("Cube loaded! Drag mouse to rotate")
        print("✅ Cube loaded")
        
    def update_rotation_display(self):
        """Update rotation display from actor"""
        if self.cube_actor:
            orientation = self.cube_actor.GetOrientation()
            self.rotation_display.setText(f"Rotation: X={orientation[0]:.2f} Y={orientation[1]:.2f} Z={orientation[2]:.2f}")
            
            # Print non-zero rotations
            if abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1:
                print(f"📊 Current rotation: X={orientation[0]:.2f} Y={orientation[1]:.2f} Z={orientation[2]:.2f}")

def main():
    print("🚀 Starting Simple VTK Test")
    
    app = QApplication(sys.argv)
    window = SimpleVTKTest()
    window.show()
    
    print("📋 Test Instructions:")
    print("  1. Click 'Load Test Cube'")
    print("  2. Drag mouse to rotate cube")
    print("  3. Watch rotation values update")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
