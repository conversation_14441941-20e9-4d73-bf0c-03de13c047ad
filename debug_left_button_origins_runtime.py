#!/usr/bin/env python3
"""
Debug script to test LEFT button origin rotation in real-time
This will load a STEP file, create origins, and test the left buttons
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import <PERSON><PERSON><PERSON><PERSON>

def debug_left_button_origins():
    """Debug LEFT button origin rotation in real-time"""
    print("🔥🔥🔥 DEBUGGING LEFT BUTTON ORIGIN ROTATION 🔥🔥🔥")
    print("This will test if LEFT buttons actually rotate origin markers")
    
    try:
        # Import the step viewer
        from step_viewer import StepViewerTDK
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer instance
        print("✅ Creating StepViewerTDK instance...")
        viewer = StepViewerTDK()
        viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(2000, lambda: test_with_step_file(viewer))
        
        # Start the application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ FAIL: {e}")
        return False

def test_with_step_file(viewer):
    """Test with a STEP file loaded"""
    print("\n🔧 TESTING WITH STEP FILE...")
    
    try:
        # Load the STEP file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if os.path.exists(step_file):
            print(f"✅ Loading STEP file: {step_file}")
            
            # Load file in left viewer
            viewer.active_viewer = "top"
            success = viewer.load_step_file(step_file)
            
            if success:
                print("✅ STEP file loaded successfully")
                
                # Wait a bit for loading to complete
                QTimer.singleShot(1000, lambda: create_origins_and_test(viewer))
            else:
                print("❌ Failed to load STEP file")
        else:
            print(f"❌ STEP file not found: {step_file}")
            
    except Exception as e:
        print(f"❌ Error loading STEP file: {e}")

def create_origins_and_test(viewer):
    """Create origin overlay and test rotation"""
    print("\n🎯 CREATING ORIGIN OVERLAY AND TESTING...")
    
    try:
        # Create origin overlay in left viewer
        if hasattr(viewer.vtk_renderer_left, 'create_origin_overlay'):
            print("✅ Creating origin overlay...")
            success = viewer.vtk_renderer_left.create_origin_overlay()
            
            if success:
                print("✅ Origin overlay created")
                
                # Check if origin actors exist
                if hasattr(viewer.vtk_renderer_left, 'origin_actors'):
                    origin_count = len(viewer.vtk_renderer_left.origin_actors)
                    print(f"✅ Found {origin_count} origin actors")
                    
                    if origin_count > 0:
                        # Test left button rotation
                        QTimer.singleShot(1000, lambda: test_left_button_rotation(viewer))
                    else:
                        print("❌ No origin actors found")
                else:
                    print("❌ origin_actors attribute not found")
            else:
                print("❌ Failed to create origin overlay")
        else:
            print("❌ create_origin_overlay method not found")
            
    except Exception as e:
        print(f"❌ Error creating origins: {e}")

def test_left_button_rotation(viewer):
    """Test LEFT button rotation"""
    print("\n🔧 TESTING LEFT BUTTON ROTATION...")
    
    try:
        # Record initial origin positions
        initial_positions = []
        if hasattr(viewer.vtk_renderer_left, 'origin_actors'):
            for i, actor in enumerate(viewer.vtk_renderer_left.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orientation = actor.GetOrientation()
                    initial_positions.append({
                        'index': i,
                        'position': pos,
                        'orientation': orientation
                    })
                    print(f"   Origin {i}: pos={pos}, orient={orientation}")
        
        print(f"✅ Recorded {len(initial_positions)} initial origin positions")
        
        # Test X+ rotation (15 degrees)
        print("\n🔧 Testing X+ rotation (15 degrees)...")
        viewer.active_viewer = "top"
        
        # Call rotate_shape directly
        print("   Calling viewer.rotate_shape('x', 15)...")
        viewer.rotate_shape('x', 15)
        
        # Wait a bit for rotation to complete
        QTimer.singleShot(500, lambda: check_rotation_results(viewer, initial_positions))
        
    except Exception as e:
        print(f"❌ Error testing rotation: {e}")

def check_rotation_results(viewer, initial_positions):
    """Check if origins actually rotated"""
    print("\n📊 CHECKING ROTATION RESULTS...")
    
    try:
        # Record new positions
        new_positions = []
        if hasattr(viewer.vtk_renderer_left, 'origin_actors'):
            for i, actor in enumerate(viewer.vtk_renderer_left.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orientation = actor.GetOrientation()
                    new_positions.append({
                        'index': i,
                        'position': pos,
                        'orientation': orientation
                    })
                    print(f"   Origin {i}: pos={pos}, orient={orientation}")
        
        # Compare positions
        origins_moved = False
        origins_rotated = False
        
        for i, (initial, new) in enumerate(zip(initial_positions, new_positions)):
            # Check position change
            pos_changed = (
                abs(initial['position'][0] - new['position'][0]) > 0.001 or
                abs(initial['position'][1] - new['position'][1]) > 0.001 or
                abs(initial['position'][2] - new['position'][2]) > 0.001
            )
            
            # Check orientation change
            orient_changed = (
                abs(initial['orientation'][0] - new['orientation'][0]) > 0.001 or
                abs(initial['orientation'][1] - new['orientation'][1]) > 0.001 or
                abs(initial['orientation'][2] - new['orientation'][2]) > 0.001
            )
            
            if pos_changed:
                origins_moved = True
                print(f"   ✅ Origin {i} POSITION changed")
            
            if orient_changed:
                origins_rotated = True
                print(f"   ✅ Origin {i} ORIENTATION changed")
        
        # Final results
        print("\n" + "="*50)
        print("FINAL RESULTS:")
        print("="*50)
        
        if origins_rotated:
            print("✅ SUCCESS: Origins ROTATED with left button!")
        elif origins_moved:
            print("⚠️  PARTIAL: Origins moved but didn't rotate properly")
        else:
            print("❌ FAIL: Origins did NOT move or rotate")
            
        print("\n🔍 DEBUGGING INFO:")
        print(f"   - Active viewer: {viewer.active_viewer}")
        print(f"   - Origin actors count: {len(viewer.vtk_renderer_left.origin_actors) if hasattr(viewer.vtk_renderer_left, 'origin_actors') else 'N/A'}")
        
        # Check if rotate_shape method was actually called
        if hasattr(viewer, 'current_rot_left'):
            print(f"   - Current rotation: {viewer.current_rot_left}")
        
    except Exception as e:
        print(f"❌ Error checking results: {e}")

if __name__ == "__main__":
    debug_left_button_origins()
