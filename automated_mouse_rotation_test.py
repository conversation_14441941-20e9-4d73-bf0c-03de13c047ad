#!/usr/bin/env python3
"""
Fully Automated Mouse Rotation Test Program
This will automatically test mouse rotation and report what's working/broken
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class AutomatedMouseRotationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = []
        self.initial_orientations = {}
        self.final_orientations = {}
        
    def log(self, message):
        """Log test results"""
        print(f"[TEST] {message}")
        self.test_results.append(message)
        
    def run_automated_test(self):
        """Run the complete automated test"""
        self.log("🚀 STARTING AUTOMATED MOUSE ROTATION TEST")
        self.log("=" * 50)
        
        try:
            # Step 1: Start viewer
            self.log("Step 1: Starting 3D STEP Viewer...")
            self.viewer = StepViewerTDK()
            self.viewer.show()
            
            # Process events to ensure GUI is ready
            self.app.processEvents()
            QTest.qWait(2000)  # Wait 2 seconds
            
            # Step 2: Load test file
            self.log("Step 2: Loading test STEP file...")
            test_file = self.find_test_file()
            if test_file:
                self.viewer.load_step_file_direct(test_file)
                self.log(f"✅ Loaded: {test_file}")
                QTest.qWait(3000)  # Wait for loading
            else:
                self.log("❌ No test file found, creating dummy model...")
                self.create_dummy_model()
                
            # Step 3: Enable overlays
            self.log("Step 3: Enabling overlays...")
            self.enable_overlays()
            QTest.qWait(1000)
            
            # Step 4: Analyze initial state
            self.log("Step 4: Analyzing initial state...")
            self.analyze_initial_state()
            
            # Step 5: Record initial orientations
            self.log("Step 5: Recording initial orientations...")
            self.record_initial_orientations()
            
            # Step 6: Simulate mouse rotation
            self.log("Step 6: Simulating mouse rotation...")
            self.simulate_mouse_rotation()
            QTest.qWait(1000)
            
            # Step 7: Record final orientations
            self.log("Step 7: Recording final orientations...")
            self.record_final_orientations()
            
            # Step 8: Analyze results
            self.log("Step 8: Analyzing test results...")
            self.analyze_test_results()
            
            # Step 9: Generate report
            self.log("Step 9: Generating final report...")
            self.generate_final_report()
            
        except Exception as e:
            self.log(f"❌ TEST FAILED: {e}")
            import traceback
            self.log(f"Traceback: {traceback.format_exc()}")
            
        finally:
            # Keep viewer open for manual inspection
            self.log("\n🔍 VIEWER REMAINS OPEN FOR MANUAL INSPECTION")
            self.log("You can now manually test mouse rotation to verify results")
            
        return self.app.exec_()
        
    def find_test_file(self):
        """Find a test STEP file"""
        test_files = [
            'test.step', 
            'SOIC16P127_1270X940X610L89X51.STEP',
            'sample.step',
            'example.step'
        ]
        
        for filename in test_files:
            if os.path.exists(filename):
                return filename
        return None
        
    def create_dummy_model(self):
        """Create a dummy model for testing if no STEP file available"""
        try:
            # This would create a simple test model
            # For now, we'll just note that no file was available
            self.log("⚠️  No STEP file available - test will be limited")
        except Exception as e:
            self.log(f"❌ Error creating dummy model: {e}")
            
    def enable_overlays(self):
        """Enable origin overlays"""
        try:
            # Enable world origin overlay
            if hasattr(self.viewer.vtk_renderer_left, 'create_origin_overlay'):
                self.viewer.vtk_renderer_left.create_origin_overlay()
                self.log("✅ World origin overlay enabled")
                
            # Enable part origin overlay if possible
            if (hasattr(self.viewer, 'orig_pos_left') and 
                hasattr(self.viewer.vtk_renderer_left, 'create_part_origin_overlay')):
                self.viewer.vtk_renderer_left.create_part_origin_overlay(
                    self.viewer.orig_pos_left.get('x', 0),
                    self.viewer.orig_pos_left.get('y', 0),
                    self.viewer.orig_pos_left.get('z', 0)
                )
                self.log("✅ Part origin overlay enabled")
                
            # Enable bounding box
            if hasattr(self.viewer, 'toggle_bounding_box_left'):
                self.viewer.toggle_bounding_box_left()
                self.log("✅ Bounding box enabled")
                
        except Exception as e:
            self.log(f"⚠️  Error enabling overlays: {e}")
            
    def analyze_initial_state(self):
        """Analyze the initial state of the viewer"""
        try:
            left_renderer = self.viewer.vtk_renderer_left
            
            # Check interactor style
            if hasattr(left_renderer, 'interactor') and left_renderer.interactor:
                style = left_renderer.interactor.GetInteractorStyle()
                style_name = style.__class__.__name__
                self.log(f"🖱️  Interactor Style: {style_name}")
                
                if "SafeModelRotationStyle" in style_name:
                    self.log("✅ Custom SafeModelRotationStyle detected")
                elif "TrackballActor" in style_name:
                    self.log("✅ TrackballActor style detected")
                elif "TrackballCamera" in style_name:
                    self.log("⚠️  TrackballCamera style (rotates camera, not model)")
                else:
                    self.log(f"❓ Unknown style: {style_name}")
            else:
                self.log("❌ No interactor found")
                
            # Count actors
            model_count = 0
            if hasattr(left_renderer, 'step_actors') and left_renderer.step_actors:
                model_count = len(left_renderer.step_actors)
                self.log(f"📦 Model actors: {model_count}")
            elif hasattr(left_renderer, 'step_actor') and left_renderer.step_actor:
                model_count = 1
                self.log(f"📦 Single model actor: 1")
            else:
                self.log("❌ No model actors found")
                
            # Count part origin actors
            part_origin_count = 0
            if hasattr(left_renderer, 'part_origin_sphere') and left_renderer.part_origin_sphere:
                part_origin_count += 1
            if hasattr(left_renderer, 'part_origin_x_arrow') and left_renderer.part_origin_x_arrow:
                part_origin_count += 1
            if hasattr(left_renderer, 'part_origin_y_arrow') and left_renderer.part_origin_y_arrow:
                part_origin_count += 1
            if hasattr(left_renderer, 'part_origin_z_arrow') and left_renderer.part_origin_z_arrow:
                part_origin_count += 1
                
            if part_origin_count > 0:
                self.log(f"🟢 Part origin actors: {part_origin_count}")
            else:
                self.log("❌ No part origin actors found")
                
            # Count world origin actors
            world_origin_count = 0
            if hasattr(left_renderer, 'origin_actors') and left_renderer.origin_actors:
                world_origin_count = len(left_renderer.origin_actors)
                self.log(f"🔴 World origin actors: {world_origin_count}")
            else:
                self.log("❌ No world origin actors found")
                
        except Exception as e:
            self.log(f"❌ Error analyzing initial state: {e}")
            
    def record_initial_orientations(self):
        """Record initial orientations of all actors"""
        try:
            left_renderer = self.viewer.vtk_renderer_left
            
            # Record model actor orientations
            if hasattr(left_renderer, 'step_actors') and left_renderer.step_actors:
                for i, actor in enumerate(left_renderer.step_actors):
                    if actor:
                        orient = actor.GetOrientation()
                        self.initial_orientations[f'model_{i}'] = orient
                        self.log(f"📦 Model {i} initial: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
            elif hasattr(left_renderer, 'step_actor') and left_renderer.step_actor:
                orient = left_renderer.step_actor.GetOrientation()
                self.initial_orientations['model_single'] = orient
                self.log(f"📦 Model initial: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                
            # Record part origin actor orientations
            if hasattr(left_renderer, 'part_origin_sphere') and left_renderer.part_origin_sphere:
                orient = left_renderer.part_origin_sphere.GetOrientation()
                self.initial_orientations['part_sphere'] = orient
                self.log(f"🟢 Part sphere initial: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                
        except Exception as e:
            self.log(f"❌ Error recording initial orientations: {e}")
            
    def simulate_mouse_rotation(self):
        """Simulate mouse rotation on the TOP viewer"""
        try:
            self.log("🖱️  Simulating mouse drag rotation...")
            
            # Get the VTK widget for the TOP viewer
            vtk_widget = self.viewer.vtk_renderer_left.vtk_widget
            if not vtk_widget:
                self.log("❌ No VTK widget found")
                return
                
            # Get widget center for mouse simulation
            center_x = vtk_widget.width() // 2
            center_y = vtk_widget.height() // 2
            
            self.log(f"🖱️  Widget size: {vtk_widget.width()}x{vtk_widget.height()}")
            self.log(f"🖱️  Simulating drag from ({center_x}, {center_y}) to ({center_x + 100}, {center_y + 50})")
            
            # Simulate mouse drag
            QTest.mousePress(vtk_widget, Qt.LeftButton, Qt.NoModifier, 
                           pos=vtk_widget.rect().center())
            QTest.qWait(100)
            
            # Move mouse while pressed (simulate drag)
            for i in range(10):
                x = center_x + (i * 10)
                y = center_y + (i * 5)
                QTest.mouseMove(vtk_widget, pos=vtk_widget.rect().center().translated(i*10, i*5))
                QTest.qWait(50)
                
            QTest.mouseRelease(vtk_widget, Qt.LeftButton, Qt.NoModifier,
                             pos=vtk_widget.rect().center().translated(100, 50))
            QTest.qWait(500)
            
            self.log("✅ Mouse rotation simulation completed")
            
        except Exception as e:
            self.log(f"❌ Error simulating mouse rotation: {e}")
            
    def record_final_orientations(self):
        """Record final orientations after mouse rotation"""
        try:
            left_renderer = self.viewer.vtk_renderer_left
            
            # Record model actor orientations
            if hasattr(left_renderer, 'step_actors') and left_renderer.step_actors:
                for i, actor in enumerate(left_renderer.step_actors):
                    if actor:
                        orient = actor.GetOrientation()
                        self.final_orientations[f'model_{i}'] = orient
                        self.log(f"📦 Model {i} final: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
            elif hasattr(left_renderer, 'step_actor') and left_renderer.step_actor:
                orient = left_renderer.step_actor.GetOrientation()
                self.final_orientations['model_single'] = orient
                self.log(f"📦 Model final: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                
            # Record part origin actor orientations
            if hasattr(left_renderer, 'part_origin_sphere') and left_renderer.part_origin_sphere:
                orient = left_renderer.part_origin_sphere.GetOrientation()
                self.final_orientations['part_sphere'] = orient
                self.log(f"🟢 Part sphere final: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                
        except Exception as e:
            self.log(f"❌ Error recording final orientations: {e}")
            
    def analyze_test_results(self):
        """Analyze the test results"""
        self.log("\n🔍 ANALYZING TEST RESULTS")
        self.log("=" * 30)
        
        try:
            actors_that_rotated = []
            actors_that_didnt_rotate = []
            
            for key in self.initial_orientations:
                if key in self.final_orientations:
                    initial = self.initial_orientations[key]
                    final = self.final_orientations[key]
                    
                    # Check if orientation changed significantly
                    change_x = abs(final[0] - initial[0])
                    change_y = abs(final[1] - initial[1])
                    change_z = abs(final[2] - initial[2])
                    
                    if change_x > 1.0 or change_y > 1.0 or change_z > 1.0:
                        actors_that_rotated.append(key)
                        self.log(f"✅ {key} ROTATED: Δ({change_x:.1f}°, {change_y:.1f}°, {change_z:.1f}°)")
                    else:
                        actors_that_didnt_rotate.append(key)
                        self.log(f"❌ {key} NO CHANGE: Δ({change_x:.1f}°, {change_y:.1f}°, {change_z:.1f}°)")
                        
            self.log(f"\n📊 SUMMARY:")
            self.log(f"✅ Actors that rotated: {len(actors_that_rotated)}")
            self.log(f"❌ Actors that didn't rotate: {len(actors_that_didnt_rotate)}")
            
            if len(actors_that_rotated) == 0:
                self.log("🚨 CRITICAL: NO ACTORS ROTATED - Mouse rotation not working!")
            elif len(actors_that_didnt_rotate) > 0:
                self.log("⚠️  PARTIAL: Some actors rotated, others didn't - Synchronization issue!")
            else:
                self.log("🎉 SUCCESS: All actors rotated together!")
                
        except Exception as e:
            self.log(f"❌ Error analyzing results: {e}")
            
    def generate_final_report(self):
        """Generate final test report"""
        self.log("\n📋 FINAL TEST REPORT")
        self.log("=" * 25)
        
        # Determine overall test result
        if len(self.final_orientations) == 0:
            result = "❌ FAILED - No actors found"
        elif len([k for k in self.initial_orientations if k in self.final_orientations and 
                 any(abs(self.final_orientations[k][i] - self.initial_orientations[k][i]) > 1.0 for i in range(3))]) == 0:
            result = "❌ FAILED - No rotation detected"
        elif len([k for k in self.initial_orientations if k in self.final_orientations and 
                 all(abs(self.final_orientations[k][i] - self.initial_orientations[k][i]) < 1.0 for i in range(3))]) > 0:
            result = "⚠️  PARTIAL - Some actors not rotating"
        else:
            result = "✅ SUCCESS - All actors rotating"
            
        self.log(f"🎯 OVERALL RESULT: {result}")
        self.log("\n🔧 NEXT STEPS:")
        if "FAILED" in result:
            self.log("- Check interactor style configuration")
            self.log("- Verify mouse event handling")
            self.log("- Debug VTK interaction system")
        elif "PARTIAL" in result:
            self.log("- Fix actor synchronization in _rotate_all_model_actors()")
            self.log("- Ensure all actors are included in rotation list")
            self.log("- Check for conflicting rotation systems")
        else:
            self.log("- Mouse rotation is working correctly!")
            self.log("- Test with different STEP files to confirm")

def main():
    test = AutomatedMouseRotationTest()
    return test.run_automated_test()

if __name__ == "__main__":
    sys.exit(main())
