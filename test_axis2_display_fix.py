#!/usr/bin/env python3
"""
Independent test program to debug AXIS2_PLACEMENT_3D data extraction and display
This will test the step_loader and verify the Original Top display functionality
"""

import sys
import os

def test_step_loader_axis2_data():
    """Test if step_loader can extract AXIS2_PLACEMENT_3D data from SOIC file"""
    print("🔧 Testing STEP loader AXIS2_PLACEMENT_3D extraction...")
    
    try:
        # Import the step_loader
        from step_loader import STEPLoader
        
        # Test with the SOIC file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"✅ Found STEP file: {step_file}")
        
        # Create loader and load file
        loader = STEPLoader()
        print("🔧 Loading STEP file...")
        success, message = loader.load_step_file(step_file)
        
        print(f"🔧 Load result: success={success}, message={message}")
        
        # Test the get_original_axis2_placement method
        if hasattr(loader, 'get_original_axis2_placement'):
            print("✅ get_original_axis2_placement method exists")
            axis_data = loader.get_original_axis2_placement()
            
            if axis_data:
                print("✅ AXIS2_PLACEMENT_3D data extracted successfully!")
                print(f"   Point: {axis_data.get('point', 'NOT FOUND')}")
                print(f"   Dir1: {axis_data.get('dir1', 'NOT FOUND')}")
                print(f"   Dir2: {axis_data.get('dir2', 'NOT FOUND')}")
                
                # Test the text formatting that would be used in GUI
                original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                print(f"\n🔧 Formatted text for GUI:\n{original_axis_text}")
                return True
            else:
                print("❌ axis_data is None - no AXIS2_PLACEMENT_3D data found")
                return False
        else:
            print("❌ get_original_axis2_placement method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing step loader: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_file_parsing():
    """Test direct STEP file parsing to see what's in the file"""
    print("\n🔧 Testing direct STEP file parsing...")
    
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(step_file):
        print(f"❌ STEP file not found: {step_file}")
        return False
        
    try:
        with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        print(f"✅ Read STEP file, {len(content)} characters")
        
        # Look for AXIS2_PLACEMENT_3D entries
        import re
        axis_pattern = r'#(\d+) = AXIS2_PLACEMENT_3D\([^;]+\);'
        axis_matches = re.findall(axis_pattern, content)
        
        print(f"🔧 Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries")
        
        if axis_matches:
            for i, match in enumerate(axis_matches[:3]):  # Show first 3
                print(f"   Entry {i+1}: #{match}")
                
            # Try to extract the first one in detail
            first_id = axis_matches[0]
            detailed_pattern = f'#{first_id} = AXIS2_PLACEMENT_3D\\([^;]+\\);'
            detailed_match = re.search(detailed_pattern, content)
            if detailed_match:
                print(f"🔧 First AXIS2_PLACEMENT_3D details:\n   {detailed_match.group(0)}")
                
        return len(axis_matches) > 0
        
    except Exception as e:
        print(f"❌ Error parsing STEP file: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_text_display_logic():
    """Test the GUI text display logic that was modified"""
    print("\n🔧 Testing GUI text display logic...")
    
    # Mock the step_loader with test data
    class MockStepLoader:
        def get_original_axis2_placement(self):
            return {
                'point': '(-4.190000000000000, -3.667300000000000, 0.491400000000000)',
                'dir1': '(0.000000000000000, 0.910400000000000, -0.413800000000000)',
                'dir2': '(0.000000000000000, 0.413800000000000, 0.910400000000000)'
            }
    
    # Test the logic from update_text_overlays
    mock_loader = MockStepLoader()
    
    # Simulate the fixed code logic
    original_axis_text = ""
    if hasattr(mock_loader, 'get_original_axis2_placement'):
        axis_data = mock_loader.get_original_axis2_placement()
        if axis_data:
            print("✅ Mock data retrieved successfully")
            original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
            print(f"🔧 Generated text:\n{original_axis_text}")
        else:
            print("❌ Mock data is None")
            original_axis_text = "\nOriginal top: No coordinate system data found"
    else:
        print("❌ Mock loader missing method")
        original_axis_text = "\nOriginal top: Method not available"
    
    # Test cursor display formatting
    cursor_text = "CURSOR: X=0.10 Y=0.05 Z=-0.00"
    cursor_display = f"{cursor_text}{original_axis_text}"
    
    print(f"\n🔧 Final cursor display text:\n{cursor_display}")
    
    return True

def main():
    """Run all tests"""
    print("🔥 INDEPENDENT AXIS2_PLACEMENT_3D TEST PROGRAM 🔥")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Step loader functionality
    print("\n1. TESTING STEP LOADER FUNCTIONALITY")
    print("-" * 40)
    result1 = test_step_loader_axis2_data()
    test_results.append(("Step Loader Test", result1))
    
    # Test 2: Direct file parsing
    print("\n2. TESTING DIRECT STEP FILE PARSING")
    print("-" * 40)
    result2 = test_step_file_parsing()
    test_results.append(("File Parsing Test", result2))
    
    # Test 3: GUI logic
    print("\n3. TESTING GUI TEXT DISPLAY LOGIC")
    print("-" * 40)
    result3 = test_gui_text_display_logic()
    test_results.append(("GUI Logic Test", result3))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY:")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in test_results)
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED - The fix should work!")
    else:
        print("\n⚠️  SOME TESTS FAILED - Need to investigate further")
    
    return all_passed

if __name__ == "__main__":
    main()
