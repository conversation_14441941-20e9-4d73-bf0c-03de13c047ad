#!/usr/bin/env python3
"""
Verification Script for Origin Marker Fix

This script will thoroughly test that the comprehensive fix actually works:
1. Load model and count initial origin actors
2. Apply rotations to move origin markers
3. Count origin actors after rotation
4. Apply reset and verify EXACT count of origin actors
5. Verify no duplicates exist by examining each actor individually
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class OriginFixVerifier:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.initial_count = 0
        self.after_rotation_count = 0
        self.after_reset_count = 0
        
    def run_verification(self):
        print("🔍 ORIGIN MARKER FIX VERIFICATION")
        print("=" * 50)
        print("This will verify the fix actually deletes duplicates")
        print("=" * 50)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.load_and_count_initial)
        self.app.exec_()
        
    def load_and_count_initial(self):
        print("\n1. Loading STEP file and counting initial origin actors...")
        
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.count_initial_origins)
        
    def count_initial_origins(self):
        self.initial_count = self.detailed_origin_count()
        print(f"   📊 Initial origin actor count: {self.initial_count}")
        
        QTimer.singleShot(1000, self.apply_rotation)
        
    def apply_rotation(self):
        print("\n2. Applying rotation to move origin markers...")
        self.viewer.rotate_shape('x', 45)
        
        QTimer.singleShot(1000, self.count_after_rotation)
        
    def count_after_rotation(self):
        self.after_rotation_count = self.detailed_origin_count()
        print(f"   📊 Origin actor count after rotation: {self.after_rotation_count}")
        
        if self.after_rotation_count != self.initial_count:
            print(f"   ⚠️  Count changed after rotation (expected same count)")
        
        QTimer.singleShot(1000, self.apply_reset)
        
    def apply_reset(self):
        print("\n3. Applying 'Reset to Original' and verifying fix...")
        print("   🔧 This should trigger the comprehensive fix...")
        
        self.viewer.reset_to_original()
        
        QTimer.singleShot(2000, self.verify_fix_results)
        
    def verify_fix_results(self):
        print("\n4. Detailed verification of fix results...")
        
        self.after_reset_count = self.detailed_origin_count()
        print(f"   📊 Origin actor count after reset: {self.after_reset_count}")
        
        # Analyze results
        print("\n📋 ANALYSIS:")
        print(f"   Initial count:      {self.initial_count}")
        print(f"   After rotation:     {self.after_rotation_count}")
        print(f"   After reset:        {self.after_reset_count}")
        
        # Check if fix worked
        if self.after_reset_count <= self.initial_count + 2:  # Allow small margin
            print("\n🎉 SUCCESS: Fix appears to be working!")
            print("✅ No significant increase in origin actor count")
            print("✅ Comprehensive deletion mechanism is effective")
        else:
            print("\n❌ FAILURE: Fix did not work properly")
            print(f"❌ Expected ~{self.initial_count} actors, got {self.after_reset_count}")
            print("❌ Duplicates are still being created")
            
        # Detailed actor analysis
        self.analyze_individual_actors()
        
        QTimer.singleShot(3000, self.app.quit)
        
    def detailed_origin_count(self):
        """Count origin actors with detailed analysis"""
        count = 0
        red_count = 0
        green_count = 0
        other_count = 0
        
        try:
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                renderer = self.viewer.vtk_renderer_left.renderer
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    while True:
                        actor = actors.GetNextActor()
                        if not actor:
                            break
                            
                        prop = actor.GetProperty()
                        if prop:
                            color = prop.GetColor()
                            
                            # Red origin markers
                            if color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2:
                                red_count += 1
                                count += 1
                            # Green origin markers  
                            elif color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2:
                                green_count += 1
                                count += 1
                            # Blue origin markers (Z-axis arrows)
                            elif color[0] < 0.2 and color[1] < 0.2 and color[2] > 0.8:
                                other_count += 1
                                count += 1
                                
        except Exception as e:
            print(f"   Error counting: {e}")
            
        print(f"   🔴 Red actors: {red_count}")
        print(f"   🟢 Green actors: {green_count}")  
        print(f"   🔵 Blue actors: {other_count}")
        print(f"   📊 Total origin-related: {count}")
        
        return count
        
    def analyze_individual_actors(self):
        """Analyze each origin actor individually to detect duplicates"""
        print("\n5. Individual actor analysis...")
        
        try:
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                renderer = self.viewer.vtk_renderer_left.renderer
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    
                    red_positions = []
                    green_positions = []
                    
                    while True:
                        actor = actors.GetNextActor()
                        if not actor:
                            break
                            
                        prop = actor.GetProperty()
                        if prop:
                            color = prop.GetColor()
                            position = actor.GetPosition()
                            
                            # Check for red actors (world origin)
                            if color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2:
                                red_positions.append(position)
                                
                            # Check for green actors (part origin)
                            elif color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2:
                                green_positions.append(position)
                    
                    # Check for duplicates at same positions
                    print(f"   🔴 Red actor positions: {len(red_positions)}")
                    for i, pos in enumerate(red_positions):
                        print(f"      Red {i}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                        
                    print(f"   🟢 Green actor positions: {len(green_positions)}")
                    for i, pos in enumerate(green_positions):
                        print(f"      Green {i}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                    
                    # Detect duplicates
                    red_duplicates = self.find_duplicate_positions(red_positions)
                    green_duplicates = self.find_duplicate_positions(green_positions)
                    
                    if red_duplicates or green_duplicates:
                        print("   ❌ DUPLICATES DETECTED:")
                        if red_duplicates:
                            print(f"      🔴 Red duplicates: {red_duplicates}")
                        if green_duplicates:
                            print(f"      🟢 Green duplicates: {green_duplicates}")
                    else:
                        print("   ✅ No duplicate positions detected")
                        
        except Exception as e:
            print(f"   Error in individual analysis: {e}")
            
    def find_duplicate_positions(self, positions):
        """Find positions that appear more than once"""
        duplicates = []
        for i, pos1 in enumerate(positions):
            for j, pos2 in enumerate(positions[i+1:], i+1):
                # Check if positions are very close (within 0.001 units)
                if (abs(pos1[0] - pos2[0]) < 0.001 and 
                    abs(pos1[1] - pos2[1]) < 0.001 and 
                    abs(pos1[2] - pos2[2]) < 0.001):
                    duplicates.append((i, j, pos1))
        return duplicates

if __name__ == '__main__':
    verifier = OriginFixVerifier()
    verifier.run_verification()
