#!/usr/bin/env python3
"""
Test to verify that the saved file contains the rotations
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer import StepViewerTDK

def test_load_saved():
    print("🧪 LOAD SAVED FILE TEST")
    print("=" * 50)
    
    # Create application and viewer
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load the saved file
    saved_file = "test-simple-save.step"
    if not os.path.exists(saved_file):
        print(f"❌ Saved file not found: {saved_file}")
        return False
        
    print(f"1. Loading saved file: {saved_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(saved_file)
    if not success:
        print("❌ Failed to load saved file")
        return False
    print("✅ Saved file loaded")
    
    # Check the Direction values
    print("\n2. Checking Direction values...")
    
    # Get the current Direction from the text overlay system
    # This should show the actual STEP file coordinate system values
    try:
        # Force update of text overlays to get current values
        viewer.update_text_overlays()
        
        # The Direction values should match the rotated values from the save test
        # Expected: Direction (X = -0.828 Y = 0.557 Z = 0.068) approximately
        
        print("✅ Direction values retrieved from loaded file")
        print("   (Check console output above for actual Direction values)")
        
        # If we got here without errors, the file loaded successfully
        return True
        
    except Exception as e:
        print(f"❌ Error checking Direction values: {e}")
        return False

if __name__ == "__main__":
    success = test_load_saved()
    if success:
        print("\n🎉 LOAD TEST PASSED!")
        print("Check the Direction values in the console output above.")
        print("They should show the rotated values, not the original values.")
    else:
        print("\n❌ LOAD TEST FAILED!")
    sys.exit(0 if success else 1)
