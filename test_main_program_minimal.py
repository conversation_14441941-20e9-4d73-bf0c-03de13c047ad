#!/usr/bin/env python3
"""
Minimal test of the main program to isolate the white background issue
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt

def test_main_program_minimal():
    """Test minimal version of main program"""
    print("🔍 Testing minimal main program...")
    
    try:
        # Import the VTK renderer
        from vtk_renderer import VTKRenderer
        print("✅ VTKRenderer imported successfully")
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create main window
        window = QMainWindow()
        window.setWindowTitle("Minimal STEP Viewer Test")
        window.setGeometry(100, 100, 1200, 800)
        
        # Create central widget with layout
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Create left side for VTK viewer
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Add file label
        file_label = QLabel("TOP VIEWER - Testing VTK initialization")
        file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        left_layout.addWidget(file_label)
        
        # Create VTK renderer
        print("🔧 Creating VTK renderer...")
        vtk_renderer = VTKRenderer(window)  # Pass the main window as parent
        
        if vtk_renderer.vtk_widget:
            print("✅ VTK widget created successfully")
            left_layout.addWidget(vtk_renderer.vtk_widget)
            
            # Check if renderer has dark blue background
            if hasattr(vtk_renderer, 'renderer'):
                bg_color = vtk_renderer.renderer.GetBackground()
                print(f"🎨 Renderer background color: {bg_color}")
                if bg_color == (0.1, 0.1, 0.2):
                    print("✅ Dark blue background set correctly")
                else:
                    print("⚠️  Background color is not dark blue")
            else:
                print("❌ No renderer found")
        else:
            print("❌ VTK widget creation failed")
            error_label = QLabel("VTK Widget Creation Failed")
            error_label.setStyleSheet("color: red; font-size: 16px; padding: 20px;")
            left_layout.addWidget(error_label)
        
        main_layout.addWidget(left_widget)
        
        # Create right side for controls
        right_widget = QWidget()
        right_widget.setFixedWidth(200)
        right_widget.setStyleSheet("background-color: lightgray; padding: 10px;")
        right_layout = QVBoxLayout(right_widget)
        
        status_label = QLabel("VTK Renderer Test\n\nIf you see dark blue\nbackground on the left,\nVTK is working correctly.")
        status_label.setWordWrap(True)
        right_layout.addWidget(status_label)
        
        main_layout.addWidget(right_widget)
        
        # Show window
        window.show()
        print("✅ Window shown")
        
        print("\n🎯 Check the window:")
        print("   - LEFT side should have DARK BLUE background")
        print("   - RIGHT side should have gray controls")
        print("   - If left side is WHITE, VTK renderer failed to initialize")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Minimal test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    sys.exit(test_main_program_minimal())
