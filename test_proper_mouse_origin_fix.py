#!/usr/bin/env python3
"""
Test the proper mouse origin update fix
This verifies that:
1. Normal mouse rotation still works (camera rotates around model)
2. Origin position values update during mouse interactions
3. Both button and mouse rotations work consistently
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer import StepViewerTDK

class ProperMouseOriginTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_test(self):
        print("🧪 TESTING PROPER MOUSE ORIGIN UPDATE FIX")
        print("=" * 70)
        
        # Create viewer
        print("1. Creating viewer...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Load STEP file
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the application
        self.app.exec_()
        
    def load_step_file(self):
        print("2. Loading STEP file...")
        
        # Load the test STEP file
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if os.path.exists(step_file):
            success = self.viewer.load_step_file_direct(step_file)
            if success:
                print("✅ STEP file loaded successfully")
                QTimer.singleShot(1000, self.check_interactor_style)
            else:
                print("❌ Failed to load STEP file")
                self.app.quit()
        else:
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            
    def check_interactor_style(self):
        print("\n3. Checking VTK interactor style...")
        
        # Check if we have the correct TrackballCamera style
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                style = self.viewer.vtk_renderer_left.interactor.GetInteractorStyle()
                style_name = style.__class__.__name__
                print(f"   VTK interactor style: {style_name}")
                
                if 'TrackballCamera' in style_name:
                    print("   ✅ Good: Using TrackballCamera - normal mouse rotation should work")
                elif 'TrackballActor' in style_name:
                    print("   ❌ Problem: Using TrackballActor - this breaks normal mouse rotation")
                else:
                    print(f"   ❓ Unknown style: {style_name}")
        
        QTimer.singleShot(1000, self.test_button_rotation)
        
    def test_button_rotation(self):
        print("\n4. Testing button rotation (baseline)...")
        
        # Get initial origin position
        if hasattr(self.viewer, 'current_pos_left'):
            self.initial_pos = self.viewer.current_pos_left.copy()
            print(f"   Initial origin: {self.initial_pos}")
            
            # Apply button rotation
            print("   Applying 30° X rotation via button...")
            self.viewer.rotate_shape('x', 30)
            
            # Check if origin updated
            QTimer.singleShot(500, self.check_button_result)
        else:
            print("   ❌ No initial origin position found")
            self.app.quit()
    
    def check_button_result(self):
        if hasattr(self.viewer, 'current_pos_left'):
            button_pos = self.viewer.current_pos_left.copy()
            print(f"   Origin after button rotation: {button_pos}")
            
            # Check if values changed
            x_changed = abs(button_pos['x'] - self.initial_pos['x']) > 0.001
            y_changed = abs(button_pos['y'] - self.initial_pos['y']) > 0.001
            z_changed = abs(button_pos['z'] - self.initial_pos['z']) > 0.001
            
            if x_changed or y_changed or z_changed:
                print("   ✅ Button rotation updates origin - GOOD")
                self.button_pos = button_pos
            else:
                print("   ❌ Button rotation does NOT update origin - PROBLEM")
                self.button_pos = self.initial_pos
        
        QTimer.singleShot(1000, self.test_mouse_rotation)
    
    def test_mouse_rotation(self):
        print("\n5. Testing mouse rotation simulation...")
        
        try:
            # Simulate camera rotation (what mouse dragging does)
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'renderer'):
                    camera = self.viewer.vtk_renderer_left.renderer.GetActiveCamera()
                    if camera:
                        print("   Simulating mouse drag rotation...")
                        
                        # Simulate rotation by changing camera position (like mouse dragging)
                        camera.Azimuth(45)  # This is what mouse dragging does
                        camera.OrthogonalizeViewUp()
                        
                        # Trigger interaction event (like mouse interaction does)
                        if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                            self.viewer.vtk_renderer_left.interactor.InvokeEvent('InteractionEvent')
                        
                        # Force render
                        if hasattr(self.viewer.vtk_renderer_left, 'render_window'):
                            self.viewer.vtk_renderer_left.render_window.Render()
                        
                        print("   ✅ Mouse rotation simulation complete")
                        
        except Exception as e:
            print(f"   ❌ Error during mouse rotation test: {e}")
        
        QTimer.singleShot(1000, self.check_mouse_result)
    
    def check_mouse_result(self):
        print("\n6. Checking mouse rotation results...")
        
        if hasattr(self.viewer, 'current_pos_left'):
            mouse_pos = self.viewer.current_pos_left
            print(f"   Origin after mouse rotation: {mouse_pos}")
            
            # Check if values changed from button rotation position
            x_changed = abs(mouse_pos['x'] - self.button_pos['x']) > 0.001
            y_changed = abs(mouse_pos['y'] - self.button_pos['y']) > 0.001
            z_changed = abs(mouse_pos['z'] - self.button_pos['z']) > 0.001
            
            print(f"   X changed: {'✅' if x_changed else '❌'} ({self.button_pos['x']:.3f} → {mouse_pos['x']:.3f})")
            print(f"   Y changed: {'✅' if y_changed else '❌'} ({self.button_pos['y']:.3f} → {mouse_pos['y']:.3f})")
            print(f"   Z changed: {'✅' if z_changed else '❌'} ({self.button_pos['z']:.3f} → {mouse_pos['z']:.3f})")
            
            if x_changed or y_changed or z_changed:
                print("   🎉 SUCCESS: Mouse rotation updates origin position!")
                self.test_result = "SUCCESS"
            else:
                print("   ❌ FAILURE: Mouse rotation does NOT update origin position")
                self.test_result = "FAILURE"
        else:
            print("   ❌ No origin position found after mouse rotation")
            self.test_result = "ERROR"
        
        QTimer.singleShot(1000, self.final_assessment)
    
    def final_assessment(self):
        print("\n" + "=" * 70)
        print("FINAL TEST RESULTS")
        print("=" * 70)
        
        if hasattr(self, 'test_result'):
            if self.test_result == "SUCCESS":
                print("🎉 OVERALL RESULT: SUCCESS")
                print("✅ Mouse rotation now updates origin position values")
                print("✅ Normal mouse rotation behavior preserved")
                print("✅ Both button and mouse rotations work consistently")
                
                print("\n📋 WHAT WORKS NOW:")
                print("   • Mouse drag rotates camera around model (normal behavior)")
                print("   • Origin position values update during mouse interactions")
                print("   • Button rotations continue to work as before")
                print("   • Text overlays show updated origin values in real-time")
                
            elif self.test_result == "FAILURE":
                print("❌ OVERALL RESULT: FAILURE")
                print("❌ Mouse rotation still does not update origin position")
                print("🔧 Need to investigate further...")
                
            else:
                print("❓ OVERALL RESULT: ERROR")
                print("❌ Test encountered errors")
        
        print("\n" + "=" * 70)
        print("TEST COMPLETE")
        
        QTimer.singleShot(3000, self.app.quit)

if __name__ == '__main__':
    test = ProperMouseOriginTest()
    test.run_test()
