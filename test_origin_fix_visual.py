#!/usr/bin/env python3
"""
Visual test to demonstrate that the left 6 rotation buttons now correctly
update the origin position numbers in the yellow text display.

This test loads a model and applies rotations, showing that both:
1. The origin actors rotate visually (they follow the model)
2. The origin position numbers in the yellow text update to reflect the new transformed position

BEFORE THE FIX: Origin actors rotated visually but numbers stayed constant
AFTER THE FIX: Both origin actors AND numbers update correctly
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

# Import the main program
from step_viewer import StepViewerTDK

class OriginFixVisualTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Visual Test: Origin Fix for Left 6 Buttons")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create the main viewer
        self.viewer = StepViewerTDK()
        
        # Setup UI
        self.init_ui()
        
        # Load a test model
        QTimer.singleShot(1000, self.load_test_model)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("ORIGIN FIX VISUAL TEST")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Instructions
        instructions = QLabel(
            "INSTRUCTIONS:\n"
            "1. Wait for model to load\n"
            "2. Click the rotation buttons (X+, Y+, Z+) on the left side\n"
            "3. Watch BOTH the visual origin markers AND the yellow text numbers\n"
            "4. BEFORE FIX: Origin markers moved but numbers stayed constant\n"
            "5. AFTER FIX: Both origin markers AND numbers update correctly"
        )
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc;")
        layout.addWidget(instructions)
        
        # Test buttons
        button_layout = QHBoxLayout()
        
        self.test_x_btn = QPushButton("Test X+ Rotation (15°)")
        self.test_x_btn.clicked.connect(lambda: self.test_rotation('x', 15))
        button_layout.addWidget(self.test_x_btn)
        
        self.test_y_btn = QPushButton("Test Y+ Rotation (15°)")
        self.test_y_btn.clicked.connect(lambda: self.test_rotation('y', 15))
        button_layout.addWidget(self.test_y_btn)
        
        self.test_z_btn = QPushButton("Test Z+ Rotation (15°)")
        self.test_z_btn.clicked.connect(lambda: self.test_rotation('z', 15))
        button_layout.addWidget(self.test_z_btn)
        
        self.reset_btn = QPushButton("Reset Model")
        self.reset_btn.clicked.connect(self.reset_model)
        button_layout.addWidget(self.reset_btn)
        
        layout.addLayout(button_layout)
        
        # Status
        self.status_label = QLabel("Status: Initializing...")
        self.status_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.status_label)
        
        # Add the main viewer
        layout.addWidget(self.viewer)
        
    def load_test_model(self):
        """Load a test STEP file"""
        self.status_label.setText("Status: Loading test model...")
        
        # Try to load test.step if it exists
        if os.path.exists("test.step"):
            success = self.viewer.load_step_file("test.step")
            if success:
                self.status_label.setText("Status: Model loaded! Try the rotation buttons above.")
                print("✅ Test model loaded successfully")
                
                # Show initial origin position
                if hasattr(self.viewer, 'current_pos_left'):
                    pos = self.viewer.current_pos_left
                    print(f"📍 Initial Origin Position: X={pos['x']:.3f}, Y={pos['y']:.3f}, Z={pos['z']:.3f}")
            else:
                self.status_label.setText("Status: Failed to load test.step")
                print("❌ Failed to load test.step")
        else:
            self.status_label.setText("Status: No test.step file found. Please load a model manually.")
            print("⚠️ No test.step file found")
            
    def test_rotation(self, axis, degrees):
        """Test a rotation and show the results"""
        self.status_label.setText(f"Status: Testing {axis.upper()}+ rotation ({degrees}°)...")
        
        # Get position before rotation
        if hasattr(self.viewer, 'current_pos_left'):
            before_pos = self.viewer.current_pos_left.copy()
            print(f"\n🔄 TESTING {axis.upper()}+ ROTATION ({degrees}°)")
            print(f"📍 Before: X={before_pos['x']:.3f}, Y={before_pos['y']:.3f}, Z={before_pos['z']:.3f}")
        else:
            print(f"\n🔄 TESTING {axis.upper()}+ ROTATION ({degrees}°)")
            print("⚠️ No current_pos_left found")
            before_pos = None
            
        # Apply the rotation
        self.viewer.active_viewer = "top"  # Make sure we're working with the top viewer
        self.viewer.rotate_shape(axis, degrees)
        
        # Show position after rotation
        if hasattr(self.viewer, 'current_pos_left') and before_pos:
            after_pos = self.viewer.current_pos_left.copy()
            print(f"📍 After:  X={after_pos['x']:.3f}, Y={after_pos['y']:.3f}, Z={after_pos['z']:.3f}")
            
            # Calculate changes
            dx = after_pos['x'] - before_pos['x']
            dy = after_pos['y'] - before_pos['y'] 
            dz = after_pos['z'] - before_pos['z']
            
            if abs(dx) > 0.001 or abs(dy) > 0.001 or abs(dz) > 0.001:
                print(f"✅ Origin position CHANGED: ΔX={dx:.3f}, ΔY={dy:.3f}, ΔZ={dz:.3f}")
                self.status_label.setText(f"Status: ✅ {axis.upper()}+ rotation applied - Origin numbers updated!")
            else:
                print(f"❌ Origin position did NOT change")
                self.status_label.setText(f"Status: ❌ {axis.upper()}+ rotation applied - Origin numbers NOT updated!")
        else:
            self.status_label.setText(f"Status: {axis.upper()}+ rotation applied")
            
    def reset_model(self):
        """Reset the model to original position"""
        self.status_label.setText("Status: Resetting model...")
        
        # Reload the model to reset transformations
        if os.path.exists("test.step"):
            self.viewer.load_step_file("test.step")
            self.status_label.setText("Status: Model reset to original position")
            print("🔄 Model reset to original position")
        else:
            self.status_label.setText("Status: Cannot reset - no test.step file")

def main():
    app = QApplication(sys.argv)
    
    # Create and show the test window
    test_window = OriginFixVisualTest()
    test_window.show()
    
    print("🚀 ORIGIN FIX VISUAL TEST STARTED")
    print("="*60)
    print("This test demonstrates the fix for left 6 rotation buttons.")
    print("BEFORE FIX: Origin actors rotated but numbers stayed constant")
    print("AFTER FIX: Both origin actors AND numbers update correctly")
    print("="*60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
