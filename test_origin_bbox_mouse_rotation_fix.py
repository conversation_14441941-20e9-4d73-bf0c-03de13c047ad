#!/usr/bin/env python3
"""
Test: Origin and Bounding Box Mouse Rotation Fix
This test verifies that origin markers and bounding box rotate with the model during mouse interaction
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import vtk

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class OriginBboxMouseRotationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_phase = 0
        self.initial_model_rotation = None
        self.initial_origin_rotation = None
        
    def start_test(self):
        """Start the test"""
        print("🎯 TESTING: Origin and Bounding Box Mouse Rotation Fix")
        print("=" * 60)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(2000, self.load_model)
        
        return self.app.exec_()
        
    def load_model(self):
        """Load a test model"""
        print("🔧 Loading test model...")
        
        try:
            # Load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                return
                
            # Wait for model to load
            QTimer.singleShot(3000, self.enable_overlays)
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            
    def enable_overlays(self):
        """Enable origin overlays and bounding box"""
        print("🔧 Enabling overlays...")
        
        try:
            # Enable origin overlay
            if hasattr(self.viewer.vtk_renderer_left, 'create_origin_overlay'):
                self.viewer.vtk_renderer_left.create_origin_overlay()
                print("✅ World origin overlay enabled (red semicircle + XYZ arrows)")
            
            # Enable bounding box
            if hasattr(self.viewer, 'toggle_bounding_box_left'):
                self.viewer.toggle_bounding_box_left()
                print("✅ Bounding box enabled")
            
            # Wait a bit, then start testing
            QTimer.singleShot(2000, self.start_rotation_test)
            
        except Exception as e:
            print(f"❌ Error enabling overlays: {e}")
            
    def start_rotation_test(self):
        """Start the rotation test"""
        print("\n🖱️  MOUSE ROTATION TEST")
        print("=" * 30)
        print("INSTRUCTIONS:")
        print("1. Use your mouse to drag and rotate the model in the TOP viewer")
        print("2. Watch these elements during rotation:")
        print("   - Red semicircle and XYZ arrows (world origin)")
        print("   - Green sphere and arrows (part origin)")
        print("   - Red bounding box wireframe")
        print("3. ALL elements should rotate together with the model")
        print("4. Test will monitor for 20 seconds")
        print()
        
        # Capture initial states
        self.capture_initial_states()
        
        # Start monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_rotation_sync)
        self.monitor_timer.start(1000)  # Check every second
        
        # End test after 20 seconds
        QTimer.singleShot(20000, self.complete_test)
        
    def capture_initial_states(self):
        """Capture initial rotation states"""
        try:
            # Get model rotation
            if (hasattr(self.viewer.vtk_renderer_left, 'step_multi_actors') and 
                self.viewer.vtk_renderer_left.step_multi_actors):
                actor = self.viewer.vtk_renderer_left.step_multi_actors[0]
                self.initial_model_rotation = actor.GetOrientation()
                print(f"📊 Initial model rotation: {self.initial_model_rotation}")
            
            # Get origin overlay rotation (if exists)
            if (hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and 
                self.viewer.vtk_renderer_left.origin_actors):
                origin_actor = self.viewer.vtk_renderer_left.origin_actors[0]
                self.initial_origin_rotation = origin_actor.GetOrientation()
                print(f"📊 Initial origin rotation: {self.initial_origin_rotation}")
                
        except Exception as e:
            print(f"🔧 Error capturing initial states: {e}")
            
    def monitor_rotation_sync(self):
        """Monitor if origin overlays stay synchronized with model"""
        try:
            # Get current model rotation
            if (hasattr(self.viewer.vtk_renderer_left, 'step_multi_actors') and 
                self.viewer.vtk_renderer_left.step_multi_actors):
                actor = self.viewer.vtk_renderer_left.step_multi_actors[0]
                current_model_rotation = actor.GetOrientation()
                
                # Check if model has rotated significantly
                if self.initial_model_rotation:
                    rotation_change = sum(abs(current_model_rotation[i] - self.initial_model_rotation[i]) 
                                        for i in range(3))
                    
                    if rotation_change > 1.0:  # Significant rotation detected
                        print(f"🔄 Model rotation detected: {current_model_rotation}")
                        
                        # Check if origin overlays are synchronized
                        self.check_origin_sync(current_model_rotation)
                        
                        # Update initial rotation for next comparison
                        self.initial_model_rotation = current_model_rotation
                        
        except Exception as e:
            # Don't spam errors
            pass
            
    def check_origin_sync(self, model_rotation):
        """Check if origin overlays are synchronized with model rotation"""
        try:
            # Check world origin actors
            if (hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and 
                self.viewer.vtk_renderer_left.origin_actors):
                origin_actor = self.viewer.vtk_renderer_left.origin_actors[0]
                origin_rotation = origin_actor.GetOrientation()
                
                # Calculate rotation difference
                rotation_diff = sum(abs(model_rotation[i] - origin_rotation[i]) for i in range(3))
                
                if rotation_diff < 1.0:  # Close enough (within 1 degree)
                    print("✅ World origin synchronized with model")
                else:
                    print(f"❌ World origin NOT synchronized - Model: {model_rotation}, Origin: {origin_rotation}")
            
            # Check part origin actors
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere'):
                part_sphere = self.viewer.vtk_renderer_left.part_origin_sphere
                if part_sphere:
                    part_rotation = part_sphere.GetOrientation()
                    rotation_diff = sum(abs(model_rotation[i] - part_rotation[i]) for i in range(3))
                    
                    if rotation_diff < 1.0:
                        print("✅ Part origin synchronized with model")
                    else:
                        print(f"❌ Part origin NOT synchronized - Model: {model_rotation}, Part: {part_rotation}")
                        
        except Exception as e:
            print(f"🔧 Error checking sync: {e}")
            
    def complete_test(self):
        """Complete the test"""
        self.monitor_timer.stop()
        
        print("\n" + "=" * 50)
        print("🎯 TEST COMPLETED")
        print("=" * 50)
        print("RESULTS:")
        print("✅ If origin markers rotated with the model: FIX WORKING")
        print("❌ If origin markers stayed fixed: FIX NEEDS MORE WORK")
        print()
        print("EXPECTED BEHAVIOR:")
        print("- Red semicircle and XYZ arrows should rotate with model")
        print("- Green sphere and arrows should rotate with model")
        print("- Bounding box should update to match rotated model")
        print("- All elements should move together as one unit")
        print()
        print("👋 Test completed. Press Ctrl+C to exit.")

if __name__ == "__main__":
    test = OriginBboxMouseRotationTest()
    try:
        sys.exit(test.start_test())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(0)
