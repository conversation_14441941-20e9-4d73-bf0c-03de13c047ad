#!/usr/bin/env python3
"""
Simple Mouse Rotation Debug - Console Output
This will quickly test and report what's happening with mouse rotation
"""

import sys
import os
import time

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

def debug_mouse_rotation():
    """Debug mouse rotation setup"""
    print("🔍 MOUSE ROTATION DEBUG")
    print("=" * 30)
    
    try:
        # Import PyQt5 for GUI
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        print("Step 1: Creating viewer...")
        viewer = StepViewerTDK()
        viewer.show()
        
        # Process events
        app.processEvents()
        time.sleep(2)
        
        print("Step 2: Analyzing VTK setup...")
        left_renderer = viewer.vtk_renderer_left
        
        # Check interactor style
        if hasattr(left_renderer, 'interactor') and left_renderer.interactor:
            style = left_renderer.interactor.GetInteractorStyle()
            style_name = style.__class__.__name__
            print(f"🖱️  Interactor Style: {style_name}")
            
            # Check if it has our custom method
            if hasattr(style, '_rotate_all_model_actors'):
                print("✅ Custom _rotate_all_model_actors method found")
                
                # Check what it would rotate
                if hasattr(style, 'vtk_renderer_obj'):
                    renderer_obj = style.vtk_renderer_obj
                    
                    # Check model actors
                    model_actors = []
                    if hasattr(renderer_obj, 'step_actors') and renderer_obj.step_actors:
                        model_actors = renderer_obj.step_actors
                        print(f"📦 Model actors available: {len(model_actors)}")
                    elif hasattr(renderer_obj, 'step_actor') and renderer_obj.step_actor:
                        model_actors = [renderer_obj.step_actor]
                        print(f"📦 Single model actor available: 1")
                    else:
                        print("❌ No model actors found")
                        
                    # Check part origin actors
                    part_origin_actors = []
                    if hasattr(renderer_obj, 'part_origin_sphere') and renderer_obj.part_origin_sphere:
                        part_origin_actors.append('sphere')
                    if hasattr(renderer_obj, 'part_origin_x_arrow') and renderer_obj.part_origin_x_arrow:
                        part_origin_actors.append('x_arrow')
                    if hasattr(renderer_obj, 'part_origin_y_arrow') and renderer_obj.part_origin_y_arrow:
                        part_origin_actors.append('y_arrow')
                    if hasattr(renderer_obj, 'part_origin_z_arrow') and renderer_obj.part_origin_z_arrow:
                        part_origin_actors.append('z_arrow')
                        
                    if part_origin_actors:
                        print(f"🟢 Part origin actors available: {part_origin_actors}")
                    else:
                        print("❌ No part origin actors found")
                        
                    # Test the method directly
                    print("\nStep 3: Testing _rotate_all_model_actors method...")
                    try:
                        # Get initial orientations
                        initial_orientations = {}
                        for i, actor in enumerate(model_actors):
                            if actor:
                                initial_orientations[f'model_{i}'] = actor.GetOrientation()
                                
                        print("🔄 Calling _rotate_all_model_actors(10, 5)...")
                        style._rotate_all_model_actors(10, 5)  # Small test rotation
                        
                        # Check if orientations changed
                        changes_detected = False
                        for i, actor in enumerate(model_actors):
                            if actor:
                                new_orient = actor.GetOrientation()
                                old_orient = initial_orientations[f'model_{i}']
                                
                                change_x = abs(new_orient[0] - old_orient[0])
                                change_y = abs(new_orient[1] - old_orient[1])
                                change_z = abs(new_orient[2] - old_orient[2])
                                
                                if change_x > 0.1 or change_y > 0.1 or change_z > 0.1:
                                    print(f"✅ Model {i} rotated: Δ({change_x:.1f}°, {change_y:.1f}°, {change_z:.1f}°)")
                                    changes_detected = True
                                else:
                                    print(f"❌ Model {i} no change: Δ({change_x:.1f}°, {change_y:.1f}°, {change_z:.1f}°)")
                                    
                        if changes_detected:
                            print("🎉 SUCCESS: _rotate_all_model_actors is working!")
                        else:
                            print("❌ FAILED: _rotate_all_model_actors not rotating actors")
                            
                    except Exception as e:
                        print(f"❌ Error testing rotation method: {e}")
                        
                else:
                    print("❌ No vtk_renderer_obj reference in style")
            else:
                print("❌ Custom _rotate_all_model_actors method NOT found")
        else:
            print("❌ No interactor found")
            
        print("\nStep 4: Checking mouse event handling...")
        
        # Check if OnMouseMove is overridden
        if hasattr(left_renderer, 'interactor') and left_renderer.interactor:
            style = left_renderer.interactor.GetInteractorStyle()
            if hasattr(style, 'OnMouseMove'):
                print("✅ OnMouseMove method found")
                
                # Check if it calls our rotation method
                import inspect
                try:
                    source = inspect.getsource(style.OnMouseMove)
                    if '_rotate_all_model_actors' in source:
                        print("✅ OnMouseMove calls _rotate_all_model_actors")
                    else:
                        print("❌ OnMouseMove does NOT call _rotate_all_model_actors")
                except:
                    print("⚠️  Could not inspect OnMouseMove source")
            else:
                print("❌ OnMouseMove method not found")
                
        print("\n📋 SUMMARY:")
        print("- Check the above results to see what's working/broken")
        print("- The viewer window should remain open for manual testing")
        print("- Try manual mouse rotation to compare with automated results")
        
        # Keep the app running for manual inspection
        print("\n🔍 Viewer remains open for manual testing...")
        return app.exec_()
        
    except Exception as e:
        print(f"❌ DEBUG FAILED: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    sys.exit(debug_mouse_rotation())
