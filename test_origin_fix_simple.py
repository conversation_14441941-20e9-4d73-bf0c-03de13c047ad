#!/usr/bin/env python3
"""
Simple test to verify the origin fix is working correctly.
This test loads the viewer and applies rotations to check if origin numbers update.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from step_viewer import StepViewerTDK

def test_origin_fix():
    """Test that origin position numbers update correctly during button rotations"""
    print("🚀 TESTING ORIGIN FIX FOR LEFT 6 BUTTONS")
    print("="*60)
    
    app = QApplication([])
    
    try:
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Viewer created successfully")
        
        # Load test model if available
        if os.path.exists("test.step"):
            success = viewer.load_step_file_direct("test.step")
            if success:
                print("✅ Test model loaded successfully")
            else:
                print("⚠️ Failed to load test.step, continuing with empty viewer")
        else:
            print("⚠️ No test.step file found, continuing with empty viewer")
            
        # Initialize position tracking if not already done
        if not hasattr(viewer, 'current_pos_left'):
            viewer.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            print("📍 Initialized current_pos_left to (0, 0, 0)")
            
        # Test X+ rotation
        print(f"\n🔄 TESTING X+ ROTATION (15°)")
        before_pos = viewer.current_pos_left.copy()
        print(f"📍 Before: X={before_pos['x']:.3f}, Y={before_pos['y']:.3f}, Z={before_pos['z']:.3f}")
        
        # Set active viewer and apply rotation
        viewer.active_viewer = "top"
        viewer.rotate_shape('x', 15)
        
        after_pos = viewer.current_pos_left.copy()
        print(f"📍 After:  X={after_pos['x']:.3f}, Y={after_pos['y']:.3f}, Z={after_pos['z']:.3f}")
        
        # Check if position changed
        dx = abs(after_pos['x'] - before_pos['x'])
        dy = abs(after_pos['y'] - before_pos['y'])
        dz = abs(after_pos['z'] - before_pos['z'])
        
        if dx > 0.001 or dy > 0.001 or dz > 0.001:
            print(f"✅ SUCCESS: Origin position numbers UPDATED!")
            print(f"   Changes: ΔX={after_pos['x']-before_pos['x']:.3f}, ΔY={after_pos['y']-before_pos['y']:.3f}, ΔZ={after_pos['z']-before_pos['z']:.3f}")
            result = True
        else:
            print(f"❌ FAILURE: Origin position numbers did NOT change")
            result = False
            
        print("\n" + "="*60)
        if result:
            print("🎉 ORIGIN FIX IS WORKING CORRECTLY!")
            print("   The left 6 rotation buttons now update both:")
            print("   1. Visual origin markers (they rotate with the model)")
            print("   2. Origin position numbers (they reflect the new transformed position)")
        else:
            print("❌ ORIGIN FIX IS NOT WORKING")
            print("   The origin position numbers are not being updated during rotations")
            
        return result
        
    except Exception as e:
        print(f"❌ ERROR during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_origin_fix()
    sys.exit(0 if success else 1)
