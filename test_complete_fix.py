#!/usr/bin/env python3
"""
Test the complete fix for both Original Bottom labels and yellow text overlays
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_complete_fix():
    """Test both Original Bottom labels and yellow text overlays"""
    print("🔧 Testing complete fix...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load STEP file in TOP viewer
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file in TOP viewer: {step_file}")
        viewer.active_viewer = "top"
        success_top = viewer.load_step_file_direct(step_file)
        
        if success_top:
            print("✅ STEP file loaded in TOP viewer")
            
            # Check TOP viewer labels
            print("\n🔧 Checking TOP viewer labels...")
            if hasattr(viewer, 'lbl_orig_axis_x'):
                print(f"✅ TOP Direction: {viewer.lbl_orig_axis_x.text()}, {viewer.lbl_orig_axis_y.text()}, {viewer.lbl_orig_axis_z.text()}")
            if hasattr(viewer, 'lbl_orig_ref_x'):
                print(f"✅ TOP REF. Direction: {viewer.lbl_orig_ref_x.text()}, {viewer.lbl_orig_ref_y.text()}, {viewer.lbl_orig_ref_z.text()}")
            
            # Check TOP yellow text overlay
            if hasattr(viewer, 'combined_text_actor_left'):
                top_overlay_text = viewer.combined_text_actor_left.GetInput()
                print(f"✅ TOP yellow overlay: {repr(top_overlay_text)}")
                if "Direction" in top_overlay_text and "REF. Direction" in top_overlay_text:
                    print("✅ TOP overlay shows Direction and REF. Direction correctly")
                else:
                    print("❌ TOP overlay does not show Direction and REF. Direction")
        
        # Load STEP file in BOTTOM viewer
        print(f"\n🔧 Loading STEP file in BOTTOM viewer: {step_file}")
        viewer.active_viewer = "bottom"
        success_bottom = viewer.load_step_file_direct(step_file)
        
        if success_bottom:
            print("✅ STEP file loaded in BOTTOM viewer")
            
            # Check BOTTOM viewer labels
            print("\n🔧 Checking BOTTOM viewer labels...")
            if hasattr(viewer, 'lbl_orig_axis_x_bottom'):
                print(f"✅ BOTTOM Direction: {viewer.lbl_orig_axis_x_bottom.text()}, {viewer.lbl_orig_axis_y_bottom.text()}, {viewer.lbl_orig_axis_z_bottom.text()}")
            else:
                print("❌ BOTTOM Direction labels not found")
            if hasattr(viewer, 'lbl_orig_ref_x_bottom'):
                print(f"✅ BOTTOM REF. Direction: {viewer.lbl_orig_ref_x_bottom.text()}, {viewer.lbl_orig_ref_y_bottom.text()}, {viewer.lbl_orig_ref_z_bottom.text()}")
            else:
                print("❌ BOTTOM REF. Direction labels not found")
            
            # Check BOTTOM yellow text overlay
            if hasattr(viewer, 'combined_text_actor_right'):
                bottom_overlay_text = viewer.combined_text_actor_right.GetInput()
                print(f"✅ BOTTOM yellow overlay: {repr(bottom_overlay_text)}")
                if "Direction" in bottom_overlay_text and "REF. Direction" in bottom_overlay_text:
                    print("✅ BOTTOM overlay shows Direction and REF. Direction correctly")
                else:
                    print("❌ BOTTOM overlay does not show Direction and REF. Direction")
        
        # Check cursor overlays (should only show cursor position)
        print("\n🔧 Checking cursor overlays...")
        if hasattr(viewer, 'cursor_text_actor_left'):
            cursor_text = viewer.cursor_text_actor_left.GetInput()
            print(f"✅ TOP cursor overlay: {repr(cursor_text)}")
            if cursor_text.startswith("CURSOR:") and "Direction" not in cursor_text:
                print("✅ TOP cursor overlay is correct")
            else:
                print("❌ TOP cursor overlay is incorrect")
        
        if hasattr(viewer, 'cursor_text_actor_right'):
            cursor_text = viewer.cursor_text_actor_right.GetInput()
            print(f"✅ BOTTOM cursor overlay: {repr(cursor_text)}")
            if cursor_text.startswith("CURSOR:") and "Direction" not in cursor_text:
                print("✅ BOTTOM cursor overlay is correct")
            else:
                print("❌ BOTTOM cursor overlay is incorrect")
        
        return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== COMPLETE FIX TEST ===")
    success = test_complete_fix()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
