#!/usr/bin/env python3
"""
Debug the exact behavior when loading SOIC file in GUI
This will show exactly what text gets set and why
"""

import sys
import os

def test_exact_gui_behavior():
    """Test exactly what happens when GUI loads SOIC file"""
    print("🔧 Testing exact GUI behavior when loading SOIC file...")
    
    try:
        # Import the actual GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Check initial state
        print("\n🔧 CHECKING INITIAL STATE...")
        if hasattr(viewer, 'cursor_text_actor_left'):
            initial_text = viewer.cursor_text_actor_left.GetInput()
            print(f"Initial text: '{initial_text}'")
        else:
            print("No cursor_text_actor_left initially")
        
        # Set active viewer to TOP
        viewer.active_viewer = "top"
        print("✅ Active viewer set to TOP")
        
        # Load the SOIC file
        print("\n🔧 LOADING SOIC FILE...")
        success = viewer.load_step_file_direct("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Check what text is set after loading
            print("\n🔧 CHECKING TEXT AFTER LOADING...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                after_load_text = viewer.cursor_text_actor_left.GetInput()
                print(f"Text after loading: '{after_load_text}'")
                
                # Check if it contains the correct values
                if "Point: (-4.19, -3.6673, 0.4914)" in after_load_text:
                    print("✅ SUCCESS: Contains correct STEP file values!")
                    return True
                elif "(-4.190000000000000, -3.667300000000000, 0.491400000000000)" in after_load_text:
                    print("❌ FAILURE: Contains old hardcoded values!")
                    print("This means there's still hardcoded values somewhere")
                    return False
                elif "No coordinate system data found" in after_load_text:
                    print("⚠️  WARNING: Shows 'No coordinate system data found'")
                    print("This means the AXIS2_PLACEMENT_3D data is not being found")
                    
                    # Let's check the step_loader directly
                    if hasattr(viewer, 'step_loader_left'):
                        if hasattr(viewer.step_loader_left, 'get_original_axis2_placement'):
                            axis_data = viewer.step_loader_left.get_original_axis2_placement()
                            print(f"Direct step_loader check: {axis_data}")
                            if axis_data:
                                print("❌ FAILURE: Data exists but not being used!")
                            else:
                                print("❌ FAILURE: No data in step_loader!")
                        else:
                            print("❌ FAILURE: get_original_axis2_placement method missing!")
                    else:
                        print("❌ FAILURE: step_loader_left missing!")
                    return False
                else:
                    print(f"⚠️  UNKNOWN: Text contains unexpected content")
                    return False
            else:
                print("❌ cursor_text_actor_left not found after loading")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_step_loader_directly():
    """Check if the step_loader itself is working correctly"""
    print("\n🔧 TESTING STEP_LOADER DIRECTLY...")
    
    try:
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        success, message = loader.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEPLoader loaded file successfully")
            
            if hasattr(loader, 'get_original_axis2_placement'):
                axis_data = loader.get_original_axis2_placement()
                if axis_data:
                    print("✅ STEPLoader has AXIS2_PLACEMENT_3D data:")
                    print(f"   Point: {axis_data['point']}")
                    print(f"   Dir1: {axis_data['dir1']}")
                    print(f"   Dir2: {axis_data['dir2']}")
                    return True
                else:
                    print("❌ STEPLoader has no AXIS2_PLACEMENT_3D data")
                    return False
            else:
                print("❌ STEPLoader missing get_original_axis2_placement method")
                return False
        else:
            print(f"❌ STEPLoader failed to load file: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing STEPLoader: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run exact behavior test"""
    print("🔥 DEBUG EXACT GUI BEHAVIOR 🔥")
    print("=" * 50)
    print("This will show exactly what happens when you load the SOIC file")
    print("=" * 50)
    
    # Test 1: Check step_loader directly
    print("1. TESTING STEP_LOADER DIRECTLY")
    print("-" * 40)
    result1 = check_step_loader_directly()
    
    # Test 2: Test exact GUI behavior
    print("\n2. TESTING EXACT GUI BEHAVIOR")
    print("-" * 40)
    result2 = test_exact_gui_behavior()
    
    # Summary
    print("\n" + "=" * 50)
    print("EXACT BEHAVIOR TEST RESULTS:")
    print("=" * 50)
    print(f"STEPLoader Direct: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"GUI Behavior: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if result1 and result2:
        print("\n🎉 SUCCESS: Everything is working correctly!")
        print("The GUI should show correct Original TOP values when you load SOIC file.")
    elif result1 and not result2:
        print("\n⚠️  STEP_LOADER WORKS BUT GUI DOESN'T")
        print("The step_loader can extract the data but the GUI isn't using it properly.")
        print("There might be a timing issue or the GUI is using cached data.")
    elif not result1:
        print("\n❌ STEP_LOADER ITSELF IS BROKEN")
        print("The step_loader cannot extract AXIS2_PLACEMENT_3D data from the SOIC file.")
    else:
        print("\n❌ BOTH TESTS FAILED")
        print("Multiple issues need to be fixed.")

if __name__ == "__main__":
    main()
