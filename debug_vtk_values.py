#!/usr/bin/env python3
"""
Debug program to identify why text display shows button values instead of VTK transformation values
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class VTKValuesDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def setup_viewer(self):
        """Initialize the viewer"""
        print("🔥🔥🔥 VTK VALUES DEBUGGER STARTING 🔥🔥🔥")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize
        self.app.processEvents()
        time.sleep(2)
        
        print("✅ Viewer initialized")
        
    def debug_button_click_flow(self):
        """Debug the complete flow from button click to text display"""
        print("\n" + "="*80)
        print("🧪 DEBUGGING BUTTON CLICK TO TEXT DISPLAY FLOW")
        print("="*80)
        
        # Step 1: Simulate button click
        print("🔧 STEP 1: Simulating Direction X+ button click...")
        self.viewer.rotate_shape('x', 15)
        
        # Step 2: Check what values are stored
        print("\n🔧 STEP 2: Checking stored values...")
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"✅ current_rot_left: {self.viewer.current_rot_left}")
        else:
            print("❌ current_rot_left: NOT FOUND")
            
        if hasattr(self.viewer, 'direction_values_left'):
            print(f"✅ direction_values_left: {self.viewer.direction_values_left}")
        else:
            print("❌ direction_values_left: NOT FOUND")
            
        if hasattr(self.viewer, 'step_actors_left'):
            print(f"✅ step_actors_left: {len(self.viewer.step_actors_left) if self.viewer.step_actors_left else 0} actors")
        else:
            print("❌ step_actors_left: NOT FOUND")
        
        # Step 3: Call the function that text display uses
        print("\n🔧 STEP 3: Calling get_actual_vtk_transformation_values...")
        actual_values = self.viewer.get_actual_vtk_transformation_values("top")
        print(f"✅ Returned values: {actual_values}")
        
        # Step 4: Check what the text display would show
        print("\n🔧 STEP 4: Checking what text display shows...")
        if actual_values and 'rotation' in actual_values:
            display_x = actual_values['rotation']['x']
            print(f"✅ Text display should show X rotation: {display_x}°")
        else:
            print("❌ No rotation values to display")
            
        # Step 5: Force text update
        print("\n🔧 STEP 5: Forcing text overlay update...")
        self.app.processEvents()
        time.sleep(1)
        
    def debug_vtk_actors(self):
        """Debug VTK actors and their transformations"""
        print("\n" + "="*80)
        print("🧪 DEBUGGING VTK ACTORS AND TRANSFORMATIONS")
        print("="*80)
        
        # Check if we have VTK actors
        if hasattr(self.viewer, 'step_actors_left') and self.viewer.step_actors_left:
            print(f"✅ Found {len(self.viewer.step_actors_left)} VTK actors")
            
            for i, actor in enumerate(self.viewer.step_actors_left):
                print(f"\n🔧 Actor {i}:")
                
                # Check user transform
                user_transform = actor.GetUserTransform()
                if user_transform:
                    print(f"  ✅ Has user transform")
                    matrix = user_transform.GetMatrix()
                    print(f"  📊 Matrix element (0,3): {matrix.GetElement(0, 3)}")
                    print(f"  📊 Matrix element (1,3): {matrix.GetElement(1, 3)}")
                    print(f"  📊 Matrix element (2,3): {matrix.GetElement(2, 3)}")
                else:
                    print(f"  ❌ No user transform")
                
                # Check actor position
                position = actor.GetPosition()
                print(f"  📍 Actor position: {position}")
                
                # Check actor orientation
                orientation = actor.GetOrientation()
                print(f"  🔄 Actor orientation: {orientation}")
        else:
            print("❌ No VTK actors found")
            
    def run_debug(self):
        """Run the complete debug sequence"""
        print("🚀 STARTING VTK VALUES DEBUG SEQUENCE")
        print("="*80)
        
        # Setup
        self.setup_viewer()
        
        # Debug the flow
        self.debug_button_click_flow()
        self.debug_vtk_actors()
        
        # Second button click to see changes
        print("\n" + "="*80)
        print("🧪 SECOND BUTTON CLICK TEST")
        print("="*80)
        self.debug_button_click_flow()
        
        print("\n🔥🔥🔥 DEBUG COMPLETE 🔥🔥🔥")
        print("The issue should now be clear from the debug output above.")
        
        # Keep running for manual testing
        return self.app.exec_()

def main():
    """Main debug function"""
    debugger = VTKValuesDebugger()
    return debugger.run_debug()

if __name__ == "__main__":
    sys.exit(main())
