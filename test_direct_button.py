#!/usr/bin/env python3
"""
Test script to directly call rotate_shape and see debug messages
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer_tdk_modular_fixed import StepViewerTDK

def test_direct_button():
    """Test direct button call"""
    print("🔥🔥🔥 STARTING DIRECT BUTTON TEST 🔥🔥🔥")
    
    app = QApplication(sys.argv)
    
    # Create the main window
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize, then test
    QTimer.singleShot(3000, lambda: run_direct_test(viewer))
    
    # Start the event loop
    app.exec_()

def run_direct_test(viewer):
    """Run direct test of rotate_shape function"""
    print("🔧 DIRECT TEST: Starting direct rotate_shape test...")
    
    # Test 1: Call rotate_shape directly
    print("🔧 DIRECT TEST: Calling viewer.rotate_shape('x', 15) directly...")
    try:
        viewer.rotate_shape('x', 15)
        print("✅ DIRECT TEST: Direct rotate_shape call completed")
    except Exception as e:
        print(f"❌ DIRECT TEST: Direct rotate_shape call failed: {e}")
    
    # Test 2: Check if direction_values_left was created
    if hasattr(viewer, 'direction_values_left'):
        print(f"✅ DIRECT TEST: direction_values_left exists: {viewer.direction_values_left}")
    else:
        print("❌ DIRECT TEST: direction_values_left does NOT exist")
    
    # Test 3: Call get_actual_vtk_transformation_values
    try:
        actual_values = viewer.get_actual_vtk_transformation_values("top")
        print(f"✅ DIRECT TEST: get_actual_vtk_transformation_values returned: {actual_values}")
    except Exception as e:
        print(f"❌ DIRECT TEST: get_actual_vtk_transformation_values failed: {e}")
    
    # Test 4: Call rotate_shape again to test accumulation
    print("🔧 DIRECT TEST: Calling viewer.rotate_shape('x', 15) again...")
    try:
        viewer.rotate_shape('x', 15)
        print("✅ DIRECT TEST: Second direct rotate_shape call completed")
    except Exception as e:
        print(f"❌ DIRECT TEST: Second direct rotate_shape call failed: {e}")
    
    # Test 5: Check final values
    if hasattr(viewer, 'direction_values_left'):
        print(f"✅ DIRECT TEST: Final direction_values_left: {viewer.direction_values_left}")
        expected_x = 30.0  # Should be 15 + 15 = 30
        actual_x = viewer.direction_values_left.get('x', 0)
        if actual_x == expected_x:
            print(f"✅ DIRECT TEST: Accumulation WORKING! Expected {expected_x}°, got {actual_x}°")
        else:
            print(f"❌ DIRECT TEST: Accumulation BROKEN! Expected {expected_x}°, got {actual_x}°")
    
    print("🔥🔥🔥 DIRECT BUTTON TEST COMPLETE 🔥🔥🔥")
    
    # Exit after test
    QTimer.singleShot(2000, lambda: sys.exit(0))

if __name__ == "__main__":
    test_direct_button()
