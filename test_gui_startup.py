#!/usr/bin/env python3
"""
Test GUI startup to catch any errors that cause immediate exit
"""

import sys
import traceback
from PyQt5.QtWidgets import QApplication

def test_gui_startup():
    """Test the GUI startup with error catching"""
    try:
        print("🔥 TESTING GUI STARTUP 🔥")
        print("=" * 50)
        
        # Create QApplication first
        app = QApplication(sys.argv)
        print("✅ QApplication created successfully")
        
        # Import and create the main window
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ StepViewerTDK imported successfully")
        
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK instance created successfully")
        
        viewer.show()
        print("✅ GUI window shown successfully")
        
        print("\n🎉 GUI STARTUP SUCCESSFUL!")
        print("The window should now be visible.")
        print("Press Ctrl+C to exit or close the window.")
        print("=" * 50)
        
        # Run the event loop
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"\n❌ ERROR DURING GUI STARTUP:")
        print(f"Error: {e}")
        print(f"Error type: {type(e).__name__}")
        print("\nFull traceback:")
        traceback.print_exc()
        print("\n" + "=" * 50)
        print("GUI startup failed. Check the error above.")
        return False

if __name__ == "__main__":
    test_gui_startup()
