#!/usr/bin/env python3
"""
Comprehensive Rotation Test Program
Tests that model rotation works correctly with:
1. Model rotation (visual)
2. Bounding box rotation 
3. Origin marker rotation
4. Origin position value updates
5. Both mouse and button interactions
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

# Import the main viewer
from step_viewer import StepViewerTDK

class ComprehensiveRotationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = {}
        
    def run_comprehensive_test(self):
        print("🔧 COMPREHENSIVE ROTATION TEST")
        print("=" * 80)
        print("Testing: Model, Bounding Box, Origin Markers, Position Values")
        print("Methods: Mouse rotation and Button rotation")
        print("=" * 80)
        
        # Create viewer
        print("1. Creating viewer and loading model...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Load STEP file
        QTimer.singleShot(1000, self.load_model)
        
        # Start the application
        self.app.exec_()
        
    def load_model(self):
        print("2. Loading STEP file...")
        
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print("✅ STEP file loaded successfully")
        QTimer.singleShot(1000, self.check_initial_state)
        
    def check_initial_state(self):
        print("\n3. Checking initial state...")
        
        # Check if model is loaded
        model_loaded = False
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'step_actors') and self.viewer.vtk_renderer_left.step_actors:
                model_loaded = True
                print(f"   ✅ Model loaded: {len(self.viewer.vtk_renderer_left.step_actors)} actors")
            elif hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
                model_loaded = True
                print("   ✅ Model loaded: single actor")
        
        if not model_loaded:
            print("   ❌ No model actors found")
            self.app.quit()
            return
            
        # Check initial origin position
        if hasattr(self.viewer, 'current_pos_left'):
            self.initial_origin = self.viewer.current_pos_left.copy()
            print(f"   ✅ Initial origin position: {self.initial_origin}")
        else:
            print("   ❌ No initial origin position found")
            self.initial_origin = {'x': 0, 'y': 0, 'z': 0}
            
        # Enable bounding box for testing
        print("   🔧 Enabling bounding box for testing...")
        if hasattr(self.viewer, 'toggle_bbox_overlay'):
            self.viewer.toggle_bbox_overlay()

        # Enable origin markers for testing
        print("   🔧 Ensuring origin markers are visible...")
        if hasattr(self.viewer, 'create_origin_overlay'):
            self.viewer.create_origin_overlay()
            
        QTimer.singleShot(2000, self.test_button_rotation)
        
    def test_button_rotation(self):
        print("\n4. Testing BUTTON rotation...")
        print("   Testing 30° X-axis rotation via button...")
        
        # Apply button rotation
        self.viewer.rotate_shape('x', 30)
        
        # Wait for rotation to complete
        QTimer.singleShot(1000, self.check_button_results)
        
    def check_button_results(self):
        print("   📊 Checking button rotation results...")
        
        # Check origin position update
        origin_updated = False
        if hasattr(self.viewer, 'current_pos_left'):
            new_origin = self.viewer.current_pos_left
            x_changed = abs(new_origin['x'] - self.initial_origin['x']) > 0.001
            y_changed = abs(new_origin['y'] - self.initial_origin['y']) > 0.001
            z_changed = abs(new_origin['z'] - self.initial_origin['z']) > 0.001
            
            if x_changed or y_changed or z_changed:
                origin_updated = True
                print(f"   ✅ Origin position updated: {new_origin}")
                self.button_origin = new_origin.copy()
            else:
                print(f"   ❌ Origin position NOT updated: {new_origin}")
                self.button_origin = self.initial_origin.copy()
        
        # Check if bounding box exists and appears rotated
        bbox_rotated = False
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'bbox_actor') and self.viewer.vtk_renderer_left.bbox_actor:
                print("   ✅ Bounding box actor exists")
                bbox_rotated = True
            else:
                print("   ❌ No bounding box actor found")
        
        # Store button test results
        self.test_results['button_origin_update'] = origin_updated
        self.test_results['button_bbox_present'] = bbox_rotated
        
        print(f"   📋 Button rotation summary:")
        print(f"      Origin updated: {'✅' if origin_updated else '❌'}")
        print(f"      Bounding box present: {'✅' if bbox_rotated else '❌'}")
        
        QTimer.singleShot(1000, self.test_mouse_rotation)
        
    def test_mouse_rotation(self):
        print("\n5. Testing MOUSE rotation...")
        print("   Simulating mouse drag rotation (45° azimuth)...")
        
        try:
            # Simulate mouse rotation by manipulating camera
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'renderer'):
                    camera = self.viewer.vtk_renderer_left.renderer.GetActiveCamera()
                    if camera:
                        # Store initial camera position
                        initial_pos = camera.GetPosition()
                        print(f"   📍 Initial camera position: {initial_pos}")
                        
                        # Simulate mouse drag rotation
                        camera.Azimuth(45)  # Rotate around Y axis
                        camera.OrthogonalizeViewUp()
                        
                        # Get new camera position
                        new_pos = camera.GetPosition()
                        print(f"   📍 New camera position: {new_pos}")
                        
                        # Trigger interaction event (simulates mouse interaction)
                        if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                            self.viewer.vtk_renderer_left.interactor.InvokeEvent('InteractionEvent')
                        
                        # Force render
                        if hasattr(self.viewer.vtk_renderer_left, 'render_window'):
                            self.viewer.vtk_renderer_left.render_window.Render()
                        
                        print("   ✅ Mouse rotation simulation complete")
                        
        except Exception as e:
            print(f"   ❌ Error during mouse rotation: {e}")
        
        QTimer.singleShot(1000, self.check_mouse_results)
        
    def check_mouse_results(self):
        print("   📊 Checking mouse rotation results...")
        
        # Check origin position update
        origin_updated = False
        if hasattr(self.viewer, 'current_pos_left'):
            mouse_origin = self.viewer.current_pos_left
            x_changed = abs(mouse_origin['x'] - self.button_origin['x']) > 0.001
            y_changed = abs(mouse_origin['y'] - self.button_origin['y']) > 0.001
            z_changed = abs(mouse_origin['z'] - self.button_origin['z']) > 0.001
            
            if x_changed or y_changed or z_changed:
                origin_updated = True
                print(f"   ✅ Origin position updated: {mouse_origin}")
                print(f"      Change from button: X={mouse_origin['x'] - self.button_origin['x']:.3f}, Y={mouse_origin['y'] - self.button_origin['y']:.3f}, Z={mouse_origin['z'] - self.button_origin['z']:.3f}")
            else:
                print(f"   ❌ Origin position NOT updated: {mouse_origin}")
        
        # Store mouse test results
        self.test_results['mouse_origin_update'] = origin_updated
        
        print(f"   📋 Mouse rotation summary:")
        print(f"      Origin updated: {'✅' if origin_updated else '❌'}")
        
        QTimer.singleShot(1000, self.test_visual_elements)
        
    def test_visual_elements(self):
        print("\n6. Testing visual elements...")
        
        # Test model visibility
        model_visible = False
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            renderer = self.viewer.vtk_renderer_left.renderer
            if renderer:
                actors = renderer.GetActors()
                actors.InitTraversal()
                actor_count = 0
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break
                    if actor.GetVisibility():
                        actor_count += 1
                
                if actor_count > 0:
                    model_visible = True
                    print(f"   ✅ Model visible: {actor_count} visible actors")
                else:
                    print("   ❌ No visible actors found")
        
        # Test bounding box visibility
        bbox_visible = False
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'bbox_actor'):
                bbox_actor = self.viewer.vtk_renderer_left.bbox_actor
                if bbox_actor and bbox_actor.GetVisibility():
                    bbox_visible = True
                    print("   ✅ Bounding box visible")
                else:
                    print("   ❌ Bounding box not visible")
        
        # Test origin markers
        origin_markers_visible = False
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            # Check for origin overlay actors
            renderer = self.viewer.vtk_renderer_left.renderer
            if renderer:
                actors = renderer.GetActors()
                actors.InitTraversal()
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break
                    # Look for origin-related actors (typically colored red or green)
                    prop = actor.GetProperty()
                    if prop:
                        color = prop.GetColor()
                        # Check for red (world origin) or green (part origin) markers
                        if (color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2) or \
                           (color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2):
                            origin_markers_visible = True
                            break
                
                if origin_markers_visible:
                    print("   ✅ Origin markers visible")
                else:
                    print("   ❌ Origin markers not visible")
        
        # Store visual test results
        self.test_results['model_visible'] = model_visible
        self.test_results['bbox_visible'] = bbox_visible
        self.test_results['origin_markers_visible'] = origin_markers_visible
        
        QTimer.singleShot(1000, self.final_assessment)

    def final_assessment(self):
        print("\n" + "=" * 80)
        print("COMPREHENSIVE ROTATION TEST RESULTS")
        print("=" * 80)

        # Count successes
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)

        print(f"📊 OVERALL SCORE: {passed_tests}/{total_tests} tests passed")
        print()

        # Detailed results
        print("📋 DETAILED RESULTS:")
        test_names = {
            'button_origin_update': 'Button rotation updates origin position',
            'button_bbox_present': 'Bounding box present after button rotation',
            'mouse_origin_update': 'Mouse rotation updates origin position',
            'model_visible': 'Model is visible',
            'bbox_visible': 'Bounding box is visible',
            'origin_markers_visible': 'Origin markers are visible'
        }

        for test_key, test_name in test_names.items():
            if test_key in self.test_results:
                result = self.test_results[test_key]
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   {status}: {test_name}")

        print()

        # Overall assessment
        if passed_tests == total_tests:
            print("🎉 OVERALL RESULT: ALL TESTS PASSED!")
            print("✅ Model rotation system is working correctly")
            print("✅ Both mouse and button rotations work")
            print("✅ Visual elements (model, bounding box, origin markers) are functioning")
            print("✅ Origin position values update correctly")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️ OVERALL RESULT: MOSTLY WORKING")
            print("✅ Most functionality is working correctly")
            print("⚠️ Some minor issues detected - see failed tests above")
        else:
            print("❌ OVERALL RESULT: SIGNIFICANT ISSUES")
            print("❌ Multiple systems are not working correctly")
            print("🔧 Requires investigation and fixes")

        print("\n" + "=" * 80)
        print("TEST COMPLETE - You can now manually test the GUI")
        print("Try rotating with mouse drag and rotation buttons to verify!")
        print("=" * 80)

        # Keep GUI open for manual testing
        print("\n⏳ GUI will remain open for 30 seconds for manual testing...")
        QTimer.singleShot(30000, self.app.quit)

if __name__ == '__main__':
    test = ComprehensiveRotationTest()
    test.run_comprehensive_test()
