#!/usr/bin/env python3
"""
Simple test to understand what's happening with mouse rotation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class SimpleMouseRotationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def start_test(self):
        """Start the test"""
        print("🔍 SIMPLE MOUSE ROTATION TEST")
        print("=" * 40)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(2000, self.load_and_test)
        
        return self.app.exec_()
        
    def load_and_test(self):
        """Load model and test"""
        print("🔧 Loading model...")
        
        try:
            # Load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                return
                
            # Wait for model to load, then analyze
            QTimer.singleShot(3000, self.analyze_current_state)
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            
    def analyze_current_state(self):
        """Analyze what's currently happening"""
        print("\n🔍 CURRENT STATE ANALYSIS")
        print("=" * 30)
        
        try:
            # Check interactor style
            if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                interactor = self.viewer.vtk_renderer_left.interactor
                if interactor:
                    style = interactor.GetInteractorStyle()
                    style_name = style.__class__.__name__
                    print(f"🖱️  Interactor Style: {style_name}")
                    
                    if "TrackballActor" in style_name:
                        print("✅ Using TrackballActor (rotates model)")
                    elif "TrackballCamera" in style_name:
                        print("⚠️  Using TrackballCamera (rotates camera)")
                    else:
                        print(f"❓ Unknown style: {style_name}")
                else:
                    print("❌ No interactor found")
            else:
                print("❌ No interactor available")
                
            # Check model actors
            model_actors = []
            if hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
                model_actors.append(self.viewer.vtk_renderer_left.step_actor)
                print(f"📦 Single model actor found")
            if hasattr(self.viewer.vtk_renderer_left, 'step_actors') and self.viewer.vtk_renderer_left.step_actors:
                model_actors.extend(self.viewer.vtk_renderer_left.step_actors)
                print(f"📦 Multi-model actors found: {len(self.viewer.vtk_renderer_left.step_actors)}")
                
            if model_actors:
                first_actor = model_actors[0]
                orientation = first_actor.GetOrientation()
                position = first_actor.GetPosition()
                print(f"📦 Model orientation: X={orientation[0]:.1f}° Y={orientation[1]:.1f}° Z={orientation[2]:.1f}°")
                print(f"📦 Model position: X={position[0]:.3f} Y={position[1]:.3f} Z={position[2]:.3f}")
            else:
                print("❌ No model actors found")
                
            # Check origin actors
            if hasattr(self.viewer.vtk_renderer_left, 'origin_actors'):
                count = len(self.viewer.vtk_renderer_left.origin_actors)
                print(f"🔴 World origin actors: {count}")
            else:
                print("❌ No world origin actors")
                
            # Check part origin actors
            part_origin_count = 0
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere') and self.viewer.vtk_renderer_left.part_origin_sphere:
                part_origin_count += 1
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_x_arrow') and self.viewer.vtk_renderer_left.part_origin_x_arrow:
                part_origin_count += 1
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_y_arrow') and self.viewer.vtk_renderer_left.part_origin_y_arrow:
                part_origin_count += 1
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_z_arrow') and self.viewer.vtk_renderer_left.part_origin_z_arrow:
                part_origin_count += 1
                
            if part_origin_count > 0:
                print(f"🟢 Part origin actors: {part_origin_count}")
                if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere') and self.viewer.vtk_renderer_left.part_origin_sphere:
                    sphere = self.viewer.vtk_renderer_left.part_origin_sphere
                    pos = sphere.GetPosition()
                    orient = sphere.GetOrientation()
                    print(f"🟢 Part origin sphere position: X={pos[0]:.3f} Y={pos[1]:.3f} Z={pos[2]:.3f}")
                    print(f"🟢 Part origin sphere orientation: X={orient[0]:.1f}° Y={orient[1]:.1f}° Z={orient[2]:.1f}°")
            else:
                print("❌ No part origin actors")
                
            print("\n🖱️  TEST INSTRUCTIONS:")
            print("1. Try rotating the model with your mouse")
            print("2. Watch what happens to each element:")
            print("   📦 Model (should rotate)")
            print("   🔴 Red world origin (should stay fixed)")
            print("   🟢 Green part origin (should rotate with model)")
            print("3. Check if text overlay updates")
            print("4. Press Ctrl+C when done")
            
        except Exception as e:
            print(f"❌ Error analyzing state: {e}")

if __name__ == "__main__":
    test = SimpleMouseRotationTest()
    try:
        sys.exit(test.start_test())
    except KeyboardInterrupt:
        print("\n👋 Test completed by user")
        sys.exit(0)
