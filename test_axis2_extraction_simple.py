#!/usr/bin/env python3
"""
Simple test to check AXIS2_PLACEMENT_3D extraction from STEP file
"""

from step_loader import <PERSON><PERSON>Loader
import os

def test_axis2_extraction():
    """Test AXIS2_PLACEMENT_3D extraction"""
    step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    if not os.path.exists(step_file):
        print(f'❌ STEP file not found: {step_file}')
        return False
    
    print(f'✅ Found STEP file: {step_file}')
    
    # Create loader and load file
    loader = STEPLoader()
    print('🔧 Loading STEP file...')
    success, message = loader.load_step_file(step_file)
    
    print(f'Load result: success={success}, message={message}')
    
    # Test the get_original_axis2_placement method
    if hasattr(loader, 'get_original_axis2_placement'):
        print('✅ get_original_axis2_placement method exists')
        axis_data = loader.get_original_axis2_placement()
        
        if axis_data:
            print('✅ AXIS2_PLACEMENT_3D data extracted successfully!')
            print(f'   Point: {axis_data.get("point", "NOT FOUND")}')
            print(f'   Dir1: {axis_data.get("dir1", "NOT FOUND")}')
            print(f'   Dir2: {axis_data.get("dir2", "NOT FOUND")}')
            
            # Test the text formatting that would be used in GUI
            original_axis_text = f"""
Original top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
            print(f'\n🔧 Formatted text for GUI:{original_axis_text}')
            return True
        else:
            print('❌ axis_data is None - no AXIS2_PLACEMENT_3D data found')
            return False
    else:
        print('❌ get_original_axis2_placement method not found')
        return False

if __name__ == "__main__":
    print("=== AXIS2_PLACEMENT_3D EXTRACTION TEST ===")
    success = test_axis2_extraction()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
