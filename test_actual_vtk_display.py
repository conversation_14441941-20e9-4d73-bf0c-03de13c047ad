#!/usr/bin/env python3
"""
Test script to demonstrate the difference between button tracking values 
and actual VTK transformation matrix values in the display
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

def test_vtk_display_values():
    """Test that display shows actual VTK transformation values, not button tracking values"""
    print("🔥🔥🔥 TESTING ACTUAL VTK DISPLAY VALUES 🔥🔥🔥")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Check if we have a test STEP file
    test_files = [
        "SOIC16P127_1270X940X610L89X51.STEP",
        "test.step",
        "test.STEP"
    ]
    
    step_file = None
    for filename in test_files:
        if os.path.exists(filename):
            step_file = filename
            break
    
    if step_file:
        print(f"🔧 Loading STEP file: {step_file}")
        
        # Load the STEP file into TOP viewer
        try:
            viewer.active_viewer = "top"
            success = viewer.step_loader_left.load_step_file(step_file)
            if success:
                print(f"✅ STEP file loaded successfully")
                
                # Display the model in the TOP viewer
                viewer.vtk_renderer_left.display_step_model(
                    viewer.step_loader_left.shapes,
                    viewer.step_loader_left.colors
                )
                print(f"✅ Model displayed in TOP viewer")
                
                # Wait a moment for the model to be fully loaded
                QTimer.singleShot(1000, lambda: test_rotation_display(viewer))
                
            else:
                print(f"❌ Failed to load STEP file")
                test_without_model(viewer)
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
            test_without_model(viewer)
    else:
        print(f"🔧 No STEP file found, testing without model")
        test_without_model(viewer)
    
    # Start the application
    app.exec_()

def test_rotation_display(viewer):
    """Test rotation display with loaded model"""
    print("\n" + "="*80)
    print("🔧 TESTING ROTATION DISPLAY WITH LOADED MODEL")
    print("="*80)
    
    # Test 1: Check initial display values
    print("\n🔧 TEST 1: Initial display values")
    initial_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"✅ Initial VTK values: {initial_values}")
    
    # Test 2: Apply button rotation and check display
    print("\n🔧 TEST 2: Apply X+15° rotation and check display")
    viewer.rotate_shape('x', 15)
    
    # Get the values that will be displayed
    display_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"✅ Display VTK values after X+15°: {display_values}")
    
    # Test 3: Apply another rotation
    print("\n🔧 TEST 3: Apply Y+30° rotation and check display")
    viewer.rotate_shape('y', 30)
    
    display_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"✅ Display VTK values after Y+30°: {display_values}")
    
    # Test 4: Check button tracking values vs display values
    print("\n🔧 TEST 4: Compare button tracking vs display values")
    if hasattr(viewer, 'current_rot_left'):
        button_values = viewer.current_rot_left
        print(f"📊 Button tracking values: {button_values}")
        print(f"📊 Display VTK values: {display_values['rotation']}")
        
        # Check if they're different (which is what we want)
        if button_values != display_values['rotation']:
            print("✅ SUCCESS: Display shows actual VTK values, not button tracking values!")
        else:
            print("⚠️  WARNING: Display still shows button tracking values")
    
    print("\n" + "="*80)
    print("🔥 ROTATION DISPLAY TEST COMPLETE")
    print("="*80)

def test_without_model(viewer):
    """Test rotation display without loaded model"""
    print("\n" + "="*80)
    print("🔧 TESTING ROTATION DISPLAY WITHOUT MODEL")
    print("="*80)
    
    # Test button rotation without model
    print("\n🔧 TEST: Apply X+15° rotation without model")
    viewer.rotate_shape('x', 15)
    
    # Get the values that will be displayed
    display_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"✅ Display VTK values (no model): {display_values}")
    
    # Check button tracking values
    if hasattr(viewer, 'current_rot_left'):
        button_values = viewer.current_rot_left
        print(f"📊 Button tracking values: {button_values}")
        print(f"📊 Display VTK values: {display_values['rotation']}")
        
        print("✅ Without model: Display correctly shows zero VTK values")
        print("✅ Button tracking still works for internal logic")
    
    print("\n" + "="*80)
    print("🔥 NO-MODEL DISPLAY TEST COMPLETE")
    print("="*80)

if __name__ == "__main__":
    test_vtk_display_values()
