#!/usr/bin/env python3
"""
Fix for VTK Actor Rotation Extraction
This demonstrates how to properly extract rotation values from VTK actors
that have been transformed using RotateWXYZ()
"""

import vtk
import numpy as np
import math

def extract_rotation_from_vtk_actor(actor):
    """
    Extract rotation angles from a VTK actor's transformation matrix
    Returns rotation angles in degrees as {'x': rx, 'y': ry, 'z': rz}
    """
    try:
        # Get the actor's transformation matrix
        transform = actor.GetUserTransform()
        if not transform:
            # If no user transform, check the actor's matrix directly
            matrix = actor.GetMatrix()
        else:
            matrix = transform.GetMatrix()
        
        if not matrix:
            print("❌ No transformation matrix found")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Convert VTK matrix to numpy array for easier manipulation
        np_matrix = np.zeros((4, 4))
        for i in range(4):
            for j in range(4):
                np_matrix[i][j] = matrix.GetElement(i, j)
        
        print(f"🔧 VTK Actor transformation matrix:")
        print(np_matrix)
        
        # Extract rotation angles from the 3x3 rotation part of the matrix
        # Using ZYX Euler angle extraction (common in 3D graphics)
        
        # Extract the 3x3 rotation matrix
        R = np_matrix[:3, :3]
        
        # Calculate rotation angles (in radians)
        # ZYX Euler angles: R = Rz(γ) * Ry(β) * Rx(α)
        
        # Extract Y rotation (β)
        sin_y = -R[2, 0]
        sin_y = max(-1.0, min(1.0, sin_y))  # Clamp to [-1, 1]
        y_rad = math.asin(sin_y)
        
        # Check for gimbal lock
        if abs(math.cos(y_rad)) > 1e-6:
            # No gimbal lock
            x_rad = math.atan2(R[2, 1], R[2, 2])
            z_rad = math.atan2(R[1, 0], R[0, 0])
        else:
            # Gimbal lock case
            x_rad = math.atan2(-R[1, 2], R[1, 1])
            z_rad = 0.0
        
        # Convert to degrees
        x_deg = math.degrees(x_rad)
        y_deg = math.degrees(y_rad)
        z_deg = math.degrees(z_rad)
        
        rotation = {'x': x_deg, 'y': y_deg, 'z': z_deg}
        print(f"✅ Extracted rotation from VTK actor: {rotation}")
        
        return rotation
        
    except Exception as e:
        print(f"❌ Error extracting rotation from VTK actor: {e}")
        import traceback
        traceback.print_exc()
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}

def extract_rotation_from_multiple_actors(actors):
    """
    Extract rotation from multiple VTK actors (for multi-color models)
    Assumes all actors have the same transformation
    """
    if not actors or len(actors) == 0:
        print("❌ No actors provided")
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    # Use the first actor's transformation (they should all be the same)
    first_actor = actors[0]
    rotation = extract_rotation_from_vtk_actor(first_actor)
    
    print(f"🔧 Extracted rotation from {len(actors)} actors using first actor")
    return rotation

def test_rotation_extraction():
    """
    Test the rotation extraction with a sample VTK actor
    """
    print("🧪 Testing VTK Actor Rotation Extraction")
    print("=" * 50)
    
    # Create a test actor
    sphere = vtk.vtkSphereSource()
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(sphere.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    
    # Apply some test rotations
    print("🔧 Applying test rotations: X=30°, Y=45°, Z=60°")
    actor.RotateWXYZ(-30, 1, 0, 0)  # X rotation
    actor.RotateWXYZ(-45, 0, 1, 0)  # Y rotation  
    actor.RotateWXYZ(-60, 0, 0, 1)  # Z rotation
    
    # Extract the rotations
    extracted = extract_rotation_from_vtk_actor(actor)
    
    print(f"🎯 Expected: X=30°, Y=45°, Z=60°")
    print(f"🎯 Extracted: X={extracted['x']:.1f}°, Y={extracted['y']:.1f}°, Z={extracted['z']:.1f}°")
    
    return extracted

if __name__ == "__main__":
    test_rotation_extraction()
