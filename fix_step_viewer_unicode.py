#!/usr/bin/env python3
"""
Fix Unicode characters in step_viewer.py to resolve console encoding errors
"""

import os
import re

def fix_unicode_in_step_viewer():
    """Remove all Unicode characters from step_viewer.py"""
    
    filename = 'step_viewer.py'
    
    if not os.path.exists(filename):
        print(f"ERROR: {filename} not found")
        return False
    
    try:
        # Read file with UTF-8
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"Original file size: {len(content)} characters")
        
        # Common Unicode characters to replace
        unicode_replacements = {
            '🔧': 'DEBUG',
            '✅': 'OK',
            '❌': 'FAIL',
            '⚠️': 'WARN',
            '🔍': 'DEBUG',
            '📁': 'FILE',
            '🎉': 'SUCCESS',
            '💡': 'INFO',
            '⭐': 'STAR',
            '🚀': 'START',
            '🛠️': 'TOOL',
            '📊': 'DATA',
            '🎯': 'TARGET',
            '🔴': 'RED',
            '🔵': 'BLUE',
            '🟢': 'GREEN',
            '🟡': 'YELLOW',
            '🟠': 'ORANGE',
            '🟣': 'PURPLE',
            '⚡': 'FAST',
            '🔥': 'FIRE',
            '🧪': 'TEST',
            '📋': 'LIST',
            '⏳': 'WAIT',
            '🏁': 'FINISH',
            '🗑️': 'DELETE',
            '°': 'deg',  # Degree symbol
            '🖱️': 'MOUSE',  # Mouse emoji
            '🖱': 'MOUSE',   # Mouse emoji variant
            '🚨': 'ALERT',   # Alert emoji
            '📅': 'DATE',    # Calendar emoji
            '→': '->',       # Right arrow
            '═': '=',        # Double horizontal line
            '️': '',         # Variation selector (invisible)
            '📺': 'SCREEN',  # TV/Screen emoji
            '•': '*',        # Bullet point
            '🎮': 'GAME',    # Game controller emoji
            '📍': 'PIN',     # Pin/Location emoji
            '👁': 'EYE',     # Eye emoji
            '🔄': 'REFRESH', # Refresh/Reload emoji
        }
        
        # Replace Unicode characters
        original_content = content
        replacements_made = 0
        
        for unicode_char, replacement in unicode_replacements.items():
            if unicode_char in content:
                count = content.count(unicode_char)
                content = content.replace(unicode_char, replacement)
                replacements_made += count
                print(f"Replaced {count} instances of '{unicode_char}' with '{replacement}'")
        
        # Check for any remaining non-ASCII characters
        non_ascii_found = []
        for i, char in enumerate(content):
            if ord(char) > 127:
                non_ascii_found.append((i, char, ord(char)))
        
        if non_ascii_found:
            print(f"WARNING: Found {len(non_ascii_found)} remaining non-ASCII characters:")
            for pos, char, code in non_ascii_found[:10]:  # Show first 10
                print(f"  Position {pos}: '{char}' (code {code})")
                # Replace any remaining non-ASCII with safe alternatives
                if char == '°':
                    content = content.replace(char, 'deg')
                else:
                    content = content.replace(char, '?')
        
        # Write back if changed
        if content != original_content:
            # Create backup first
            backup_name = f"{filename}.unicode_backup"
            with open(backup_name, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"Backup created: {backup_name}")
            
            # Write cleaned version
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"SUCCESS: Fixed {replacements_made} Unicode characters in {filename}")
            print(f"New file size: {len(content)} characters")
            return True
        else:
            print(f"No Unicode characters found in {filename}")
            return True
            
    except Exception as e:
        print(f"ERROR: Failed to fix {filename} - {e}")
        return False

if __name__ == "__main__":
    print("=== FIXING UNICODE CHARACTERS IN STEP_VIEWER.PY ===")
    success = fix_unicode_in_step_viewer()
    if success:
        print("=== UNICODE FIX COMPLETED SUCCESSFULLY ===")
    else:
        print("=== UNICODE FIX FAILED ===")
