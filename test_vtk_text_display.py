#!/usr/bin/env python3
"""
Test VTK text display to isolate the text rendering issue
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class VTKTextTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VTK Text Display Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
        layout.addWidget(self.vtk_widget)
        
        # Setup VTK
        self.setup_vtk()
        
        # Timer to update text
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_text)
        self.timer.start(2000)  # Update every 2 seconds
        
        self.counter = 0
        
    def setup_vtk(self):
        """Setup VTK renderer and text actors"""
        # Create renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.1)  # Dark gray background
        
        # Create render window
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        
        # Create multiple text actors to test different positioning methods
        
        # Test 1: Normalized coordinates at top
        self.text_actor_1 = vtk.vtkTextActor()
        self.text_actor_1.SetInput("TEST 1: Normalized Top (Original top data should appear here)")
        self.text_actor_1.GetTextProperty().SetFontSize(16)
        self.text_actor_1.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
        self.text_actor_1.GetTextProperty().SetBold(True)
        self.text_actor_1.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
        self.text_actor_1.SetPosition(0.02, 0.95)  # Top left
        self.renderer.AddActor2D(self.text_actor_1)
        
        # Test 2: Pixel coordinates at bottom
        self.text_actor_2 = vtk.vtkTextActor()
        self.text_actor_2.SetInput("TEST 2: Pixel Bottom")
        self.text_actor_2.GetTextProperty().SetFontSize(16)
        self.text_actor_2.GetTextProperty().SetColor(0.0, 1.0, 0.0)  # Green
        self.text_actor_2.GetTextProperty().SetBold(True)
        self.text_actor_2.SetPosition(10, 10)  # Bottom left in pixels
        self.renderer.AddActor2D(self.text_actor_2)
        
        # Test 3: Middle of screen
        self.text_actor_3 = vtk.vtkTextActor()
        self.text_actor_3.SetInput("TEST 3: Middle Screen")
        self.text_actor_3.GetTextProperty().SetFontSize(20)
        self.text_actor_3.GetTextProperty().SetColor(1.0, 0.0, 0.0)  # Red
        self.text_actor_3.GetTextProperty().SetBold(True)
        self.text_actor_3.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
        self.text_actor_3.SetPosition(0.5, 0.5)  # Center
        self.renderer.AddActor2D(self.text_actor_3)
        
        # Create a simple cube for reference
        cube_source = vtk.vtkCubeSource()
        cube_mapper = vtk.vtkPolyDataMapper()
        cube_mapper.SetInputConnection(cube_source.GetOutputPort())
        cube_actor = vtk.vtkActor()
        cube_actor.SetMapper(cube_mapper)
        cube_actor.GetProperty().SetColor(0.5, 0.5, 1.0)  # Light blue
        self.renderer.AddActor(cube_actor)
        
        # Setup camera
        self.renderer.ResetCamera()
        
        print("✅ VTK setup complete with 3 test text actors")
        
    def update_text(self):
        """Update text actors with new content"""
        self.counter += 1
        
        # Simulate the Original top data
        original_top_text = f"""CURSOR: X=3.46 Y=-10.49 Z=3.05
Original top:
Point: (-4.19, -3.6673, 0.4914)
Dir1: (0.0, 0.9104, -0.4138)
Dir2: (0.0, 0.4138, 0.9104)
Update #{self.counter}"""
        
        self.text_actor_1.SetInput(original_top_text)
        self.text_actor_2.SetInput(f"Bottom Text Update #{self.counter}")
        self.text_actor_3.SetInput(f"Center Text #{self.counter}")
        
        # Force render
        self.render_window.Render()
        
        print(f"🔧 Text updated #{self.counter}")
        print(f"🔧 Text actor 1 input: {repr(self.text_actor_1.GetInput())}")
        print(f"🔧 Text actor 1 visibility: {self.text_actor_1.GetVisibility()}")
        print(f"🔧 Text actor 1 position: {self.text_actor_1.GetPosition()}")

def main():
    app = QApplication(sys.argv)
    
    window = VTKTextTest()
    window.show()
    
    # Start the Qt event loop
    app.exec_()

if __name__ == "__main__":
    main()
