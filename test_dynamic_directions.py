#!/usr/bin/env python3
"""
Test dynamic direction vector transformation for bottom yellow text
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_dynamic_directions():
    """Test that direction vectors transform correctly with model rotation"""
    print("🔧 Testing dynamic direction vector transformation...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load STEP file to get original direction vectors
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file: {step_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Get original direction vectors from STEP file
            if hasattr(viewer, 'step_loader_left'):
                axis_data = viewer.step_loader_left.get_original_axis2_placement()
                if axis_data:
                    original_dir1 = axis_data['dir1']
                    original_dir2 = axis_data['dir2']
                    print(f"🔧 Original Dir1 (Direction): {original_dir1}")
                    print(f"🔧 Original Dir2 (REF. Direction): {original_dir2}")
                    
                    # Test transformation function with zero rotation (should be unchanged)
                    print(f"\n🔧 Testing transformation with zero rotation...")
                    zero_rotation = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                    transformed_dir1_zero = viewer._transform_direction_vector(original_dir1, zero_rotation)
                    transformed_dir2_zero = viewer._transform_direction_vector(original_dir2, zero_rotation)
                    
                    print(f"Zero rotation - Dir1: {original_dir1} -> {transformed_dir1_zero}")
                    print(f"Zero rotation - Dir2: {original_dir2} -> {transformed_dir2_zero}")
                    
                    # Check if vectors are unchanged (within tolerance)
                    tolerance = 0.001
                    dir1_unchanged = all(abs(a - b) < tolerance for a, b in zip(original_dir1, transformed_dir1_zero))
                    dir2_unchanged = all(abs(a - b) < tolerance for a, b in zip(original_dir2, transformed_dir2_zero))
                    
                    if dir1_unchanged and dir2_unchanged:
                        print("✅ GOOD: Zero rotation preserves original vectors")
                    else:
                        print("❌ PROBLEM: Zero rotation changes vectors")
                    
                    # Test transformation with 90-degree X rotation
                    print(f"\n🔧 Testing transformation with 90° X rotation...")
                    x_rotation = {'x': 90.0, 'y': 0.0, 'z': 0.0}
                    transformed_dir1_x90 = viewer._transform_direction_vector(original_dir1, x_rotation)
                    transformed_dir2_x90 = viewer._transform_direction_vector(original_dir2, x_rotation)
                    
                    print(f"90° X rotation - Dir1: {original_dir1} -> {transformed_dir1_x90}")
                    print(f"90° X rotation - Dir2: {original_dir2} -> {transformed_dir2_x90}")
                    
                    # Test transformation with 90-degree Y rotation
                    print(f"\n🔧 Testing transformation with 90° Y rotation...")
                    y_rotation = {'x': 0.0, 'y': 90.0, 'z': 0.0}
                    transformed_dir1_y90 = viewer._transform_direction_vector(original_dir1, y_rotation)
                    transformed_dir2_y90 = viewer._transform_direction_vector(original_dir2, y_rotation)
                    
                    print(f"90° Y rotation - Dir1: {original_dir1} -> {transformed_dir1_y90}")
                    print(f"90° Y rotation - Dir2: {original_dir2} -> {transformed_dir2_y90}")
                    
                    # Test transformation with 90-degree Z rotation
                    print(f"\n🔧 Testing transformation with 90° Z rotation...")
                    z_rotation = {'x': 0.0, 'y': 0.0, 'z': 90.0}
                    transformed_dir1_z90 = viewer._transform_direction_vector(original_dir1, z_rotation)
                    transformed_dir2_z90 = viewer._transform_direction_vector(original_dir2, z_rotation)
                    
                    print(f"90° Z rotation - Dir1: {original_dir1} -> {transformed_dir1_z90}")
                    print(f"90° Z rotation - Dir2: {original_dir2} -> {transformed_dir2_z90}")
                    
                    # Test current rotation values
                    print(f"\n🔧 Testing with current model rotation...")
                    print(f"Current TOP rotation: {viewer.current_rot_left}")
                    current_dir1 = viewer._transform_direction_vector(original_dir1, viewer.current_rot_left)
                    current_dir2 = viewer._transform_direction_vector(original_dir2, viewer.current_rot_left)
                    
                    print(f"Current rotation - Dir1: {original_dir1} -> {current_dir1}")
                    print(f"Current rotation - Dir2: {original_dir2} -> {current_dir2}")
                    
                    # Test text overlay update
                    print(f"\n🔧 Testing text overlay update...")
                    viewer.update_text_overlays()
                    
                    # Check if combined text actor has the transformed values
                    if hasattr(viewer, 'combined_text_actor_left'):
                        text_content = viewer.combined_text_actor_left.GetInput()
                        print(f"TOP combined text content: {repr(text_content)}")
                        
                        # Check if the text contains the transformed direction values
                        if "Direction" in text_content and "REF. Direction" in text_content:
                            print("✅ GOOD: Text contains Direction and REF. Direction")
                            
                            # Check if values are dynamic (different from original if rotation is applied)
                            if viewer.current_rot_left['x'] != 0 or viewer.current_rot_left['y'] != 0 or viewer.current_rot_left['z'] != 0:
                                # Model has rotation, check if text shows transformed values
                                original_text = f"Direction (X - {original_dir1[0]:.2f} Y - {original_dir1[1]:.2f} Z - {original_dir1[2]:.2f})"
                                if original_text not in text_content:
                                    print("✅ EXCELLENT: Text shows transformed values (not original static values)")
                                else:
                                    print("❌ PROBLEM: Text still shows original static values")
                            else:
                                print("✅ GOOD: No rotation applied, text should show original values")
                        else:
                            print("❌ PROBLEM: Text does not contain Direction and REF. Direction")
                    else:
                        print("❌ PROBLEM: combined_text_actor_left not found")
                    
                    return True
                else:
                    print("❌ No AXIS2_PLACEMENT_3D data found")
                    return False
            else:
                print("❌ step_loader_left not found")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== DYNAMIC DIRECTION VECTORS TEST ===")
    success = test_dynamic_directions()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
