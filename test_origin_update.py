#!/usr/bin/env python3
"""
Test script to debug Origin number updates during rotation
This script will automatically test the rotation and origin update functionality
"""

import sys
import os
sys.path.append('.')

from PyQt5.QtWidgets import QApplication
from step_viewer import StepViewerTDK
import vtk

# Create QApplication for Qt widgets
app = QApplication(sys.argv)

def test_origin_update():
    """Test if Origin numbers update when model is rotated"""
    print("="*60)
    print("TESTING ORIGIN NUMBER UPDATES DURING ROTATION")
    print("="*60)
    
    try:
        # Create viewer instance
        print("1. Creating viewer instance...")
        viewer = StepViewerTDK()
        
        # Load a STEP file
        print("2. Loading STEP file...")
        test_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
            
        # Set active viewer to top first
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(test_file)
        if not success:
            print("❌ Failed to load STEP file")
            return False
        print("✅ STEP file loaded successfully")
        
        # Check initial Origin values
        print("\n3. Checking initial Origin values...")
        if hasattr(viewer, 'current_pos_left'):
            initial_origin = viewer.current_pos_left.copy()
            print(f"   Initial Origin: X={initial_origin['x']:.3f}, Y={initial_origin['y']:.3f}, Z={initial_origin['z']:.3f}")
        else:
            print("❌ No current_pos_left found")
            return False
            
        # Apply a rotation
        print("\n4. Applying rotation (30° on X-axis)...")
        print("   Before rotation:")
        print(f"     current_pos_left: {viewer.current_pos_left}")
        
        # Call the rotation function directly
        viewer.rotate_shape("x", 30)
        
        print("   After rotation:")
        print(f"     current_pos_left: {viewer.current_pos_left}")
        
        # Check if Origin values changed
        print("\n5. Checking if Origin values changed...")
        if hasattr(viewer, 'current_pos_left'):
            final_origin = viewer.current_pos_left.copy()
            print(f"   Final Origin: X={final_origin['x']:.3f}, Y={final_origin['y']:.3f}, Z={final_origin['z']:.3f}")

            # Compare values
            x_changed = abs(final_origin['x'] - initial_origin['x']) > 0.001
            y_changed = abs(final_origin['y'] - initial_origin['y']) > 0.001
            z_changed = abs(final_origin['z'] - initial_origin['z']) > 0.001

            print(f"\n6. Results:")
            print(f"   X changed: {'✅' if x_changed else '❌'} ({initial_origin['x']:.3f} → {final_origin['x']:.3f})")
            print(f"   Y changed: {'✅' if y_changed else '❌'} ({initial_origin['y']:.3f} → {final_origin['y']:.3f})")
            print(f"   Z changed: {'✅' if z_changed else '❌'} ({initial_origin['z']:.3f} → {final_origin['z']:.3f})")

            # ADDITIONAL TEST: Check if text display would show updated values
            print(f"\n7. Testing text display generation...")
            try:
                # Simulate what the text overlay system does
                def format_value(val):
                    return f"= {val:.3f}"

                origin_text = f"Origin (X {format_value(viewer.current_pos_left['x'])} Y {format_value(viewer.current_pos_left['y'])} Z {format_value(viewer.current_pos_left['z'])})"
                print(f"   Generated text: {origin_text}")

                # Check if the text contains the new values
                text_has_new_values = (
                    f"{final_origin['x']:.3f}" in origin_text and
                    f"{final_origin['y']:.3f}" in origin_text and
                    f"{final_origin['z']:.3f}" in origin_text
                )
                print(f"   Text contains new values: {'✅' if text_has_new_values else '❌'}")

            except Exception as e:
                print(f"   ❌ Error generating text: {e}")

            if x_changed or y_changed or z_changed:
                print("\n🎉 SUCCESS: Origin numbers updated after rotation!")
                return True
            else:
                print("\n❌ PROBLEM: Origin numbers did NOT change after rotation!")
                return False
        else:
            print("❌ No current_pos_left found after rotation")
            return False
            
    except Exception as e:
        print(f"❌ ERROR during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_transform_creation():
    """Test if UserTransform is being created properly"""
    print("\n" + "="*60)
    print("TESTING USER TRANSFORM CREATION")
    print("="*60)
    
    try:
        # Create viewer instance
        viewer = StepViewerTDK()
        
        # Load a STEP file
        test_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
            
        # Set active viewer to top first
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(test_file)
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        # Check if step_actor exists
        print("1. Checking step_actor...")
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actor') and renderer.step_actor:
            step_actor = renderer.step_actor
            print("✅ step_actor found")
            
            # Check initial UserTransform
            initial_transform = step_actor.GetUserTransform()
            print(f"   Initial UserTransform: {initial_transform}")
            
            # Apply rotation
            print("2. Applying rotation...")
            viewer.rotate_shape("x", 30)
            
            # Check UserTransform after rotation
            final_transform = step_actor.GetUserTransform()
            print(f"   Final UserTransform: {final_transform}")
            
            if final_transform:
                print("✅ UserTransform created successfully")
                
                # Get transformation matrix
                matrix = vtk.vtkMatrix4x4()
                final_transform.GetMatrix(matrix)
                print("   Transformation matrix:")
                for i in range(4):
                    row = [matrix.GetElement(i, j) for j in range(4)]
                    print(f"     [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
                    
                return True
            else:
                print("❌ UserTransform not created")
                return False
        else:
            print("❌ step_actor not found")
            return False
            
    except Exception as e:
        print(f"❌ ERROR during UserTransform test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 AUTOMATED ORIGIN UPDATE DEBUG TEST")
    print("This script will test if Origin numbers update when rotating the model")
    
    # Test 1: Origin update
    test1_success = test_origin_update()
    
    # Test 2: UserTransform creation
    test2_success = test_user_transform_creation()
    
    print("\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    print(f"Origin Update Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"UserTransform Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL TESTS PASSED! Origin numbers should update correctly.")
    else:
        print("\n❌ TESTS FAILED! Origin numbers are not updating properly.")
        print("\nDEBUG INFO:")
        print("- Check if _update_origin_position_after_rotation() is being called")
        print("- Check if UserTransform is being created correctly")
        print("- Check if current_pos_left is being updated")
    
    sys.exit(0 if (test1_success and test2_success) else 1)
