#!/usr/bin/env python3
"""
Test script to debug AXIS2_PLACEMENT_3D extraction from STEP files
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_step_loader():
    """Test the STEPLoader AXIS2_PLACEMENT_3D extraction"""
    print("🔧 Testing STEPLoader AXIS2_PLACEMENT_3D extraction...")
    
    try:
        from step_loader import STEPLoader
        print("✅ STEPLoader imported successfully")
        
        # Create loader instance
        loader = STEPLoader()
        print("✅ STEPLoader instance created")
        
        # Test file path
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"✅ STEP file found: {step_file}")
        
        # Test direct extraction method
        print("\n🔧 Testing direct _extract_first_axis2_placement method...")
        axis_data = loader._extract_first_axis2_placement(step_file)
        
        if axis_data:
            print("✅ AXIS2_PLACEMENT_3D extraction successful!")
            print(f"   Point: {axis_data['point']}")
            print(f"   Dir1: {axis_data['dir1']}")
            print(f"   Dir2: {axis_data['dir2']}")
        else:
            print("❌ AXIS2_PLACEMENT_3D extraction failed!")
            return False
            
        # Test full load_step_file method
        print("\n🔧 Testing full load_step_file method...")
        success, message = loader.load_step_file(step_file)
        print(f"Load result: success={success}, message={message}")
        
        # Test get_original_axis2_placement method
        print("\n🔧 Testing get_original_axis2_placement method...")
        stored_axis_data = loader.get_original_axis2_placement()
        
        if stored_axis_data:
            print("✅ Stored AXIS2_PLACEMENT_3D data retrieved!")
            print(f"   Point: {stored_axis_data['point']}")
            print(f"   Dir1: {stored_axis_data['dir1']}")
            print(f"   Dir2: {stored_axis_data['dir2']}")
        else:
            print("❌ No stored AXIS2_PLACEMENT_3D data found!")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing STEPLoader: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_viewer_integration():
    """Test integration with main viewer"""
    print("\n🔧 Testing main viewer integration...")
    
    try:
        # Import main viewer
        import step_viewer_tdk_modular_fixed
        print("✅ Main viewer imported successfully")
        
        # Test if we can access the step_loader attributes
        print("✅ Main viewer integration test complete")
        return True
        
    except Exception as e:
        print(f"❌ Error testing main viewer integration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 AXIS2_PLACEMENT_3D Extraction Debug Test")
    print("=" * 50)
    
    # Test 1: STEPLoader functionality
    test1_result = test_step_loader()
    
    # Test 2: Main viewer integration
    test2_result = test_main_viewer_integration()
    
    print("\n" + "=" * 50)
    print("🔧 TEST RESULTS:")
    print(f"   STEPLoader test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Main viewer test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed - debugging needed")
