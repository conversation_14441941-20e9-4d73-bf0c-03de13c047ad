#!/usr/bin/env python3
"""
Debug script to test text overlay updates during mouse rotation
This will help identify why text doesn't update in real-time during mouse dragging
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class TextUpdateDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        
    def start_test(self):
        """Start the debugging test"""
        print("🧪 STARTING TEXT UPDATE DURING MOUSE ROTATION DEBUG TEST")
        print("=" * 60)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize, then start test
        QTimer.singleShot(2000, self.begin_test)
        
        return self.app.exec_()
        
    def begin_test(self):
        """Begin the actual test sequence"""
        print("🔧 GUI initialized, starting test sequence...")
        self.timer.start(3000)  # Run test steps every 3 seconds
        
    def run_test_step(self):
        """Run each test step"""
        if self.test_step == 0:
            print("\n=== STEP 1: Load test STEP file ===")
            # Try to load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found, creating a simple test model")
                # We'll continue anyway to test the text update mechanism
                
        elif self.test_step == 1:
            print("\n=== STEP 2: Check initial text overlay state ===")
            self.check_text_overlay_state("INITIAL")
            
        elif self.test_step == 2:
            print("\n=== STEP 3: Test VTK observer setup ===")
            self.check_vtk_observers()
            
        elif self.test_step == 3:
            print("\n=== STEP 4: Test manual text update ===")
            print("Calling update_text_overlays() manually...")
            self.viewer.update_text_overlays()
            self.check_text_overlay_state("AFTER MANUAL UPDATE")
            
        elif self.test_step == 4:
            print("\n=== STEP 5: Test get_actual_vtk_transformation_values ===")
            self.test_vtk_value_extraction()
            
        elif self.test_step == 5:
            print("\n=== STEP 6: Simulate mouse interaction event ===")
            self.simulate_mouse_interaction()
            
        elif self.test_step == 6:
            print("\n=== STEP 7: Test timer-based updates ===")
            self.test_timer_updates()
            
        elif self.test_step == 7:
            print("\n=== TEST COMPLETE ===")
            print("🎯 SUMMARY:")
            print("- Check the console output above for any issues")
            print("- The text overlay should update when mouse interactions occur")
            print("- If text is not updating, the issue is likely in the VTK observer setup")
            self.timer.stop()
            QTimer.singleShot(3000, self.app.quit)
            
        self.test_step += 1
        
    def check_text_overlay_state(self, stage):
        """Check the current state of text overlays"""
        print(f"🔍 Checking text overlay state at {stage}:")
        
        # Check TOP viewer text overlay
        if hasattr(self.viewer, 'combined_text_actor_left'):
            text_content = self.viewer.combined_text_actor_left.GetInput()
            visibility = self.viewer.combined_text_actor_left.GetVisibility()
            print(f"  TOP text overlay: Visible={visibility}, Content='{text_content}'")
        else:
            print("  ❌ TOP text overlay (combined_text_actor_left) not found")
            
        # Check cursor text overlay
        if hasattr(self.viewer, 'cursor_text_actor_left'):
            cursor_content = self.viewer.cursor_text_actor_left.GetInput()
            cursor_visibility = self.viewer.cursor_text_actor_left.GetVisibility()
            print(f"  TOP cursor overlay: Visible={cursor_visibility}, Content='{cursor_content}'")
        else:
            print("  ❌ TOP cursor overlay (cursor_text_actor_left) not found")
            
        # Check BOTTOM viewer text overlay
        if hasattr(self.viewer, 'combined_text_actor_right'):
            bottom_text = self.viewer.combined_text_actor_right.GetInput()
            bottom_visibility = self.viewer.combined_text_actor_right.GetVisibility()
            print(f"  BOTTOM text overlay: Visible={bottom_visibility}, Content='{bottom_text}'")
        else:
            print("  ❌ BOTTOM text overlay (combined_text_actor_right) not found")
            
    def check_vtk_observers(self):
        """Check if VTK observers are properly set up"""
        print("🔍 Checking VTK observer setup:")
        
        # Check TOP viewer observers
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            if hasattr(self.viewer.vtk_renderer_left, 'interactor') and self.viewer.vtk_renderer_left.interactor:
                print("  ✅ TOP viewer interactor found")
                # Try to get observer information (this is tricky in VTK)
                interactor = self.viewer.vtk_renderer_left.interactor
                print(f"  TOP interactor type: {type(interactor)}")
            else:
                print("  ❌ TOP viewer interactor not found")
        else:
            print("  ❌ TOP viewer renderer not found")
            
        # Check BOTTOM viewer observers  
        if hasattr(self.viewer, 'vtk_renderer_right') and self.viewer.vtk_renderer_right:
            if hasattr(self.viewer.vtk_renderer_right, 'interactor') and self.viewer.vtk_renderer_right.interactor:
                print("  ✅ BOTTOM viewer interactor found")
            else:
                print("  ❌ BOTTOM viewer interactor not found")
        else:
            print("  ❌ BOTTOM viewer renderer not found")
            
    def test_vtk_value_extraction(self):
        """Test the VTK value extraction method"""
        print("🔍 Testing VTK value extraction:")
        
        try:
            top_values = self.viewer.get_actual_vtk_transformation_values("top")
            print(f"  TOP VTK values: {top_values}")
        except Exception as e:
            print(f"  ❌ Error getting TOP VTK values: {e}")
            
        try:
            bottom_values = self.viewer.get_actual_vtk_transformation_values("bottom")
            print(f"  BOTTOM VTK values: {bottom_values}")
        except Exception as e:
            print(f"  ❌ Error getting BOTTOM VTK values: {e}")
            
    def simulate_mouse_interaction(self):
        """Simulate a mouse interaction event"""
        print("🔍 Simulating mouse interaction:")
        
        try:
            # Call the mouse interaction handler directly
            if hasattr(self.viewer, 'on_mouse_interaction_left'):
                print("  Calling on_mouse_interaction_left...")
                self.viewer.on_mouse_interaction_left(None, 'InteractionEvent')
                self.check_text_overlay_state("AFTER SIMULATED MOUSE INTERACTION")
            else:
                print("  ❌ on_mouse_interaction_left method not found")
        except Exception as e:
            print(f"  ❌ Error simulating mouse interaction: {e}")
            
    def test_timer_updates(self):
        """Test if the timer-based updates are working"""
        print("🔍 Testing timer-based updates:")
        
        if hasattr(self.viewer, 'display_update_timer'):
            if self.viewer.display_update_timer.isActive():
                print("  ✅ Display update timer is active")
                print(f"  Timer interval: {self.viewer.display_update_timer.interval()}ms")
            else:
                print("  ❌ Display update timer is not active")
        else:
            print("  ❌ Display update timer not found")

if __name__ == "__main__":
    debugger = TextUpdateDebugger()
    sys.exit(debugger.start_test())
