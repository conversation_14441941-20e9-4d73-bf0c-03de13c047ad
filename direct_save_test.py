#!/usr/bin/env python3
"""
Direct Save Test - Test save functionality without GUI dialogs
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class DirectSaveTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'direct_save_test.STEP'
        
    def run_test(self):
        print("🔧 DIRECT SAVE TEST")
        print("=" * 50)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_and_transform)
        self.app.exec_()
        
    def step1_load_and_transform(self):
        print("\n1. Loading original file and applying transformation...")
        
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        
        # Apply transformation
        self.viewer.rotate_shape('x', 45)
        print("✅ Applied 45° X rotation")
        
        # Get TOP display values
        top_display = self.viewer.combined_text_actor_left.GetInput()
        print(f"📺 TOP DISPLAY: {top_display}")
        
        QTimer.singleShot(1000, self.step2_save_file_direct)
        
    def step2_save_file_direct(self):
        print("\n2. Saving file directly...")
        
        try:
            # Get the loader and current values
            loader = self.viewer.step_loader_left
            current_pos = self.viewer.current_pos_left
            current_rot = self.viewer.current_rot_left
            orig_pos = self.viewer.orig_pos_left
            orig_rot = self.viewer.orig_rot_left
            
            print(f"📊 SAVE VALUES:")
            print(f"   Current Pos: {current_pos}")
            print(f"   Current Rot: {current_rot}")
            print(f"   Original Pos: {orig_pos}")
            print(f"   Original Rot: {orig_rot}")
            
            # Call the internal save method directly
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"✅ Saved to {self.saved_file}")
            else:
                print("❌ Save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            import traceback
            traceback.print_exc()
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.step3_load_saved_file)
        
    def step3_load_saved_file(self):
        print("\n3. Loading saved file into BOTTOM viewer...")
        
        # Set active viewer to bottom
        self.viewer.active_viewer = "bottom"
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        
        if not success:
            print(f"❌ Failed to load {self.saved_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.saved_file} into BOTTOM viewer")
        
        QTimer.singleShot(1000, self.step4_compare_displays)
        
    def step4_compare_displays(self):
        print("\n4. Comparing TOP and BOTTOM displays...")
        
        # Get display values from both viewers
        top_display = self.viewer.combined_text_actor_left.GetInput()
        bottom_display = self.viewer.combined_text_actor_right.GetInput()
        
        print(f"\n📺 FINAL COMPARISON:")
        print(f"   TOP:    {top_display}")
        print(f"   BOTTOM: {bottom_display}")
        
        # Extract and compare values
        import re
        
        # Extract Origin values
        origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        top_origin = re.search(origin_pattern, top_display)
        bottom_origin = re.search(origin_pattern, bottom_display)
        
        # Extract Direction values
        direction_pattern = r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        ref_direction_pattern = r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        
        top_dir = re.search(direction_pattern, top_display)
        top_ref = re.search(ref_direction_pattern, top_display)
        bottom_dir = re.search(direction_pattern, bottom_display)
        bottom_ref = re.search(ref_direction_pattern, bottom_display)
        
        print(f"\n🔧 DETAILED COMPARISON:")
        
        # Compare Origins
        origin_match = False
        if top_origin and bottom_origin:
            top_o = [float(top_origin.group(i)) for i in range(1, 4)]
            bottom_o = [float(bottom_origin.group(i)) for i in range(1, 4)]
            origin_match = all(abs(top_o[i] - bottom_o[i]) < 0.01 for i in range(3))
            print(f"   Origin Match: {origin_match}")
            if not origin_match:
                print(f"     TOP Origin:    {top_o}")
                print(f"     BOTTOM Origin: {bottom_o}")
        
        # Compare Directions
        dir_match = False
        if top_dir and bottom_dir:
            top_d = [float(top_dir.group(i)) for i in range(1, 4)]
            bottom_d = [float(bottom_dir.group(i)) for i in range(1, 4)]
            dir_match = all(abs(top_d[i] - bottom_d[i]) < 0.01 for i in range(3))
            print(f"   Direction Match: {dir_match}")
            if not dir_match:
                print(f"     TOP Direction:    {top_d}")
                print(f"     BOTTOM Direction: {bottom_d}")
        
        # Compare REF. Directions
        ref_match = False
        if top_ref and bottom_ref:
            top_r = [float(top_ref.group(i)) for i in range(1, 4)]
            bottom_r = [float(bottom_ref.group(i)) for i in range(1, 4)]
            ref_match = all(abs(top_r[i] - bottom_r[i]) < 0.01 for i in range(3))
            print(f"   REF. Direction Match: {ref_match}")
            if not ref_match:
                print(f"     TOP REF. Direction:    {top_r}")
                print(f"     BOTTOM REF. Direction: {bottom_r}")
        
        # Overall assessment
        if origin_match and dir_match and ref_match:
            print(f"\n🎉 SUCCESS: All values match! Save functionality is working correctly.")
        else:
            print(f"\n❌ FAILURE: Values don't match. Save functionality needs fixing.")
            
            if not origin_match:
                print(f"   ❌ Origin values don't match")
            if not dir_match:
                print(f"   ❌ Direction values don't match")
            if not ref_match:
                print(f"   ❌ REF. Direction values don't match")
        
        QTimer.singleShot(3000, self.cleanup_and_exit)
        
    def cleanup_and_exit(self):
        print("\n" + "=" * 50)
        print("DIRECT SAVE TEST COMPLETE")
        print("=" * 50)
        
        self.app.quit()

if __name__ == '__main__':
    tester = DirectSaveTest()
    tester.run_test()
