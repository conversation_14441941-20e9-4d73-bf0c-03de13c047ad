#!/usr/bin/env python3
"""
COMPREHENSIVE ORIGIN UPDATE DEBUGGING
This script tests every single step of the origin update process to find exactly where it fails
"""

import sys
import os
sys.path.append('.')

from PyQt5.QtWidgets import QApplication
from step_viewer import <PERSON>ViewerTDK
import vtk
import time

# Create QApplication for Qt widgets
app = QApplication(sys.argv)

def test_complete_origin_update_workflow():
    """Test every single step of the origin update process"""
    print("="*80)
    print("COMPREHENSIVE ORIGIN UPDATE DEBUGGING")
    print("="*80)
    
    try:
        # Step 1: Create viewer
        print("\n1. Creating viewer instance...")
        viewer = StepViewerTDK()
        viewer.active_viewer = "top"
        print("✅ Viewer created")
        
        # Step 2: Load STEP file
        print("\n2. Loading STEP file...")
        test_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
            
        success = viewer.load_step_file_direct(test_file)
        if not success:
            print("❌ Failed to load STEP file")
            return False
        print("✅ STEP file loaded")
        
        # Step 3: Check initial state
        print("\n3. Checking initial state...")
        initial_pos = viewer.current_pos_left.copy()
        print(f"   Initial current_pos_left: {initial_pos}")
        
        # Step 4: Check text overlay system
        print("\n4. Testing text overlay generation BEFORE rotation...")
        def format_value(val):
            return f"= {val:.3f}"
        
        initial_text = f"Origin (X {format_value(initial_pos['x'])} Y {format_value(initial_pos['y'])} Z {format_value(initial_pos['z'])})"
        print(f"   Initial text would be: {initial_text}")
        
        # Step 5: Apply rotation and track every step
        print("\n5. Applying rotation with detailed tracking...")
        print("   BEFORE rotation:")
        print(f"     current_pos_left: {viewer.current_pos_left}")
        
        # Call rotate_shape and monitor what happens
        print("   Calling viewer.rotate_shape('x', 30)...")
        viewer.rotate_shape('x', 30)
        
        print("   AFTER rotation:")
        print(f"     current_pos_left: {viewer.current_pos_left}")
        
        # Step 6: Check if values actually changed
        print("\n6. Checking if values changed...")
        final_pos = viewer.current_pos_left.copy()
        
        x_changed = abs(final_pos['x'] - initial_pos['x']) > 0.001
        y_changed = abs(final_pos['y'] - initial_pos['y']) > 0.001
        z_changed = abs(final_pos['z'] - initial_pos['z']) > 0.001
        
        print(f"   X changed: {'✅' if x_changed else '❌'} ({initial_pos['x']:.3f} → {final_pos['x']:.3f})")
        print(f"   Y changed: {'✅' if y_changed else '❌'} ({initial_pos['y']:.3f} → {final_pos['y']:.3f})")
        print(f"   Z changed: {'✅' if z_changed else '❌'} ({initial_pos['z']:.3f} → {final_pos['z']:.3f})")
        
        if not (x_changed or y_changed or z_changed):
            print("❌ PROBLEM: current_pos_left values did not change!")
            return False
        
        # Step 7: Test text generation with new values
        print("\n7. Testing text generation with NEW values...")
        final_text = f"Origin (X {format_value(final_pos['x'])} Y {format_value(final_pos['y'])} Z {format_value(final_pos['z'])})"
        print(f"   Final text should be: {final_text}")
        
        # Step 8: Check if text overlay system would use new values
        print("\n8. Testing if text overlay system uses new values...")
        
        # Simulate what update_text_overlays() does
        if hasattr(viewer, 'current_pos_left'):
            simulated_origin_text = f"Origin (X {format_value(viewer.current_pos_left['x'])} Y {format_value(viewer.current_pos_left['y'])} Z {format_value(viewer.current_pos_left['z'])})"
            print(f"   Simulated origin text: {simulated_origin_text}")
            
            # Check if it contains the new values
            contains_new_x = f"{final_pos['x']:.3f}" in simulated_origin_text
            contains_new_y = f"{final_pos['y']:.3f}" in simulated_origin_text
            contains_new_z = f"{final_pos['z']:.3f}" in simulated_origin_text
            
            print(f"   Contains new X: {'✅' if contains_new_x else '❌'}")
            print(f"   Contains new Y: {'✅' if contains_new_y else '❌'}")
            print(f"   Contains new Z: {'✅' if contains_new_z else '❌'}")
            
            if not (contains_new_x and contains_new_y and contains_new_z):
                print("❌ PROBLEM: Text generation doesn't use new values!")
                return False
        
        # Step 9: Test actual VTK text actor update
        print("\n9. Testing VTK text actor update...")
        
        if hasattr(viewer, 'combined_text_actor_left'):
            # Get current text from VTK actor
            current_vtk_text = viewer.combined_text_actor_left.GetInput()
            print(f"   Current VTK text: {current_vtk_text}")
            
            # Check if VTK text contains new values
            vtk_has_new_x = f"{final_pos['x']:.3f}" in current_vtk_text
            vtk_has_new_y = f"{final_pos['y']:.3f}" in current_vtk_text
            vtk_has_new_z = f"{final_pos['z']:.3f}" in current_vtk_text
            
            print(f"   VTK text has new X: {'✅' if vtk_has_new_x else '❌'}")
            print(f"   VTK text has new Y: {'✅' if vtk_has_new_y else '❌'}")
            print(f"   VTK text has new Z: {'✅' if vtk_has_new_z else '❌'}")
            
            if not (vtk_has_new_x and vtk_has_new_y and vtk_has_new_z):
                print("❌ PROBLEM: VTK text actor doesn't have new values!")
                print("   This means update_text_overlays() is not working correctly")
                return False
        else:
            print("❌ PROBLEM: combined_text_actor_left not found!")
            return False
        
        # Step 10: Force another update and check again
        print("\n10. Testing manual text overlay update...")
        print("   Calling update_text_overlays() manually...")
        viewer.update_text_overlays()
        
        # Check VTK text again
        updated_vtk_text = viewer.combined_text_actor_left.GetInput()
        print(f"   Updated VTK text: {updated_vtk_text}")
        
        # Final verification
        final_vtk_has_new_x = f"{final_pos['x']:.3f}" in updated_vtk_text
        final_vtk_has_new_y = f"{final_pos['y']:.3f}" in updated_vtk_text
        final_vtk_has_new_z = f"{final_pos['z']:.3f}" in updated_vtk_text
        
        print(f"   Final VTK text has new X: {'✅' if final_vtk_has_new_x else '❌'}")
        print(f"   Final VTK text has new Y: {'✅' if final_vtk_has_new_y else '❌'}")
        print(f"   Final VTK text has new Z: {'✅' if final_vtk_has_new_z else '❌'}")
        
        if final_vtk_has_new_x and final_vtk_has_new_y and final_vtk_has_new_z:
            print("\n🎉 SUCCESS: All steps working correctly!")
            print("   The origin numbers should be updating in the GUI")
            return True
        else:
            print("\n❌ FAILURE: VTK text actor still doesn't have new values after manual update")
            print("   The problem is in the update_text_overlays() function")
            return False
            
    except Exception as e:
        print(f"❌ ERROR during comprehensive test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 COMPREHENSIVE ORIGIN UPDATE DEBUGGING")
    print("This script will test every step to find exactly where the origin update fails")
    
    success = test_complete_origin_update_workflow()
    
    print("\n" + "="*80)
    print("FINAL DIAGNOSIS")
    print("="*80)
    
    if success:
        print("✅ ALL TESTS PASSED!")
        print("   The origin update system should be working correctly")
        print("   If GUI still doesn't show updates, the problem is elsewhere")
    else:
        print("❌ TESTS FAILED!")
        print("   The debug output above shows exactly where the problem is")
        print("   Look for the last ❌ PROBLEM message to see what's broken")
    
    sys.exit(0 if success else 1)
