#!/usr/bin/env python3
"""
Simple test to check if Option 1 save is working with rotations
"""

import sys
import os
import vtk
from PyQt5.QtWidgets import QApplication
from step_viewer import StepViewerTDK

def test_save():
    print("🧪 SIMPLE SAVE TEST")
    print("=" * 50)
    
    # Create application and viewer
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load original file
    original_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return False
        
    print(f"1. Loading original file: {original_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(original_file)
    if not success:
        print("❌ Failed to load original file")
        return False
    print("✅ Original file loaded")
    
    # Apply some rotations
    print("\n2. Applying rotations...")
    viewer.rotate_shape('x', 30)
    viewer.rotate_shape('y', 45) 
    viewer.rotate_shape('z', 60)
    print("✅ Rotations applied")
    
    # Check if UserTransform exists
    print("\n3. Checking UserTransform...")
    step_actor = viewer.vtk_renderer_left.step_actor
    if step_actor:
        user_transform = step_actor.GetUserTransform()
        if user_transform:
            print("✅ UserTransform exists")
            matrix = user_transform.GetMatrix()
            print(f"   Transform matrix element [0,0]: {matrix.GetElement(0,0):.6f}")
        else:
            print("❌ No UserTransform found")
            return False
    else:
        print("❌ No step_actor found")
        return False
    
    # Save with Option 1
    print("\n4. Saving with Option 1...")
    save_file = "test-simple-save.step"

    # Manually call the save method - but it uses file dialog, so let's test the internal logic
    try:
        # Test the internal save logic directly
        loader = viewer.step_loader_left

        # Check if user transform exists
        step_actor = viewer.vtk_renderer_left.step_actor
        user_transform = step_actor.GetUserTransform() if step_actor else None

        if user_transform:
            print("✅ UserTransform detected - applying transformations")

            # Apply transformations to polydata
            import vtk
            transform_filter = vtk.vtkTransformPolyDataFilter()
            transform_filter.SetInputData(loader.current_polydata)
            transform_filter.SetTransform(user_transform)
            transform_filter.Update()
            transformed_polydata = transform_filter.GetOutput()

            # Update the loader's polydata with the transformed version
            original_polydata = loader.current_polydata
            loader.current_polydata = transformed_polydata

            # Get the VTK transformation matrix to pass to the save method
            transform_matrix = vtk.vtkMatrix4x4()
            user_transform.GetMatrix(transform_matrix)

            # Save the transformed geometry to STEP file with transformation matrix
            success = loader.save_step_file(save_file, transform_matrix)

            # Restore original polydata
            loader.current_polydata = original_polydata
        else:
            print("❌ No UserTransform - using direct save")
            success = loader.save_step_file(save_file)
        if success:
            print(f"✅ File saved successfully: {save_file}")
            if os.path.exists(save_file):
                print(f"✅ File exists on disk: {os.path.getsize(save_file)} bytes")
                return True
            else:
                print("❌ File not found on disk")
                return False
        else:
            print("❌ Save failed")
            return False
    except Exception as e:
        print(f"❌ Save error: {e}")
        return False

if __name__ == "__main__":
    success = test_save()
    if success:
        print("\n🎉 TEST PASSED!")

        # Now test loading the saved file to see if it matches
        print("\n" + "="*50)
        print("TESTING LOAD COMPARISON")
        print("="*50)

        # Load the saved file in a new viewer instance
        print("Loading saved file to compare...")
        viewer2 = StepViewerTDK()
        success2 = viewer2.load_step_file("test-simple-save.step")

        if success2:
            print("✅ Saved file loaded successfully")

            # Get the axis data from the loaded file
            loader2 = viewer2.step_loader_left
            if hasattr(loader2, 'axis_data') and loader2.axis_data:
                saved_axis_data = loader2.axis_data
                print(f"📊 SAVED FILE AXIS DATA:")
                print(f"   Point: {saved_axis_data['point']}")
                print(f"   Dir1: {saved_axis_data['dir1']}")
                print(f"   Dir2: {saved_axis_data['dir2']}")

                print(f"\n🎯 CONCLUSION: The saved file should make both viewers look identical when loaded.")
            else:
                print("❌ No axis data found in saved file")
        else:
            print("❌ Failed to load saved file")
    else:
        print("\n❌ TEST FAILED!")
    sys.exit(0 if success else 1)
