#!/usr/bin/env python3
"""
Verification script to check if LEFT button origin fix is working
This runs without GUI and checks the code logic
"""

import sys
import os

def check_rotate_shape_method():
    """Check if rotate_shape method has the correct origin rotation logic"""
    print("🔍 CHECKING rotate_shape METHOD FOR ORIGIN ROTATION FIX")
    print("=" * 60)
    
    try:
        # Read the step_viewer.py file
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the rotate_shape method
        if 'def rotate_shape(self, axis, degrees):' in content:
            print("✅ Found rotate_shape method")
            
            # Check for the fixed origin rotation logic
            if 'origin_actor.RotateWXYZ(-degrees' in content:
                print("✅ Found RotateWXYZ call for origin actors")
                
                # Check if it's using the correct approach (not position delta)
                if 'AddPosition(delta_x, delta_y, delta_z)' not in content.split('def rotate_shape')[1].split('def ')[0]:
                    print("✅ OLD BROKEN LOGIC REMOVED: No AddPosition with delta calculation")
                    
                    # Check for the correct rotation logic
                    rotate_section = content.split('def rotate_shape')[1].split('def ')[0]
                    if 'for origin_actor in self.vtk_renderer_left.origin_actors:' in rotate_section:
                        print("✅ CORRECT LOGIC FOUND: Iterating through origin_actors")
                        
                        if 'origin_actor.RotateWXYZ(-degrees,' in rotate_section:
                            print("✅ CORRECT ROTATION: Using RotateWXYZ with same angle as model")
                            
                            if '1 if axis == \'x\' else 0' in rotate_section:
                                print("✅ CORRECT AXIS HANDLING: Using conditional axis selection")
                                
                                print("\n🎉 LEFT BUTTON ORIGIN FIX IS CORRECTLY IMPLEMENTED!")
                                print("   - Origin actors will now ROTATE with LEFT buttons")
                                print("   - Uses same RotateWXYZ approach as mouse rotation")
                                print("   - Removed broken position delta calculation")
                                return True
                            else:
                                print("❌ MISSING: Conditional axis selection")
                        else:
                            print("❌ MISSING: RotateWXYZ call with correct parameters")
                    else:
                        print("❌ MISSING: Origin actor iteration")
                else:
                    print("❌ OLD BROKEN LOGIC STILL PRESENT: Still using AddPosition with delta")
            else:
                print("❌ MISSING: RotateWXYZ call for origin actors")
        else:
            print("❌ rotate_shape method not found")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False
    
    return False

def check_move_shape_method():
    """Check if move_shape method still has correct origin movement logic"""
    print("\n🔍 CHECKING move_shape METHOD FOR COMPARISON")
    print("=" * 60)
    
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'def move_shape(self, axis, amount):' in content:
            print("✅ Found move_shape method")
            
            move_section = content.split('def move_shape')[1].split('def ')[0]
            
            if 'for origin_actor in self.vtk_renderer_left.origin_actors:' in move_section:
                print("✅ Found origin actor iteration in move_shape")
                
                if 'origin_actor.AddPosition(' in move_section:
                    print("✅ CORRECT: move_shape uses AddPosition (translation)")
                    print("   - RIGHT buttons will MOVE origin actors")
                    return True
                else:
                    print("❌ MISSING: AddPosition call in move_shape")
            else:
                print("❌ MISSING: Origin actor handling in move_shape")
        else:
            print("❌ move_shape method not found")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False
    
    return False

def main():
    print("🔥🔥🔥 VERIFYING LEFT BUTTON ORIGIN FIX 🔥🔥🔥")
    print()
    
    # Check if the fix is implemented correctly
    rotate_fix_ok = check_rotate_shape_method()
    move_still_ok = check_move_shape_method()
    
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY:")
    print("=" * 60)
    
    if rotate_fix_ok:
        print("✅ LEFT BUTTONS (rotate_shape): FIXED - will rotate origin markers")
    else:
        print("❌ LEFT BUTTONS (rotate_shape): NOT FIXED - origin markers won't rotate")
    
    if move_still_ok:
        print("✅ RIGHT BUTTONS (move_shape): WORKING - will move origin markers")
    else:
        print("❌ RIGHT BUTTONS (move_shape): BROKEN - origin markers won't move")
    
    print()
    
    if rotate_fix_ok and move_still_ok:
        print("🎉 SUCCESS: The fix is correctly implemented!")
        print("   - LEFT buttons will ROTATE origin markers (like mouse)")
        print("   - RIGHT buttons will MOVE origin markers (as before)")
        print("   - Both behaviors should now work correctly")
        
        print("\n📋 TO TEST:")
        print("   1. Run: python test_left_buttons_origin_fix.py")
        print("   2. Load STEP file and create origin overlay")
        print("   3. Test LEFT buttons - origin should ROTATE")
        print("   4. Test RIGHT buttons - origin should MOVE")
        
    else:
        print("❌ FAILURE: The fix is not correctly implemented")
        print("   Manual code review and fixes needed")
    
    return rotate_fix_ok and move_still_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
