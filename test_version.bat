@echo off
echo ========================================
echo VERSION TESTER - Copy and Run Versions
echo ========================================
echo.

set /p VERSION="Enter revision number (e.g. 96, 95, 94): "

echo.
echo Testing version %VERSION%...
echo Copying files from ..\save\ directory...
echo.

echo Copying step_viewer_tdk_modular_rev%VERSION%.py to step_viewer_tdk_modular.py
if exist "..\save\step_viewer_tdk_modular_rev%VERSION%.py" (
    copy "..\save\step_viewer_tdk_modular_rev%VERSION%.py" step_viewer_tdk_modular.py
    echo ✓ Copied step_viewer_tdk_modular
) else (
    echo ✗ step_viewer_tdk_modular_rev%VERSION%.py not found in ..\save\
)

echo Copying vtk_renderer_rev%VERSION%.py to vtk_renderer.py
if exist "..\save\vtk_renderer_rev%VERSION%.py" (
    copy "..\save\vtk_renderer_rev%VERSION%.py" vtk_renderer.py
    echo ✓ Copied vtk_renderer
) else (
    echo ✗ vtk_renderer_rev%VERSION%.py not found in ..\save\
)

echo Copying gui_components_rev%VERSION%.py to gui_components.py
if exist "..\save\gui_components_rev%VERSION%.py" (
    copy "..\save\gui_components_rev%VERSION%.py" gui_components.py
    echo ✓ Copied gui_components
) else (
    echo ✗ gui_components_rev%VERSION%.py not found in ..\save\
)

echo Copying step_loader_rev%VERSION%.py to step_loader.py
if exist "..\save\step_loader_rev%VERSION%.py" (
    copy "..\save\step_loader_rev%VERSION%.py" step_loader.py
    echo ✓ Copied step_loader
) else (
    echo ✗ step_loader_rev%VERSION%.py not found in ..\save\
)

echo Copying requirements_rev%VERSION%.txt to requirements.txt
if exist "..\save\requirements_rev%VERSION%.txt" (
    copy "..\save\requirements_rev%VERSION%.txt" requirements.txt
    echo ✓ Copied requirements
) else (
    echo ✗ requirements_rev%VERSION%.txt not found in ..\save\
)

echo.
echo ========================================
echo STARTING REVISION %VERSION%
echo ========================================
echo.
python step_viewer.py

echo.
echo Program finished. Press any key to test another version...
pause
