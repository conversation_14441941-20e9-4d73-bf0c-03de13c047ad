#!/usr/bin/env python3
"""
Test script to verify button cumulative tracking functionality
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer_tdk_modular_fixed import <PERSON><PERSON><PERSON><PERSON>

def test_button_cumulative():
    """Test button cumulative tracking"""
    print("🔥🔥🔥 STARTING BUTTON CUMULATIVE TEST 🔥🔥🔥")
    
    app = QApplication(sys.argv)
    
    # Create the main window
    viewer = StepViewer()
    viewer.show()
    
    # Wait for GUI to initialize
    QTimer.singleShot(2000, lambda: test_buttons(viewer))
    
    # Start the event loop
    app.exec_()

def test_buttons(viewer):
    """Test button functionality"""
    print("🔧 TESTING: Starting button tests...")
    
    # Test 1: Click Direction X+ button (should call rotate_shape)
    print("🔧 TESTING: Clicking Direction X+ button...")
    try:
        viewer.rotate_shape('x', 15)
        print("✅ TESTING: rotate_shape('x', 15) called successfully")
    except Exception as e:
        print(f"❌ TESTING: rotate_shape failed: {e}")
    
    # Test 2: Check cumulative tracking
    print("🔧 TESTING: Checking cumulative tracking variables...")
    if hasattr(viewer, 'cumulative_rotation_left'):
        print(f"✅ TESTING: cumulative_rotation_left exists: {viewer.cumulative_rotation_left}")
    else:
        print("❌ TESTING: cumulative_rotation_left does NOT exist")
    
    # Test 3: Call get_actual_vtk_transformation_values
    print("🔧 TESTING: Calling get_actual_vtk_transformation_values...")
    try:
        actual_values = viewer.get_actual_vtk_transformation_values("top")
        print(f"✅ TESTING: get_actual_vtk_transformation_values returned: {actual_values}")
    except Exception as e:
        print(f"❌ TESTING: get_actual_vtk_transformation_values failed: {e}")
    
    # Test 4: Click another button
    print("🔧 TESTING: Clicking Direction X+ button again...")
    try:
        viewer.rotate_shape('x', 15)
        print("✅ TESTING: Second rotate_shape('x', 15) called successfully")
    except Exception as e:
        print(f"❌ TESTING: Second rotate_shape failed: {e}")
    
    # Test 5: Check cumulative values again
    if hasattr(viewer, 'cumulative_rotation_left'):
        print(f"✅ TESTING: cumulative_rotation_left after second click: {viewer.cumulative_rotation_left}")
        expected_x = 30.0  # Should be 15 + 15 = 30
        actual_x = viewer.cumulative_rotation_left.get('x', 0)
        if actual_x == expected_x:
            print(f"✅ TESTING: Cumulative tracking WORKING! Expected {expected_x}°, got {actual_x}°")
        else:
            print(f"❌ TESTING: Cumulative tracking BROKEN! Expected {expected_x}°, got {actual_x}°")
    
    print("🔥🔥🔥 BUTTON CUMULATIVE TEST COMPLETE 🔥🔥🔥")
    
    # Exit after test
    QTimer.singleShot(1000, lambda: sys.exit(0))

if __name__ == "__main__":
    test_button_cumulative()
