#!/usr/bin/env python3
"""
Test to force refresh of text overlay and check if Dir1/Dir2 values are updating
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_text_overlay_refresh():
    """Test text overlay refresh with STEP file"""
    print("🔧 Testing text overlay refresh...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the SOIC file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file: {step_file}")
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Check the AXIS2_PLACEMENT_3D data directly
            if hasattr(viewer, 'step_loader_left'):
                axis_data = viewer.step_loader_left.get_original_axis2_placement()
                if axis_data:
                    print("\n✅ AXIS2_PLACEMENT_3D data from step_loader:")
                    print(f"   Point: {axis_data['point']}")
                    print(f"   Dir1: {axis_data['dir1']}")
                    print(f"   Dir2: {axis_data['dir2']}")
                    
                    # Force multiple text overlay updates to ensure refresh
                    print("\n🔧 Forcing text overlay updates...")
                    for i in range(3):
                        print(f"   Update #{i+1}:")
                        viewer.update_text_overlays()
                        
                        # Check what the text actor actually contains
                        if hasattr(viewer, 'cursor_text_actor_left'):
                            actual_text = viewer.cursor_text_actor_left.GetInput()
                            print(f"   Actual text in overlay: {repr(actual_text)}")
                            
                            # Check if Dir1 and Dir2 are in the text
                            if "Dir1:" in actual_text and "Dir2:" in actual_text:
                                print("   ✅ Dir1 and Dir2 found in text overlay")
                                # Extract the Dir1 and Dir2 lines
                                lines = actual_text.split('\n')
                                for line in lines:
                                    if line.startswith('Dir1:') or line.startswith('Dir2:'):
                                        print(f"   📝 {line}")
                            else:
                                print("   ❌ Dir1 and Dir2 NOT found in text overlay")
                        else:
                            print("   ❌ cursor_text_actor_left not found")
                    
                    return True
                else:
                    print("❌ No AXIS2_PLACEMENT_3D data found")
                    return False
            else:
                print("❌ step_loader_left not found")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== TEXT OVERLAY REFRESH TEST ===")
    success = test_text_overlay_refresh()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
