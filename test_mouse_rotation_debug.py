#!/usr/bin/env python3
"""
Test program to debug mouse rotation display issues
This isolates the VTK mouse interaction and display update problem
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer
import vtk
from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class MouseRotationTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Mouse Rotation Display Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status labels
        self.status_label = QLabel("Status: No model loaded")
        self.rotation_label = QLabel("VTK Rotation: X=0.00 Y=0.00 Z=0.00")
        layout.addWidget(self.status_label)
        layout.addWidget(self.rotation_label)
        
        # Load test model button
        load_btn = QPushButton("Load Test Cube")
        load_btn.clicked.connect(self.load_test_cube)
        layout.addWidget(load_btn)
        
        # VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # VTK setup
        self.renderer = vtk.vtkRenderer()
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # Test actor
        self.test_actor = None
        
        # Setup custom interaction style
        self.setup_custom_interaction()
        
        # Setup timer for display updates
        self.setup_update_timer()
        
        # Initialize VTK
        self.interactor.Initialize()
        
    def setup_custom_interaction(self):
        """Setup custom VTK interaction style that rotates actors"""
        class TestRotationStyle(vtk.vtkInteractorStyleTrackballCamera):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.last_mouse_pos = None
                self.SetMotionFactor(5.0)
                
            def OnLeftButtonDown(self):
                super().OnLeftButtonDown()
                interactor = self.GetInteractor()
                if interactor:
                    self.last_mouse_pos = interactor.GetEventPosition()
                    print(f"🖱️ Mouse down at: {self.last_mouse_pos}")
                    
            def OnMouseMove(self):
                try:
                    interactor = self.GetInteractor()
                    if interactor and interactor.GetLeftButtonDown() and self.last_mouse_pos:
                        current_pos = interactor.GetEventPosition()
                        dx = current_pos[0] - self.last_mouse_pos[0]
                        dy = current_pos[1] - self.last_mouse_pos[1]
                        
                        if abs(dx) > 1 or abs(dy) > 1:
                            print(f"🖱️ Mouse drag: dx={dx}, dy={dy}")
                            self.parent.rotate_test_actor(dx, dy)
                            self.last_mouse_pos = current_pos
                    else:
                        super().OnMouseMove()
                except Exception as e:
                    print(f"❌ Mouse move error: {e}")
                    super().OnMouseMove()
        
        style = TestRotationStyle(self)
        self.interactor.SetInteractorStyle(style)
        print("✅ Custom interaction style set up")
        
    def setup_update_timer(self):
        """Setup timer to update display values"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)  # Update every 100ms
        print("✅ Update timer started (100ms)")
        
    def load_test_cube(self):
        """Load a simple test cube"""
        # Create a cube
        cube_source = vtk.vtkCubeSource()
        cube_source.SetXLength(2.0)
        cube_source.SetYLength(2.0)
        cube_source.SetZLength(2.0)
        
        # Create mapper and actor
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cube_source.GetOutputPort())
        
        self.test_actor = vtk.vtkActor()
        self.test_actor.SetMapper(mapper)
        self.test_actor.GetProperty().SetColor(0.8, 0.2, 0.2)  # Red color
        
        # Add to renderer
        self.renderer.AddActor(self.test_actor)
        self.renderer.ResetCamera()
        self.render_window.Render()
        
        self.status_label.setText("Status: Test cube loaded - try mouse drag rotation")
        print("✅ Test cube loaded")
        
    def rotate_test_actor(self, dx, dy):
        """Rotate the test actor based on mouse movement"""
        if not self.test_actor:
            return
            
        try:
            # Convert mouse movement to rotation
            rotation_factor = 0.5
            y_rotation = dx * rotation_factor  # Horizontal mouse = Y-axis rotation
            x_rotation = dy * rotation_factor  # Vertical mouse = X-axis rotation
            
            # Apply rotation to actor
            if y_rotation != 0:
                self.test_actor.RotateWXYZ(y_rotation, 0, 1, 0)  # Y-axis
            if x_rotation != 0:
                self.test_actor.RotateWXYZ(x_rotation, 1, 0, 0)  # X-axis
                
            # Force render
            self.render_window.Render()
            
            print(f"🔧 Rotated actor: Y={y_rotation:.1f}° X={x_rotation:.1f}°")
            
        except Exception as e:
            print(f"❌ Rotation error: {e}")
            
    def update_display(self):
        """Update display values from VTK actor"""
        if not self.test_actor:
            return
            
        try:
            # Get actor orientation
            orientation = self.test_actor.GetOrientation()
            
            # Update display
            self.rotation_label.setText(f"VTK Rotation: X={orientation[0]:.2f} Y={orientation[1]:.2f} Z={orientation[2]:.2f}")
            
            # Only print if values are non-zero (to avoid spam)
            if abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1:
                print(f"🔄 Display updated: X={orientation[0]:.2f} Y={orientation[1]:.2f} Z={orientation[2]:.2f}")
                
        except Exception as e:
            print(f"❌ Display update error: {e}")

def main():
    app = QApplication(sys.argv)
    
    # Test program
    test_window = MouseRotationTest()
    test_window.show()
    
    print("🎯 Mouse Rotation Test Program Started")
    print("📋 Instructions:")
    print("   1. Click 'Load Test Cube' to create a red cube")
    print("   2. Drag with mouse to rotate the cube")
    print("   3. Watch console for debug messages")
    print("   4. Check if rotation values update in the GUI")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
