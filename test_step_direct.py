#!/usr/bin/env python3
"""
Direct test of STEP file AXIS2_PLACEMENT_3D extraction without imports
"""

import re
import os

def extract_axis2_placement_direct(filename):
    """Extract AXIS2_PLACEMENT_3D directly from STEP file"""
    try:
        with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Find first AXIS2_PLACEMENT_3D
        axis2_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\(\s*\'[^\']*\'\s*,\s*#(\d+)\s*,\s*#(\d+)\s*,\s*#(\d+)\s*\)'
        match = re.search(axis2_pattern, content)

        if not match:
            print("❌ No AXIS2_PLACEMENT_3D found in STEP file")
            return None

        axis2_id, point_id, dir1_id, dir2_id = match.groups()
        print(f"🔧 Found first AXIS2_PLACEMENT_3D #{axis2_id} -> Point #{point_id}, Dir1 #{dir1_id}, Dir2 #{dir2_id}")

        # Extract CARTESIAN_POINT
        point_pattern = rf'#{point_id}\s*=\s*CARTESIAN_POINT\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*\)\s*\)'
        point_match = re.search(point_pattern, content)

        # Extract DIRECTION for dir1
        dir1_pattern = rf'#{dir1_id}\s*=\s*DIRECTION\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*\)\s*\)'
        dir1_match = re.search(dir1_pattern, content)

        # Extract DIRECTION for dir2
        dir2_pattern = rf'#{dir2_id}\s*=\s*DIRECTION\s*\(\s*\'[^\']*\'\s*,\s*\(\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*\)\s*\)'
        dir2_match = re.search(dir2_pattern, content)

        if point_match and dir1_match and dir2_match:
            result = {
                'point': (float(point_match.group(1)), float(point_match.group(2)), float(point_match.group(3))),
                'dir1': (float(dir1_match.group(1)), float(dir1_match.group(2)), float(dir1_match.group(3))),
                'dir2': (float(dir2_match.group(1)), float(dir2_match.group(2)), float(dir2_match.group(3)))
            }
            print(f"✅ Extracted AXIS2_PLACEMENT_3D data:")
            print(f"   Point: {result['point']}")
            print(f"   Dir1: {result['dir1']}")
            print(f"   Dir2: {result['dir2']}")
            return result
        else:
            print("❌ Failed to extract coordinate data from STEP file")
            print(f"   Point match: {point_match is not None}")
            print(f"   Dir1 match: {dir1_match is not None}")
            print(f"   Dir2 match: {dir2_match is not None}")
            return None

    except Exception as e:
        print(f"❌ Error extracting AXIS2_PLACEMENT_3D: {e}")
        return None

def test_step_extraction():
    """Test STEP file extraction"""
    step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
    
    if not os.path.exists(step_file):
        print(f'❌ STEP file not found: {step_file}')
        return False
    
    print(f'✅ Found STEP file: {step_file}')
    
    # Extract AXIS2_PLACEMENT_3D data
    axis_data = extract_axis2_placement_direct(step_file)
    
    if axis_data:
        print('\n🔧 Testing GUI text formatting...')
        # Test the text formatting that would be used in GUI
        original_axis_text = f"""
Original top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
        print(f'Formatted text for GUI:{original_axis_text}')
        return True
    else:
        print('❌ Failed to extract AXIS2_PLACEMENT_3D data')
        return False

if __name__ == "__main__":
    print("=== DIRECT STEP FILE AXIS2_PLACEMENT_3D EXTRACTION TEST ===")
    success = test_step_extraction()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
