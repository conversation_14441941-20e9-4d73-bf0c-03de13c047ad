#!/usr/bin/env python3
"""
Final test to verify the origin markers now rotate correctly with the model.
This test will:
1. Load a model
2. Record initial positions and rotations
3. Click rotation buttons
4. Verify origin markers rotate AND move with the model
"""

import sys
import os
import math
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🎯 FINAL ROTATION FIX TEST: VERIFYING ORIGIN MARKERS ROTATE WITH MODEL")
    print("=" * 70)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    # Store test data
    test_data = {
        'initial_sphere_pos': None,
        'initial_model_orient': None,
        'positions_after_clicks': [],
        'orientations_after_clicks': [],
        'click_count': 0,
        'max_clicks': 4  # Test 4 clicks (60 degrees total)
    }
    
    def record_initial_state():
        print("\n3. RECORDING INITIAL STATE")
        print("-" * 40)
        
        renderer = viewer.vtk_renderer_left
        
        # Record initial green sphere position
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            initial_pos = renderer.part_origin_sphere.GetPosition()
            test_data['initial_sphere_pos'] = initial_pos
            print(f"   Initial green sphere position: {initial_pos}")
        else:
            print("   ❌ No green sphere found")
            return
            
        # Record initial model orientation
        if hasattr(viewer, 'step_actors_left') and viewer.step_actors_left:
            initial_orient = viewer.step_actors_left[0].GetOrientation()
            test_data['initial_model_orient'] = initial_orient
            print(f"   Initial model orientation: {initial_orient}")
        else:
            print("   ❌ No model actors found")
            return
        
        # Start clicking buttons
        QTimer.singleShot(1000, click_button_and_measure)
    
    def click_button_and_measure():
        test_data['click_count'] += 1
        click_num = test_data['click_count']
        
        print(f"\n4.{click_num} CLICKING X+ BUTTON (Click #{click_num})")
        print("-" * 40)
        
        # Find and click X+ button
        buttons = viewer.findChildren(QPushButton)
        x_plus_button = None
        for button in buttons:
            if button.text() == "X+":
                x_plus_button = button
                break
        
        if x_plus_button:
            x_plus_button.click()
            # Wait for rotation to complete, then measure
            QTimer.singleShot(1000, lambda: measure_state_after_click(click_num))
        else:
            print("   ❌ Could not find X+ button")
    
    def measure_state_after_click(click_num):
        print(f"\n5.{click_num} MEASURING STATE AFTER CLICK #{click_num}")
        print("-" * 40)
        
        renderer = viewer.vtk_renderer_left
        
        # Measure green sphere position
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            current_pos = renderer.part_origin_sphere.GetPosition()
            test_data['positions_after_clicks'].append(current_pos)
            print(f"   Green sphere position: {current_pos}")
        else:
            print("   ❌ No green sphere found after click")
            return
            
        # Measure model orientation
        if hasattr(viewer, 'step_actors_left') and viewer.step_actors_left:
            current_orient = viewer.step_actors_left[0].GetOrientation()
            test_data['orientations_after_clicks'].append(current_orient)
            print(f"   Model orientation: {current_orient}")
        else:
            print("   ❌ No model actors found after click")
            return
        
        # Calculate changes from initial state
        if test_data['initial_sphere_pos'] and test_data['initial_model_orient']:
            # Position change
            dx = current_pos[0] - test_data['initial_sphere_pos'][0]
            dy = current_pos[1] - test_data['initial_sphere_pos'][1]
            dz = current_pos[2] - test_data['initial_sphere_pos'][2]
            pos_distance = math.sqrt(dx*dx + dy*dy + dz*dz)
            
            # Orientation change
            dox = current_orient[0] - test_data['initial_model_orient'][0]
            doy = current_orient[1] - test_data['initial_model_orient'][1]
            doz = current_orient[2] - test_data['initial_model_orient'][2]
            
            print(f"   Position change: ({dx:.6f}, {dy:.6f}, {dz:.6f}) distance={pos_distance:.6f}")
            print(f"   Orientation change: ({dox:.6f}, {doy:.6f}, {doz:.6f})")
            
            # Expected: X rotation should be 15° * click_num
            expected_x_rotation = 15.0 * click_num
            print(f"   Expected X rotation: {expected_x_rotation}°, Actual: {current_orient[0]:.1f}°")
            
            # Check if rotation is working
            if abs(current_orient[0] - expected_x_rotation) < 1.0:  # Within 1 degree
                print(f"   ✅ Model rotation is correct!")
            else:
                print(f"   ⚠️ Model rotation may be incorrect")
                
            # Check if position changed (origin markers should move with model)
            if pos_distance > 0.01:  # More than 0.01 units
                print(f"   ✅ Origin markers moved with model!")
            else:
                print(f"   ❌ Origin markers did not move significantly")
        
        # Continue clicking if we haven't reached max clicks
        if test_data['click_count'] < test_data['max_clicks']:
            QTimer.singleShot(1000, click_button_and_measure)
        else:
            # Finished all clicks, analyze results
            QTimer.singleShot(1000, analyze_final_results)
    
    def analyze_final_results():
        print(f"\n6. FINAL ANALYSIS AFTER {test_data['max_clicks']} CLICKS")
        print("=" * 70)
        
        if not test_data['initial_sphere_pos'] or not test_data['positions_after_clicks']:
            print("❌ FAILED: Could not collect position data")
            return
        
        initial_pos = test_data['initial_sphere_pos']
        final_pos = test_data['positions_after_clicks'][-1]
        initial_orient = test_data['initial_model_orient']
        final_orient = test_data['orientations_after_clicks'][-1]
        
        print(f"   Initial sphere position: {initial_pos}")
        print(f"   Final sphere position:   {final_pos}")
        print(f"   Initial model orientation: {initial_orient}")
        print(f"   Final model orientation:   {final_orient}")
        
        # Calculate total changes
        dx = final_pos[0] - initial_pos[0]
        dy = final_pos[1] - initial_pos[1] 
        dz = final_pos[2] - initial_pos[2]
        total_pos_distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        dox = final_orient[0] - initial_orient[0]
        doy = final_orient[1] - initial_orient[1]
        doz = final_orient[2] - initial_orient[2]
        
        print(f"   Total position change: ({dx:.6f}, {dy:.6f}, {dz:.6f})")
        print(f"   Total position distance: {total_pos_distance:.6f}")
        print(f"   Total orientation change: ({dox:.6f}, {doy:.6f}, {doz:.6f})")
        
        # Expected: 4 clicks * 15° = 60° X rotation
        expected_total_rotation = 60.0
        print(f"   Expected total X rotation: {expected_total_rotation}°")
        print(f"   Actual total X rotation: {dox:.1f}°")
        
        # Analyze results
        rotation_correct = abs(dox - expected_total_rotation) < 2.0  # Within 2 degrees
        position_moved = total_pos_distance > 0.1  # At least 0.1 units movement
        
        print(f"\n🎯 FINAL VERDICT:")
        if rotation_correct and position_moved:
            print(f"🎉 SUCCESS! Origin markers are rotating correctly with the model!")
            print(f"   ✅ Model rotation: {dox:.1f}° (expected {expected_total_rotation}°)")
            print(f"   ✅ Origin movement: {total_pos_distance:.6f} units")
            print(f"   ✅ Both rotation and position are working correctly!")
        elif rotation_correct and not position_moved:
            print(f"⚠️ PARTIAL SUCCESS: Model rotates but origin markers don't move enough")
            print(f"   ✅ Model rotation: {dox:.1f}° (expected {expected_total_rotation}°)")
            print(f"   ❌ Origin movement: {total_pos_distance:.6f} units (too small)")
        elif not rotation_correct and position_moved:
            print(f"⚠️ PARTIAL SUCCESS: Origin markers move but rotation is wrong")
            print(f"   ❌ Model rotation: {dox:.1f}° (expected {expected_total_rotation}°)")
            print(f"   ✅ Origin movement: {total_pos_distance:.6f} units")
        else:
            print(f"❌ FAILED: Neither rotation nor origin movement is working correctly")
            print(f"   ❌ Model rotation: {dox:.1f}° (expected {expected_total_rotation}°)")
            print(f"   ❌ Origin movement: {total_pos_distance:.6f} units (too small)")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(3000, record_initial_state)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will test 4 button clicks (60° total rotation)")
    print("   - Both model rotation AND origin marker movement will be verified")
    print("   - Final analysis will determine if the fix works completely")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
