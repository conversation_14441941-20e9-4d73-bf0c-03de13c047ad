#!/usr/bin/env python3
"""
Simple test to verify the duplicate world origin fix
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class DuplicateFixTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_test(self):
        print("🔍 TESTING DUPLICATE WORLD ORIGIN FIX")
        print("=" * 40)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.load_model)
        self.app.exec_()
        
    def load_model(self):
        print("1. Loading STEP file...")
        
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print("✅ STEP file loaded")
        QTimer.singleShot(1000, self.count_initial)
        
    def count_initial(self):
        initial_count = self.count_red_actors()
        print(f"2. Initial red (world origin) actors: {initial_count}")
        
        QTimer.singleShot(1000, self.apply_rotation)
        
    def apply_rotation(self):
        print("3. Applying rotation...")
        self.viewer.rotate_shape('x', 30)
        
        QTimer.singleShot(1000, self.count_after_rotation)
        
    def count_after_rotation(self):
        after_rotation = self.count_red_actors()
        print(f"4. Red actors after rotation: {after_rotation}")
        
        QTimer.singleShot(1000, self.apply_reset)
        
    def apply_reset(self):
        print("5. Applying reset (this should trigger the fix)...")
        self.viewer.reset_to_original()
        
        QTimer.singleShot(2000, self.count_after_reset)
        
    def count_after_reset(self):
        after_reset = self.count_red_actors()
        print(f"6. Red actors after reset: {after_reset}")
        
        if after_reset <= 4:  # Expected: 1 semicircle + 3 arrows = 4 red actors max
            print("🎉 SUCCESS: Duplicate fix is working!")
            print("✅ No duplicate world origin markers detected")
        else:
            print("❌ FAILURE: Still have duplicate world origin markers")
            print(f"   Expected ≤4 red actors, got {after_reset}")
            
        QTimer.singleShot(3000, self.app.quit)
        
    def count_red_actors(self):
        """Count red actors (world origin markers)"""
        count = 0
        try:
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                renderer = self.viewer.vtk_renderer_left.renderer
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    while True:
                        actor = actors.GetNextActor()
                        if not actor:
                            break
                            
                        prop = actor.GetProperty()
                        if prop:
                            color = prop.GetColor()
                            # Red actors (world origin)
                            if color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2:
                                count += 1
                                
        except Exception as e:
            print(f"   Error counting red actors: {e}")
            
        return count

if __name__ == '__main__':
    tester = DuplicateFixTester()
    tester.run_test()
