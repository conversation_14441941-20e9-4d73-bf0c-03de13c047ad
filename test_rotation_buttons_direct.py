#!/usr/bin/env python3
"""
Direct test of rotation buttons to verify green origin markers move.
This test will programmatically click the rotation buttons and verify movement.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import <PERSON><PERSON>iewerTDK

def test_rotation_buttons():
    print("🧪 DIRECT ROTATION BUTTON TEST")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load a STEP file
    step_file = "e:/Python/3d-view/debug_save_test.STEP"
    if not os.path.exists(step_file):
        print(f"❌ ERROR: STEP file not found: {step_file}")
        return False
        
    print(f"📁 Loading STEP file: {step_file}")
    success = viewer.load_step_file_direct(step_file)
    if not success:
        print("❌ ERROR: Failed to load STEP file")
        return False
        
    print("✅ STEP file loaded successfully")
    
    # Wait for loading to complete
    app.processEvents()
    time.sleep(2)
    
    # Check if green origin markers exist
    if not hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
        print("❌ ERROR: Green origin sphere not found")
        return False
        
    if not viewer.vtk_renderer_left.part_origin_sphere:
        print("❌ ERROR: Green origin sphere is None")
        return False
        
    print("✅ Green origin markers found")
    
    # Get initial position
    initial_pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    print(f"📍 Initial green sphere position: ({initial_pos[0]:.3f}, {initial_pos[1]:.3f}, {initial_pos[2]:.3f})")
    
    # Test X+ rotation button
    print(f"\n🔄 Testing X+ rotation button...")
    pos_before = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    print(f"   Before: ({pos_before[0]:.3f}, {pos_before[1]:.3f}, {pos_before[2]:.3f})")
    
    # Directly call the rotation method (simulating button click)
    viewer.rotate_shape('x', 15)
    app.processEvents()
    time.sleep(0.5)
    
    pos_after = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    print(f"   After:  ({pos_after[0]:.3f}, {pos_after[1]:.3f}, {pos_after[2]:.3f})")
    
    # Calculate movement
    movement_x = pos_after[0] - pos_before[0]
    movement_y = pos_after[1] - pos_before[1]
    movement_z = pos_after[2] - pos_before[2]
    total_movement = (movement_x**2 + movement_y**2 + movement_z**2)**0.5
    
    print(f"   Movement: ({movement_x:.3f}, {movement_y:.3f}, {movement_z:.3f})")
    print(f"   Distance: {total_movement:.3f} units")
    
    if total_movement > 0.001:  # Threshold for detecting movement
        print("   ✅ SUCCESS: Green origin markers moved!")
        result = True
    else:
        print("   ❌ FAILURE: Green origin markers did not move!")
        result = False
    
    # Test Y+ rotation button
    print(f"\n🔄 Testing Y+ rotation button...")
    pos_before = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    print(f"   Before: ({pos_before[0]:.3f}, {pos_before[1]:.3f}, {pos_before[2]:.3f})")
    
    viewer.rotate_shape('y', 15)
    app.processEvents()
    time.sleep(0.5)
    
    pos_after = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    print(f"   After:  ({pos_after[0]:.3f}, {pos_after[1]:.3f}, {pos_after[2]:.3f})")
    
    # Calculate movement
    movement_x = pos_after[0] - pos_before[0]
    movement_y = pos_after[1] - pos_before[1]
    movement_z = pos_after[2] - pos_before[2]
    total_movement = (movement_x**2 + movement_y**2 + movement_z**2)**0.5
    
    print(f"   Movement: ({movement_x:.3f}, {movement_y:.3f}, {movement_z:.3f})")
    print(f"   Distance: {total_movement:.3f} units")
    
    if total_movement > 0.001:  # Threshold for detecting movement
        print("   ✅ SUCCESS: Green origin markers moved!")
        result = result and True
    else:
        print("   ❌ FAILURE: Green origin markers did not move!")
        result = False
    
    # Final result
    print("\n" + "=" * 50)
    if result:
        print("🎉 OVERALL RESULT: SUCCESS!")
        print("✅ Green origin markers are rotating correctly with the model!")
    else:
        print("❌ OVERALL RESULT: FAILURE!")
        print("❌ Green origin markers are NOT rotating with the model!")
    
    print("\n👁️ Viewer will stay open for 5 seconds for visual inspection...")
    
    # Keep viewer open for visual inspection
    QTimer.singleShot(5000, app.quit)  # Close after 5 seconds
    app.exec_()
    
    return result

if __name__ == "__main__":
    success = test_rotation_buttons()
    if success:
        print("\n✅ TEST PASSED: The fix is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ TEST FAILED: The fix needs more work!")
        sys.exit(1)
