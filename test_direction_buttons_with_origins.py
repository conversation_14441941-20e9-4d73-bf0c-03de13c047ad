#!/usr/bin/env python3
"""
Test script to verify direction buttons work with origin actors
"""

import sys
import os
import time
from PyQt5.QtWidgets import <PERSON>Application, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer_tdk_modular import StepViewerTDK

class DirectionButtonTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Direction Button Tester")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(300)
        
        # Status display
        self.status_label = QLabel("Status: Ready")
        left_layout.addWidget(self.status_label)
        
        # Load button
        load_btn = QPushButton("Load STEP File")
        load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(load_btn)
        
        # Create origin button
        origin_btn = QPushButton("Create Origin Overlay")
        origin_btn.clicked.connect(self.create_origin)
        left_layout.addWidget(origin_btn)
        
        # Test movement buttons
        test_layout = QVBoxLayout()
        
        btn_x_plus = QPushButton("Test X+ (should move origin east)")
        btn_x_plus.clicked.connect(lambda: self.test_move('x', 1.0))
        test_layout.addWidget(btn_x_plus)
        
        btn_x_minus = QPushButton("Test X- (should move origin west)")
        btn_x_minus.clicked.connect(lambda: self.test_move('x', -1.0))
        test_layout.addWidget(btn_x_minus)
        
        btn_y_plus = QPushButton("Test Y+ (should move origin north)")
        btn_y_plus.clicked.connect(lambda: self.test_move('y', 1.0))
        test_layout.addWidget(btn_y_plus)
        
        btn_y_minus = QPushButton("Test Y- (should move origin south)")
        btn_y_minus.clicked.connect(lambda: self.test_move('y', -1.0))
        test_layout.addWidget(btn_y_minus)
        
        btn_z_plus = QPushButton("Test Z+ (should move origin up)")
        btn_z_plus.clicked.connect(lambda: self.test_move('z', 1.0))
        test_layout.addWidget(btn_z_plus)
        
        btn_z_minus = QPushButton("Test Z- (should move origin down)")
        btn_z_minus.clicked.connect(lambda: self.test_move('z', -1.0))
        test_layout.addWidget(btn_z_minus)
        
        left_layout.addLayout(test_layout)
        
        # Reset button
        reset_btn = QPushButton("Reset Position")
        reset_btn.clicked.connect(self.reset_position)
        left_layout.addWidget(reset_btn)
        
        left_layout.addStretch()
        layout.addWidget(left_panel)
        
        # Right side - The actual viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
    def load_step_file(self):
        """Load a STEP file"""
        self.status_label.setText("Status: Loading STEP file...")
        QApplication.processEvents()
        
        # Use the viewer's load function
        self.viewer.load_step_file()
        
        self.status_label.setText("Status: STEP file loaded")
        
    def create_origin(self):
        """Create origin overlay"""
        self.status_label.setText("Status: Creating origin overlay...")
        QApplication.processEvents()
        
        # Create origin overlay
        self.viewer.create_origin_overlay()
        
        # Check if origin actors were created
        if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
            count = len(self.viewer.vtk_renderer_left.origin_actors)
            self.status_label.setText(f"Status: Origin overlay created ({count} actors)")
        else:
            self.status_label.setText("Status: Origin overlay creation failed")
        
    def test_move(self, axis, amount):
        """Test a movement and show results"""
        print(f"\n🔧 DIRECTION BUTTON TEST: Moving {axis} by {amount}")
        
        # Check origin actors before move
        origin_count = 0
        part_origin_count = 0
        
        if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
            origin_count = len(self.viewer.vtk_renderer_left.origin_actors)
            
        if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere') and self.viewer.vtk_renderer_left.part_origin_sphere:
            part_origin_count = 1
            
        print(f"   Before move: {origin_count} world origin actors, {part_origin_count} part origin actors")
        
        # Set active viewer and move
        self.viewer.active_viewer = "top"
        self.viewer.move_shape(axis, amount)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Update status
        self.status_label.setText(f"Status: Moved {axis} by {amount}mm")
        
        print(f"   Move completed - check if origin actors moved visually")
        
    def reset_position(self):
        """Reset to original position"""
        self.status_label.setText("Status: Resetting position...")
        QApplication.processEvents()
        
        self.viewer.reset_to_original()
        
        self.status_label.setText("Status: Position reset")

def main():
    print("🔥🔥🔥 STARTING DIRECTION BUTTON TEST WITH ORIGINS 🔥🔥🔥")
    
    app = QApplication(sys.argv)
    
    # Create the test window
    tester = DirectionButtonTester()
    tester.show()
    
    print("✅ Test window created")
    print("📋 Instructions:")
    print("   1. Click 'Load STEP File' to load a model")
    print("   2. Click 'Create Origin Overlay' to create origin markers")
    print("   3. Use the direction buttons to test movement")
    print("   4. Watch if the origin markers move with the buttons")
    
    # Start the event loop
    app.exec_()

if __name__ == "__main__":
    main()
