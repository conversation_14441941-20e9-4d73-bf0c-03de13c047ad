#!/usr/bin/env python3
"""
Debug script to investigate why VTK display values aren't updating
when the 3D model rotates correctly
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

def debug_vtk_values():
    """Debug VTK value extraction and display"""
    print("🔥🔥🔥 DEBUGGING VTK DISPLAY VALUES 🔥🔥🔥")
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize, then run debug tests
    QTimer.singleShot(2000, lambda: run_debug_tests(viewer))
    
    # Start the application
    app.exec_()

def run_debug_tests(viewer):
    """Run comprehensive debug tests"""
    print("\n" + "="*80)
    print("🔧 STARTING VTK VALUE DEBUG TESTS")
    print("="*80)
    
    # Test 1: Check what actors are available
    print("\n🔧 TEST 1: Check available actors")
    debug_available_actors(viewer)
    
    # Test 2: Load a STEP file if available
    print("\n🔧 TEST 2: Try to load STEP file")
    step_file = find_step_file()
    if step_file:
        load_step_file(viewer, step_file)
        
        # Wait for model to load, then continue tests
        QTimer.singleShot(1000, lambda: continue_debug_with_model(viewer))
    else:
        print("❌ No STEP file found, testing without model")
        continue_debug_without_model(viewer)

def debug_available_actors(viewer):
    """Debug what actors are available in the viewer"""
    print("🔍 Checking TOP viewer actors:")
    
    # Check vtk_renderer_left.step_actors
    if hasattr(viewer.vtk_renderer_left, 'step_actors'):
        if viewer.vtk_renderer_left.step_actors:
            print(f"✅ vtk_renderer_left.step_actors: {len(viewer.vtk_renderer_left.step_actors)} actors")
            for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
                print(f"   Actor {i}: {actor}")
        else:
            print("❌ vtk_renderer_left.step_actors: Empty list")
    else:
        print("❌ vtk_renderer_left.step_actors: Not found")
    
    # Check vtk_renderer_left.step_actor (single actor)
    if hasattr(viewer.vtk_renderer_left, 'step_actor'):
        if viewer.vtk_renderer_left.step_actor:
            print(f"✅ vtk_renderer_left.step_actor: {viewer.vtk_renderer_left.step_actor}")
        else:
            print("❌ vtk_renderer_left.step_actor: None")
    else:
        print("❌ vtk_renderer_left.step_actor: Not found")
    
    # Check renderer actors
    if hasattr(viewer.vtk_renderer_left, 'renderer'):
        actors = viewer.vtk_renderer_left.renderer.GetActors()
        actors.InitTraversal()
        actor_count = 0
        print("🔍 All actors in renderer:")
        actor = actors.GetNextActor()
        while actor:
            actor_count += 1
            print(f"   Renderer Actor {actor_count}: {actor}")
            actor = actors.GetNextActor()
        print(f"✅ Total actors in renderer: {actor_count}")
    else:
        print("❌ vtk_renderer_left.renderer: Not found")

def find_step_file():
    """Find an available STEP file"""
    test_files = [
        "SOIC16P127_1270X940X610L89X51.STEP",
        "test.step",
        "test.STEP"
    ]
    
    for filename in test_files:
        if os.path.exists(filename):
            print(f"✅ Found STEP file: {filename}")
            return filename
    
    print("❌ No STEP file found")
    return None

def load_step_file(viewer, step_file):
    """Load STEP file and display it"""
    print(f"🔧 Loading STEP file: {step_file}")
    
    try:
        viewer.active_viewer = "top"
        success = viewer.step_loader_left.load_step_file(step_file)
        if success:
            print(f"✅ STEP file loaded successfully")
            
            # Display the model in the TOP viewer
            viewer.vtk_renderer_left.display_polydata(
                viewer.step_loader_left.current_polydata
            )
            print(f"✅ Model displayed in TOP viewer")
            
        else:
            print(f"❌ Failed to load STEP file")
    except Exception as e:
        print(f"❌ Error loading STEP file: {e}")

def continue_debug_with_model(viewer):
    """Continue debug tests with loaded model"""
    print("\n🔧 TEST 3: Debug with loaded model")
    
    # Check actors again after loading
    debug_available_actors(viewer)
    
    # Test VTK value extraction before rotation
    print("\n🔧 TEST 4: VTK values before rotation")
    initial_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"📊 Initial VTK values: {initial_values}")
    
    # Apply rotation and check values
    print("\n🔧 TEST 5: Apply rotation and check VTK values")
    print("🔄 Applying X+15° rotation...")
    viewer.rotate_shape('x', 15)
    
    # Check VTK values after rotation
    after_rotation_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"📊 VTK values after X+15°: {after_rotation_values}")
    
    # Compare values
    print("\n🔧 TEST 6: Compare before/after values")
    if initial_values != after_rotation_values:
        print("✅ SUCCESS: VTK values changed after rotation!")
        print(f"   Before: {initial_values['rotation']}")
        print(f"   After:  {after_rotation_values['rotation']}")
    else:
        print("❌ PROBLEM: VTK values did not change after rotation")
        print("   This explains why the display numbers don't update")
        
        # Debug why values didn't change
        debug_rotation_application(viewer)

def continue_debug_without_model(viewer):
    """Continue debug tests without model"""
    print("\n🔧 TEST 3: Debug without model")
    
    # Test VTK value extraction without model
    print("\n🔧 TEST 4: VTK values without model")
    values = viewer.get_actual_vtk_transformation_values("top")
    print(f"📊 VTK values (no model): {values}")
    
    # Apply rotation and check values
    print("\n🔧 TEST 5: Apply rotation without model")
    print("🔄 Applying X+15° rotation...")
    viewer.rotate_shape('x', 15)
    
    # Check VTK values after rotation
    after_values = viewer.get_actual_vtk_transformation_values("top")
    print(f"📊 VTK values after rotation (no model): {after_values}")
    
    print("✅ Expected: Values should remain zero without model")

def debug_rotation_application(viewer):
    """Debug why rotation isn't being applied to VTK actors"""
    print("\n🔧 DEBUGGING ROTATION APPLICATION")
    
    # Check if actors exist and are in renderer
    if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
        print(f"✅ step_actors found: {len(viewer.vtk_renderer_left.step_actors)} actors")
        
        # Check each actor's transformation
        for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
            print(f"🔍 Actor {i} transformation:")
            
            # Check user transform
            user_transform = actor.GetUserTransform()
            if user_transform:
                print(f"   ✅ Has user transform: {user_transform}")
                matrix = user_transform.GetMatrix()
                print(f"   📊 Transform matrix elements:")
                for row in range(4):
                    for col in range(4):
                        element = matrix.GetElement(row, col)
                        if abs(element) > 0.001:  # Only show non-zero elements
                            print(f"      [{row},{col}] = {element:.6f}")
            else:
                print(f"   ❌ No user transform")
            
            # Check actor orientation
            orientation = actor.GetOrientation()
            print(f"   📊 Actor orientation: X={orientation[0]:.3f}° Y={orientation[1]:.3f}° Z={orientation[2]:.3f}°")
            
            # Check actor position
            position = actor.GetPosition()
            print(f"   📊 Actor position: X={position[0]:.3f} Y={position[1]:.3f} Z={position[2]:.3f}")
    
    elif hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
        print(f"✅ Single step_actor found")
        actor = viewer.vtk_renderer_left.step_actor
        
        # Check transformation
        user_transform = actor.GetUserTransform()
        if user_transform:
            print(f"   ✅ Has user transform")
        else:
            print(f"   ❌ No user transform")
        
        orientation = actor.GetOrientation()
        print(f"   📊 Actor orientation: X={orientation[0]:.3f}° Y={orientation[1]:.3f}° Z={orientation[2]:.3f}°")
    
    else:
        print("❌ No STEP actors found - this is the problem!")

if __name__ == "__main__":
    debug_vtk_values()
