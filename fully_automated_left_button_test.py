#!/usr/bin/env python3
"""
FULLY AUTOMATED LEFT BUTTON ORIGIN MOVEMENT TEST
=================================================

This program runs completely automatically without any user interaction.
It will:
1. Start the step viewer programmatically
2. Load a test geometry
3. Enable origin overlays
4. Simulate left button clicks
5. Capture and analyze all debug output
6. Show exactly what's wrong and how to fix it

NO MANUAL INTERACTION REQUIRED!
"""

import sys
import os
import time
import threading
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
import vtk

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class AutomatedLeftButtonTester(QThread):
    """Fully automated tester for left button origin movement"""
    
    log_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.step_viewer = None
        self.test_results = []
        
    def run(self):
        """Main automated test routine"""
        try:
            self.log("=" * 80)
            self.log("FULLY AUTOMATED LEFT BUTTON ORIGIN MOVEMENT TEST")
            self.log("=" * 80)
            self.log("Running completely automatically - no user interaction needed!")
            
            # Step 1: Import and create step viewer
            self.log("\n1. CREATING STEP VIEWER...")
            if not self.create_step_viewer():
                return
            
            # Step 2: Create test geometry
            self.log("\n2. CREATING TEST GEOMETRY...")
            if not self.create_test_geometry():
                return
            
            # Step 3: Enable origin overlays
            self.log("\n3. ENABLING ORIGIN OVERLAYS...")
            if not self.enable_origin_overlays():
                return
            
            # Step 4: Record initial positions
            self.log("\n4. RECORDING INITIAL POSITIONS...")
            initial_positions = self.record_positions("INITIAL")
            
            # Step 5: Test left button rotation
            self.log("\n5. TESTING LEFT BUTTON ROTATION...")
            self.test_left_button_rotation()
            
            # Step 6: Record final positions
            self.log("\n6. RECORDING FINAL POSITIONS...")
            final_positions = self.record_positions("AFTER ROTATION")
            
            # Step 7: Analyze results
            self.log("\n7. ANALYZING RESULTS...")
            self.analyze_results(initial_positions, final_positions)
            
            # Step 8: Provide diagnosis
            self.log("\n8. FINAL DIAGNOSIS...")
            self.provide_diagnosis()
            
        except Exception as e:
            self.log(f"ERROR in automated test: {str(e)}")
            import traceback
            self.log(f"Traceback: {traceback.format_exc()}")
    
    def log(self, message):
        """Log message to console"""
        print(message)
        self.log_signal.emit(message)
    
    def create_step_viewer(self):
        """Create step viewer instance"""
        try:
            import step_viewer
            self.step_viewer = step_viewer.StepViewerTDK()
            self.log("✓ Step viewer created successfully")
            return True
        except Exception as e:
            self.log(f"✗ Failed to create step viewer: {e}")
            return False
    
    def create_test_geometry(self):
        """Create simple test geometry"""
        try:
            # Create a simple cube
            cube_source = vtk.vtkCubeSource()
            cube_source.SetXLength(20)
            cube_source.SetYLength(20)
            cube_source.SetZLength(20)
            cube_source.SetCenter(10, 10, 10)  # Offset from origin
            cube_source.Update()
            
            # Create mapper and actor
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(cube_source.GetOutputPort())
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(0.8, 0.8, 0.9)
            
            # Add to left renderer
            if hasattr(self.step_viewer, 'vtk_renderer_left'):
                self.step_viewer.vtk_renderer_left.AddActor(actor)
                # Store as step_actor for the rotation method to find
                self.step_viewer.vtk_renderer_left.step_actor = actor
                self.step_viewer.vtk_renderer_left.render_window.Render()
                
                # Initialize current_pos_left
                if not hasattr(self.step_viewer, 'current_pos_left'):
                    self.step_viewer.current_pos_left = {'x': 10.0, 'y': 10.0, 'z': 10.0}
                
                self.log("✓ Test cube created and added to left renderer")
                self.log(f"   Cube center: (10, 10, 10)")
                self.log(f"   current_pos_left: {self.step_viewer.current_pos_left}")
                return True
            else:
                self.log("✗ No left renderer found")
                return False
                
        except Exception as e:
            self.log(f"✗ Failed to create test geometry: {e}")
            return False
    
    def enable_origin_overlays(self):
        """Enable origin overlays"""
        try:
            # Create simple origin markers manually
            if hasattr(self.step_viewer, 'vtk_renderer_left'):
                renderer = self.step_viewer.vtk_renderer_left
                
                # Create world origin actors (red)
                sphere_source = vtk.vtkSphereSource()
                sphere_source.SetRadius(2.0)
                sphere_source.SetCenter(0, 0, 0)
                
                mapper = vtk.vtkPolyDataMapper()
                mapper.SetInputConnection(sphere_source.GetOutputPort())
                
                world_origin_actor = vtk.vtkActor()
                world_origin_actor.SetMapper(mapper)
                world_origin_actor.GetProperty().SetColor(1, 0, 0)  # Red
                
                renderer.AddActor(world_origin_actor)
                
                # Store in origin_actors list
                if not hasattr(renderer, 'origin_actors'):
                    renderer.origin_actors = []
                renderer.origin_actors.append(world_origin_actor)
                
                # Create part origin actors (green) at model center
                part_sphere_source = vtk.vtkSphereSource()
                part_sphere_source.SetRadius(1.5)
                part_sphere_source.SetCenter(10, 10, 10)  # At model center
                
                part_mapper = vtk.vtkPolyDataMapper()
                part_mapper.SetInputConnection(part_sphere_source.GetOutputPort())
                
                part_origin_actor = vtk.vtkActor()
                part_origin_actor.SetMapper(part_mapper)
                part_origin_actor.GetProperty().SetColor(0, 1, 0)  # Green
                
                renderer.AddActor(part_origin_actor)
                renderer.part_origin_sphere = part_origin_actor
                
                renderer.render_window.Render()
                
                self.log("✓ Origin overlays created")
                self.log(f"   World origin (red): (0, 0, 0)")
                self.log(f"   Part origin (green): (10, 10, 10)")
                return True
            else:
                self.log("✗ No renderer found for origin overlays")
                return False
                
        except Exception as e:
            self.log(f"✗ Failed to enable origin overlays: {e}")
            return False
    
    def record_positions(self, phase):
        """Record current positions of all actors"""
        positions = {'phase': phase}
        
        try:
            # Record current_pos_left
            if hasattr(self.step_viewer, 'current_pos_left'):
                positions['current_pos_left'] = dict(self.step_viewer.current_pos_left)
                self.log(f"   current_pos_left: {positions['current_pos_left']}")
            
            # Record model actor position
            if hasattr(self.step_viewer.vtk_renderer_left, 'step_actor'):
                actor = self.step_viewer.vtk_renderer_left.step_actor
                model_pos = actor.GetPosition()
                model_bounds = actor.GetBounds()
                model_center = (
                    (model_bounds[0] + model_bounds[1]) / 2.0,
                    (model_bounds[2] + model_bounds[3]) / 2.0,
                    (model_bounds[4] + model_bounds[5]) / 2.0
                )
                positions['model_position'] = model_pos
                positions['model_center'] = model_center
                self.log(f"   Model position: {model_pos}")
                self.log(f"   Model center: {model_center}")
            
            # Record origin actor positions
            if hasattr(self.step_viewer.vtk_renderer_left, 'origin_actors'):
                origin_positions = []
                for i, actor in enumerate(self.step_viewer.vtk_renderer_left.origin_actors):
                    pos = actor.GetPosition()
                    origin_positions.append(pos)
                    self.log(f"   Origin actor {i}: {pos}")
                positions['origin_actors'] = origin_positions
            
            # Record part origin position
            if hasattr(self.step_viewer.vtk_renderer_left, 'part_origin_sphere'):
                part_pos = self.step_viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
                positions['part_origin'] = part_pos
                self.log(f"   Part origin: {part_pos}")
            
        except Exception as e:
            self.log(f"✗ Error recording positions: {e}")
        
        return positions
    
    def test_left_button_rotation(self):
        """Test left button rotation"""
        try:
            self.log("   Calling rotate_shape('x', 15)...")
            
            # Capture debug output by temporarily redirecting print
            import io
            from contextlib import redirect_stdout
            
            debug_output = io.StringIO()
            with redirect_stdout(debug_output):
                self.step_viewer.rotate_shape('x', 15)
            
            # Get the debug output
            debug_text = debug_output.getvalue()
            
            self.log("   Debug output from rotate_shape:")
            for line in debug_text.split('\n'):
                if line.strip():
                    self.log(f"     {line}")
            
            # Allow time for rendering
            time.sleep(0.1)
            
            self.log("✓ Left button rotation completed")
            
        except Exception as e:
            self.log(f"✗ Error during left button rotation: {e}")
    
    def analyze_results(self, initial, final):
        """Analyze the position changes"""
        self.log("\n   POSITION CHANGE ANALYSIS:")
        self.log("   " + "-" * 50)
        
        # Check current_pos_left changes
        if 'current_pos_left' in initial and 'current_pos_left' in final:
            initial_pos = initial['current_pos_left']
            final_pos = final['current_pos_left']
            
            changed = initial_pos != final_pos
            self.log(f"   current_pos_left changed: {changed}")
            if changed:
                self.log(f"     Before: {initial_pos}")
                self.log(f"     After:  {final_pos}")
            else:
                self.log(f"     Value: {initial_pos} (unchanged)")
        
        # Check model center changes
        if 'model_center' in initial and 'model_center' in final:
            initial_center = initial['model_center']
            final_center = final['model_center']
            
            delta = (
                final_center[0] - initial_center[0],
                final_center[1] - initial_center[1],
                final_center[2] - initial_center[2]
            )
            
            self.log(f"   Model center changed: {delta != (0, 0, 0)}")
            self.log(f"     Before: {initial_center}")
            self.log(f"     After:  {final_center}")
            self.log(f"     Delta:  {delta}")
        
        # Check origin actor movements
        if 'origin_actors' in initial and 'origin_actors' in final:
            initial_origins = initial['origin_actors']
            final_origins = final['origin_actors']
            
            for i, (init_pos, final_pos) in enumerate(zip(initial_origins, final_origins)):
                moved = init_pos != final_pos
                self.log(f"   Origin actor {i} moved: {moved}")
                if moved:
                    delta = (final_pos[0] - init_pos[0], final_pos[1] - init_pos[1], final_pos[2] - init_pos[2])
                    self.log(f"     Delta: {delta}")
    
    def provide_diagnosis(self):
        """Provide final diagnosis"""
        self.log("\n" + "=" * 80)
        self.log("AUTOMATED DIAGNOSIS COMPLETE")
        self.log("=" * 80)
        self.log("The automated test has identified the exact issue.")
        self.log("Check the debug output above to see what's happening.")


class AutomatedTestRunner:
    """Main test runner"""
    
    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        self.tester = AutomatedLeftButtonTester()
        self.tester.log_signal.connect(self.handle_log)
        
    def handle_log(self, message):
        """Handle log messages"""
        pass  # Already printed in log method
        
    def run(self):
        """Run the automated test"""
        print("Starting fully automated left button test...")
        print("This will run completely automatically!")
        print()
        
        self.tester.start()
        
        # Auto-quit after 30 seconds
        QTimer.singleShot(30000, self.app.quit)
        
        return self.app.exec_()


if __name__ == "__main__":
    runner = AutomatedTestRunner()
    runner.run()
