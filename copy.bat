@echo off
:: Enable delayed expansion for variables at the beginning
setlocal enabledelayedexpansion

echo Creating backup copies with auto-incrementing revision numbers...

set DEST_DIR=e:\python\save
set REV_FILE=%DEST_DIR%\rev.txt

:: Create destination directory if it doesn't exist
if not exist "%DEST_DIR%" mkdir "%DEST_DIR%"

:: Check if rev.txt exists and read/increment revision number
if exist "%REV_FILE%" (
    :: Read current revision number from file
    set /p CURRENT_REV=<"%REV_FILE%"
    echo Current revision read from file: !CURRENT_REV!
    :: Increment revision number (handle empty case)
    if "!CURRENT_REV!"=="" (
        set NEXT_REV=1
    ) else (
        set /a NEXT_REV=!CURRENT_REV!+1
    )
    echo Calculated next revision: !NEXT_REV!
) else (
    :: First time - start with revision 1
    set NEXT_REV=1
    echo First time - starting with revision 1
)

:: Save new revision number to file
echo !NEXT_REV! > "%REV_FILE%"

:: Copy the 5 modular files with revision numbers
copy "step_viewer_tdk_modular.py" "%DEST_DIR%\step_viewer_tdk_modular_rev!NEXT_REV!.py"
copy "step_viewer.py" "%DEST_DIR%\step_viewer_rev!NEXT_REV!.py"
copy "step_viewer_tdk_modular_fixed.py" "%DEST_DIR%\step_viewer_tdk_modular_fixed_rev!NEXT_REV!.py"
copy "vtk_renderer.py" "%DEST_DIR%\vtk_renderer_rev!NEXT_REV!.py"
copy "step_loader.py" "%DEST_DIR%\step_loader_rev!NEXT_REV!.py"
copy "gui_components.py" "%DEST_DIR%\gui_components_rev!NEXT_REV!.py"
copy "requirements.txt" "%DEST_DIR%\requirements_rev!NEXT_REV!.txt"

echo.
echo Files copied to %DEST_DIR% with revision !NEXT_REV!:
echo - step_viewer_tdk_modular_rev!NEXT_REV!.py
echo - step_viewer_tdk_modular_fixed_rev!NEXT_REV!.py
echo - step_viewer_rev!NEXT_REV!.py
echo - vtk_renderer_rev!NEXT_REV!.py
echo - step_loader_rev!NEXT_REV!.py
echo - gui_components_rev!NEXT_REV!.py
echo - requirements_rev!NEXT_REV!.txt
echo.
echo Next revision will be: !NEXT_REV!
echo Revision number saved to: %REV_FILE%
pause