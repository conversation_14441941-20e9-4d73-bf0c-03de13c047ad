#!/usr/bin/env python3
"""
Complete verification test for green origin marker rotation.
This test will:
1. Load the viewer
2. Load a STEP file
3. Test each rotation button
4. Verify green markers move with the model
5. Report exact results
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class RotationVerificationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        self.test_results = []
        self.current_test = 0
        self.tests = [
            ('x', 15, 'X+ rotation'),
            ('x', -15, 'X- rotation'),
            ('y', 15, 'Y+ rotation'), 
            ('y', -15, 'Y- rotation'),
            ('z', 15, 'Z+ rotation'),
            ('z', -15, 'Z- rotation')
        ]
        
    def run_test(self):
        print("🧪 COMPLETE ROTATION VERIFICATION TEST")
        print("=" * 60)
        
        # Show the viewer
        self.viewer.show()
        
        # Load a STEP file
        step_file = "e:/Python/3d-view/test_files/simple_cube.step"
        if not os.path.exists(step_file):
            step_file = "e:/Python/3d-view/test_files/test_part.step"
        if not os.path.exists(step_file):
            # Create a simple test file path - use any available STEP file
            import glob
            step_files = glob.glob("e:/Python/3d-view/**/*.step", recursive=True)
            if step_files:
                step_file = step_files[0]
            else:
                print("❌ ERROR: No STEP files found for testing")
                return False
                
        print(f"📁 Loading STEP file: {step_file}")
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ ERROR: Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        time.sleep(2)
        
        # Check if green origin markers exist
        if not hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere'):
            print("❌ ERROR: Green origin sphere not found")
            return False
            
        if not self.viewer.vtk_renderer_left.part_origin_sphere:
            print("❌ ERROR: Green origin sphere is None")
            return False
            
        print("✅ Green origin markers found")
        
        # Get initial position
        initial_pos = self.viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
        print(f"📍 Initial green sphere position: ({initial_pos[0]:.3f}, {initial_pos[1]:.3f}, {initial_pos[2]:.3f})")
        
        # Test each rotation
        for i, (axis, degrees, description) in enumerate(self.tests):
            print(f"\n🔄 Test {i+1}/6: {description} ({degrees}°)")
            
            # Get position before rotation
            pos_before = self.viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
            print(f"   Before: ({pos_before[0]:.3f}, {pos_before[1]:.3f}, {pos_before[2]:.3f})")
            
            # Apply rotation
            self.viewer.rotate_shape(axis, degrees)
            
            # Wait for rotation to complete
            time.sleep(0.5)
            
            # Get position after rotation
            pos_after = self.viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
            print(f"   After:  ({pos_after[0]:.3f}, {pos_after[1]:.3f}, {pos_after[2]:.3f})")
            
            # Calculate movement
            movement_x = pos_after[0] - pos_before[0]
            movement_y = pos_after[1] - pos_before[1]
            movement_z = pos_after[2] - pos_before[2]
            total_movement = (movement_x**2 + movement_y**2 + movement_z**2)**0.5
            
            print(f"   Movement: ({movement_x:.3f}, {movement_y:.3f}, {movement_z:.3f})")
            print(f"   Distance: {total_movement:.3f} units")
            
            # Record result
            if total_movement > 0.001:  # Threshold for detecting movement
                result = "✅ SUCCESS"
                success = True
            else:
                result = "❌ FAILURE"
                success = False
                
            print(f"   Result: {result}")
            
            self.test_results.append({
                'test': description,
                'axis': axis,
                'degrees': degrees,
                'pos_before': pos_before,
                'pos_after': pos_after,
                'movement': (movement_x, movement_y, movement_z),
                'distance': total_movement,
                'success': success
            })
        
        # Print final results
        print("\n" + "=" * 60)
        print("📊 FINAL TEST RESULTS")
        print("=" * 60)
        
        success_count = sum(1 for r in self.test_results if r['success'])
        total_tests = len(self.test_results)
        
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test']}: {result['distance']:.3f} units movement")
            
        print(f"\n🎯 OVERALL RESULT: {success_count}/{total_tests} tests passed")
        
        if success_count == total_tests:
            print("🎉 ALL TESTS PASSED! Green origin markers are rotating correctly!")
            return True
        else:
            print("❌ SOME TESTS FAILED! Green origin markers are not rotating properly!")
            return False

def main():
    test = RotationVerificationTest()
    success = test.run_test()
    
    # Keep the viewer open for visual inspection
    print(f"\n👁️ Viewer is open for visual inspection...")
    print("   You can manually test the rotation buttons to verify the fix.")
    print("   Close the viewer window to exit.")
    
    # Run the Qt event loop
    sys.exit(test.app.exec_())

if __name__ == "__main__":
    main()
