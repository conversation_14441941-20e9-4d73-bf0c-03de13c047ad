#!/usr/bin/env python3
"""
Console-Only Debug - No GUI Required
This will analyze the code structure to find the mouse rotation issue
"""

import sys
import os
import inspect

def analyze_vtk_renderer():
    """Analyze the VTK renderer code structure"""
    print("🔍 CONSOLE-ONLY DEBUG - ANALYZING CODE STRUCTURE")
    print("=" * 55)
    
    try:
        # Import the VTK renderer module
        print("Step 1: Importing vtk_renderer module...")
        import vtk_renderer
        print("✅ vtk_renderer imported successfully")
        
        # Find the VTKRenderer class
        print("\nStep 2: Analyzing VTKRenderer class...")
        if hasattr(vtk_renderer, 'VTKRenderer'):
            vtk_class = vtk_renderer.VTKRenderer
            print("✅ VTKRenderer class found")
            
            # Check for SafeModelRotationStyle
            print("\nStep 3: Looking for SafeModelRotationStyle...")
            source = inspect.getsource(vtk_class)
            
            if 'SafeModelRotationStyle' in source:
                print("✅ SafeModelRotationStyle found in VTKRenderer")
                
                # Extract the SafeModelRotationStyle class definition
                lines = source.split('\n')
                in_safe_style = False
                safe_style_lines = []
                indent_level = None
                
                for line in lines:
                    if 'class SafeModelRotationStyle' in line:
                        in_safe_style = True
                        indent_level = len(line) - len(line.lstrip())
                        safe_style_lines.append(line)
                    elif in_safe_style:
                        current_indent = len(line) - len(line.lstrip())
                        if line.strip() and current_indent <= indent_level and not line.strip().startswith('#'):
                            # End of class
                            break
                        safe_style_lines.append(line)
                
                # Analyze the SafeModelRotationStyle
                safe_style_code = '\n'.join(safe_style_lines)
                
                print("\nStep 4: Analyzing SafeModelRotationStyle methods...")
                
                if '_rotate_all_model_actors' in safe_style_code:
                    print("✅ _rotate_all_model_actors method found")
                    
                    # Check what actors it includes
                    if 'part_origin_sphere' in safe_style_code:
                        print("✅ part_origin_sphere included in rotation")
                    else:
                        print("❌ part_origin_sphere NOT included in rotation")
                        
                    if 'part_origin_x_arrow' in safe_style_code:
                        print("✅ part_origin_x_arrow included in rotation")
                    else:
                        print("❌ part_origin_x_arrow NOT included in rotation")
                        
                    if 'step_actors' in safe_style_code:
                        print("✅ step_actors (multi-actor models) included")
                    else:
                        print("❌ step_actors NOT included")
                        
                    if 'step_actor' in safe_style_code:
                        print("✅ step_actor (single actor) included")
                    else:
                        print("❌ step_actor NOT included")
                        
                else:
                    print("❌ _rotate_all_model_actors method NOT found")
                
                if 'OnMouseMove' in safe_style_code:
                    print("✅ OnMouseMove method found")
                    
                    if '_rotate_all_model_actors' in safe_style_code and 'OnMouseMove' in safe_style_code:
                        # Check if OnMouseMove calls _rotate_all_model_actors
                        mouse_move_start = safe_style_code.find('def OnMouseMove')
                        if mouse_move_start != -1:
                            # Find the end of OnMouseMove method
                            next_def = safe_style_code.find('def ', mouse_move_start + 1)
                            if next_def == -1:
                                mouse_move_code = safe_style_code[mouse_move_start:]
                            else:
                                mouse_move_code = safe_style_code[mouse_move_start:next_def]
                                
                            if '_rotate_all_model_actors' in mouse_move_code:
                                print("✅ OnMouseMove calls _rotate_all_model_actors")
                            else:
                                print("❌ OnMouseMove does NOT call _rotate_all_model_actors")
                        else:
                            print("⚠️  Could not analyze OnMouseMove method")
                else:
                    print("❌ OnMouseMove method NOT found")
                    
            else:
                print("❌ SafeModelRotationStyle NOT found in VTKRenderer")
                
        else:
            print("❌ VTKRenderer class not found")
            
        print("\nStep 5: Checking for conflicting rotation code...")
        
        # Check for the problematic sync method
        if hasattr(vtk_renderer, 'VTKRenderer'):
            vtk_class = vtk_renderer.VTKRenderer
            source = inspect.getsource(vtk_class)
            
            if '_sync_part_origin_with_mouse_rotation' in source:
                print("⚠️  _sync_part_origin_with_mouse_rotation method found - this might conflict!")
            else:
                print("✅ No conflicting _sync_part_origin_with_mouse_rotation method")
                
            if 'on_interaction_event' in source:
                print("⚠️  on_interaction_event observer found - this might interfere!")
                
                # Check what the observer does
                if '_sync_part_origin_with_mouse_rotation' in source and 'on_interaction_event' in source:
                    print("🚨 POTENTIAL ISSUE: Observer calling _sync_part_origin_with_mouse_rotation")
                    print("   This could interfere with the custom mouse rotation!")
            else:
                print("✅ No conflicting interaction observers")
                
        print("\nStep 6: Analyzing the issue...")
        
        # Try to determine the root cause
        issues_found = []
        
        # Import and check the actual structure
        try:
            from vtk_renderer import VTKRenderer
            
            # Create a dummy instance to check methods
            print("\nStep 7: Testing method availability...")
            
            # Check if we can find the methods
            if hasattr(VTKRenderer, '__init__'):
                print("✅ VTKRenderer.__init__ found")
                
                # Get the source of __init__ to see how SafeModelRotationStyle is set up
                init_source = inspect.getsource(VTKRenderer.__init__)

                print(f"🔍 Searching for SafeModelRotationStyle instantiation...")

                # Look for the instantiation pattern
                if 'SafeModelRotationStyle(' in init_source:
                    print("✅ SafeModelRotationStyle instantiation found in __init__")

                    if 'SetInteractorStyle' in init_source:
                        print("✅ SetInteractorStyle is called")
                    else:
                        print("❌ SetInteractorStyle NOT called")
                        issues_found.append("SetInteractorStyle not called")

                    # Check for the specific line
                    lines = init_source.split('\n')
                    for i, line in enumerate(lines):
                        if 'SafeModelRotationStyle(' in line:
                            print(f"✅ Found instantiation at line {i+1}: {line.strip()}")

                elif 'SafeModelRotationStyle' in init_source:
                    print("⚠️  SafeModelRotationStyle mentioned but not instantiated")
                    print("   (class definition found but no instantiation)")
                    issues_found.append("SafeModelRotationStyle defined but not instantiated")
                else:
                    print("❌ SafeModelRotationStyle NOT found in __init__")
                    issues_found.append("SafeModelRotationStyle not found")
                    
        except Exception as e:
            print(f"❌ Error testing VTKRenderer: {e}")
            issues_found.append(f"VTKRenderer error: {e}")
            
        print("\n📋 DIAGNOSIS:")
        print("=" * 15)
        
        if not issues_found:
            print("🤔 Code structure looks correct, but mouse rotation still not working.")
            print("\n🔧 POSSIBLE CAUSES:")
            print("1. VTK event system not properly connected")
            print("2. Mouse events not reaching OnMouseMove")
            print("3. Actor rotation not being applied correctly")
            print("4. Conflicting rotation systems interfering")
            print("5. GUI/display issues preventing visual updates")
            
            print("\n💡 RECOMMENDED FIXES:")
            print("1. Add debug prints to OnMouseMove to see if it's called")
            print("2. Add debug prints to _rotate_all_model_actors to see if it's called")
            print("3. Check if actors are actually in the renderer")
            print("4. Verify VTK rendering is working")
        else:
            print("🚨 ISSUES FOUND:")
            for issue in issues_found:
                print(f"   - {issue}")
                
        print(f"\n✅ Analysis complete. Found {len(issues_found)} structural issues.")
        
    except Exception as e:
        print(f"❌ ANALYSIS FAILED: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    analyze_vtk_renderer()
