#!/usr/bin/env python3
"""
TEST: What text is actually displayed to the user in yellow text
This will capture the actual SetInput() calls to see what the user sees
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import Step<PERSON>iewerTDK

def capture_display_text(viewer):
    """Capture what text is actually displayed to the user"""
    print("\n📺 CAPTURING ACTUAL DISPLAY TEXT:")
    print("=" * 60)
    
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        # Check cursor text
        if hasattr(viewer, 'cursor_text_actor_left') and viewer.cursor_text_actor_left:
            cursor_text = viewer.cursor_text_actor_left.GetInput()
            print(f"LINE 1 (Cursor): {cursor_text}")
        else:
            print("LINE 1 (Cursor): NOT FOUND")
            
        # Check local origin text
        if hasattr(viewer, 'local_origin_text_actor_left') and viewer.local_origin_text_actor_left:
            local_text = viewer.local_origin_text_actor_left.GetInput()
            print(f"LINE 2 (Local Origin): {local_text}")
        else:
            print("LINE 2 (Local Origin): NOT FOUND")
            
        # Check world origin text
        if hasattr(viewer, 'world_origin_text_actor_left') and viewer.world_origin_text_actor_left:
            world_text = viewer.world_origin_text_actor_left.GetInput()
            print(f"LINE 3 (World Origin): {world_text}")
        else:
            print("LINE 3 (World Origin): NOT FOUND")
            
        # Check combined text (model data)
        if hasattr(viewer, 'combined_text_actor_left') and viewer.combined_text_actor_left:
            combined_text = viewer.combined_text_actor_left.GetInput()
            print(f"LINE 4 (Model): {combined_text}")
        else:
            print("LINE 4 (Model): NOT FOUND")
    else:
        print("❌ NO RENDERER FOUND")

def main():
    print("📺 ACTUAL DISPLAY TEXT TEST")
    print("=" * 60)
    print("This will show exactly what text the user sees")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load STEP file
    step_file = "e:/Python/3d-view/debug_save_test.STEP"
    if not os.path.exists(step_file):
        print(f"❌ ERROR: STEP file not found: {step_file}")
        return False
        
    print(f"\n📁 Loading STEP file: {step_file}")
    success = viewer.load_step_file_direct(step_file)
    if not success:
        print("❌ ERROR: Failed to load STEP file")
        return False
        
    print("✅ STEP file loaded successfully")
    
    # Wait for loading to complete
    app.processEvents()
    time.sleep(2)
    
    # Capture BEFORE rotation
    print("\n🔍 BEFORE ROTATION:")
    capture_display_text(viewer)
    
    # Apply rotation
    print(f"\n🔄 Applying X+ rotation (15 degrees)...")
    viewer.rotate_shape('x', 15)
    app.processEvents()
    time.sleep(1)
    
    # Capture AFTER rotation
    print("\n🔍 AFTER ROTATION:")
    capture_display_text(viewer)
    
    # Keep viewer open briefly
    print(f"\n👁️ Keeping viewer open for 3 seconds...")
    QTimer.singleShot(3000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ TEST COMPLETE: Now we know exactly what the user sees!")
    else:
        print("\n❌ TEST FAILED: Could not capture display text")
