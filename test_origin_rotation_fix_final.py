#!/usr/bin/env python3
"""
Final Test: Origin Rotation Fix
This test verifies that origin markers rotate with the model during mouse interaction
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class OriginRotationFixTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def start_test(self):
        """Start the test"""
        print("🎯 FINAL TEST: Origin Rotation Fix")
        print("=" * 40)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(2000, self.load_and_setup)
        
        return self.app.exec_()
        
    def load_and_setup(self):
        """Load model and setup overlays"""
        print("🔧 Loading model and setting up overlays...")
        
        try:
            # Load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                return
                
            # Wait for model to load, then setup overlays
            QTimer.singleShot(3000, self.setup_overlays)
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            
    def setup_overlays(self):
        """Setup origin overlays and bounding box"""
        print("🔧 Setting up overlays...")
        
        try:
            # Enable world origin overlay (red semicircle + XYZ arrows)
            if hasattr(self.viewer.vtk_renderer_left, 'create_origin_overlay'):
                self.viewer.vtk_renderer_left.create_origin_overlay()
                print("✅ World origin overlay enabled")
            
            # Enable part origin overlay (green sphere + arrows) if available
            if (hasattr(self.viewer, 'orig_pos_left') and 
                hasattr(self.viewer.vtk_renderer_left, 'create_part_origin_overlay')):
                self.viewer.vtk_renderer_left.create_part_origin_overlay(
                    self.viewer.orig_pos_left['x'],
                    self.viewer.orig_pos_left['y'],
                    self.viewer.orig_pos_left['z']
                )
                print("✅ Part origin overlay enabled")
            
            # Enable bounding box
            if hasattr(self.viewer, 'toggle_bounding_box_left'):
                self.viewer.toggle_bounding_box_left()
                print("✅ Bounding box enabled")
            
            # Start the interactive test
            QTimer.singleShot(2000, self.start_interactive_test)
            
        except Exception as e:
            print(f"❌ Error setting up overlays: {e}")
            
    def start_interactive_test(self):
        """Start the interactive test"""
        print("\n🖱️  INTERACTIVE TEST")
        print("=" * 25)
        print("INSTRUCTIONS:")
        print("1. Use your mouse to drag and rotate the model in the TOP viewer")
        print("2. Watch these elements during rotation:")
        print("   🔴 Red semicircle and XYZ arrows (world origin)")
        print("   🟢 Green sphere and arrows (part origin)")
        print("   📦 Red bounding box wireframe")
        print("   📝 Text overlay values")
        print()
        print("✅ EXPECTED BEHAVIOR:")
        print("   - ALL elements should rotate together with the model")
        print("   - Origin markers should follow the model rotation")
        print("   - Bounding box should update to match rotated model")
        print("   - Text should show changing rotation values")
        print()
        print("❌ BROKEN BEHAVIOR:")
        print("   - Origin markers stay fixed while model rotates")
        print("   - Bounding box doesn't update")
        print("   - Elements move independently")
        print()
        print("🔧 TECHNICAL DETAILS:")
        print("   - Mouse rotation uses TrackballActor (rotates model)")
        print("   - Interaction observers apply incremental rotations")
        print("   - Origin actors use RotateWXYZ() like button rotations")
        print("   - Bounding box recreated to match transformed model")
        print()
        print("Test the fix by rotating the model with your mouse!")
        print("Press Ctrl+C to exit when done.")
        
        # Show current fix status
        self.show_fix_status()
        
    def show_fix_status(self):
        """Show the current fix status"""
        print("\n🔍 FIX STATUS:")
        print("-" * 15)
        
        # Check if TrackballActor is enabled
        try:
            if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                interactor = self.viewer.vtk_renderer_left.interactor
                if interactor:
                    style = interactor.GetInteractorStyle()
                    style_name = style.__class__.__name__
                    if "TrackballActor" in style_name:
                        print("✅ TrackballActor style: ENABLED")
                    else:
                        print(f"❌ Wrong style: {style_name}")
                else:
                    print("❌ No interactor found")
            else:
                print("❌ No interactor available")
        except Exception as e:
            print(f"❌ Error checking interactor: {e}")
            
        # Check if rotation tracking is initialized
        try:
            if hasattr(self.viewer.vtk_renderer_left, 'last_model_orientation'):
                if self.viewer.vtk_renderer_left.last_model_orientation is not None:
                    print("✅ Rotation tracking: INITIALIZED")
                else:
                    print("❌ Rotation tracking: NOT INITIALIZED")
            else:
                print("❌ Rotation tracking: NOT AVAILABLE")
        except Exception as e:
            print(f"❌ Error checking rotation tracking: {e}")
            
        # Check if origin overlays exist
        try:
            if hasattr(self.viewer.vtk_renderer_left, 'origin_actors'):
                count = len(self.viewer.vtk_renderer_left.origin_actors)
                if count > 0:
                    print(f"✅ World origin actors: {count} FOUND")
                else:
                    print("❌ World origin actors: NONE")
            else:
                print("❌ World origin actors: NOT AVAILABLE")
        except Exception as e:
            print(f"❌ Error checking origin actors: {e}")
            
        print("\n🚀 Ready for testing!")

if __name__ == "__main__":
    test = OriginRotationFixTest()
    try:
        sys.exit(test.start_test())
    except KeyboardInterrupt:
        print("\n👋 Test completed by user")
        sys.exit(0)
