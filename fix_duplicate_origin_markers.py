#!/usr/bin/env python3
"""
Automatic Fix for Duplicate Origin Markers Issue

This script will:
1. Test the current reset functionality
2. Identify the duplicate origin marker problem
3. Apply a comprehensive fix to the reset_to_original function
4. Test that the fix works correctly

The issue: When using direction buttons and then pressing "Reset to Original",
duplicate origin markers appear because the original markers are not properly cleared.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer import StepViewerTDK

class OriginMarkerFixer:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.fix_applied = False
        
    def run_automatic_fix(self):
        print("🔧 AUTOMATIC ORIGIN MARKER DUPLICATE FIX")
        print("=" * 60)
        print("This script will automatically fix the duplicate origin marker issue")
        print("that occurs when using 'Reset to Original' after direction buttons.")
        print("=" * 60)
        
        # Step 1: Test current behavior
        print("\n1. Testing current behavior...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.load_and_test)
        self.app.exec_()
        
    def load_and_test(self):
        print("2. Loading STEP file...")
        
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.apply_fix_and_restart()
            return
            
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            self.apply_fix_and_restart()
            return
            
        print("✅ STEP file loaded")
        QTimer.singleShot(1000, self.test_current_behavior)
        
    def test_current_behavior(self):
        print("\n3. Testing current reset behavior...")
        
        # Apply some rotation to move origin markers
        print("   Applying rotation to move origin markers...")
        self.viewer.rotate_shape('x', 30)
        
        QTimer.singleShot(1000, self.test_reset)
        
    def test_reset(self):
        print("   Testing reset to original...")
        
        # Count origin actors before reset
        origin_count_before = self.count_origin_actors()
        print(f"   Origin actors before reset: {origin_count_before}")
        
        # Apply reset
        self.viewer.reset_to_original()
        
        QTimer.singleShot(1000, self.check_for_duplicates)
        
    def count_origin_actors(self):
        """Count all origin-related actors in the renderer"""
        count = 0
        try:
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                renderer = self.viewer.vtk_renderer_left.renderer
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    while True:
                        actor = actors.GetNextActor()
                        if not actor:
                            break
                        # Check if this looks like an origin actor (red or green spheres/arrows)
                        prop = actor.GetProperty()
                        if prop:
                            color = prop.GetColor()
                            # Red (world origin) or green (part origin) markers
                            if (color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2) or \
                               (color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2):
                                count += 1
        except Exception as e:
            print(f"   Error counting origin actors: {e}")
        return count
        
    def check_for_duplicates(self):
        print("\n4. Checking for duplicate origin markers...")
        
        origin_count_after = self.count_origin_actors()
        print(f"   Origin actors after reset: {origin_count_after}")
        
        if origin_count_after > 6:  # Normal is ~6 actors (semicircle + 3 arrows + part origin + extras)
            print("❌ DUPLICATE ORIGIN MARKERS DETECTED!")
            print("   The reset function is not properly clearing existing origin markers.")
            print("   Applying comprehensive fix...")
            self.apply_fix_and_restart()
        else:
            print("✅ No duplicate origin markers detected")
            print("   The reset function appears to be working correctly.")
            QTimer.singleShot(2000, self.app.quit)
            
    def apply_fix_and_restart(self):
        print("\n5. Applying comprehensive fix to reset_to_original function...")
        
        # Close current viewer
        if self.viewer:
            self.viewer.close()
            
        # Apply the fix
        self.apply_comprehensive_fix()
        
        # Restart and test
        print("\n6. Restarting with fix applied...")
        QTimer.singleShot(2000, self.test_fix)
        
    def apply_comprehensive_fix(self):
        """Apply a comprehensive fix to the reset_to_original function"""
        print("   🔧 Applying comprehensive origin marker fix...")
        
        # Read the current step_viewer.py file
        try:
            with open('step_viewer.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create the comprehensive fix code
            fix_code = '''            # COMPREHENSIVE FIX: Completely clear ALL origin-related actors
            print("🎯 COMPREHENSIVE FIX: Removing ALL origin-related actors for TOP viewer")
            
            # Method 1: Use existing clear function
            if hasattr(self.vtk_renderer_left, 'clear_origin_overlay'):
                self.vtk_renderer_left.clear_origin_overlay()
            
            # Method 2: Manually remove ALL actors that look like origin markers
            if hasattr(self.vtk_renderer_left, 'renderer') and self.vtk_renderer_left.renderer:
                renderer = self.vtk_renderer_left.renderer
                actors_to_remove = []
                
                # Find all origin-related actors
                actors = renderer.GetActors()
                actors.InitTraversal()
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break
                    
                    # Check if this looks like an origin actor
                    prop = actor.GetProperty()
                    if prop:
                        color = prop.GetColor()
                        # Red (world origin) or green (part origin) markers
                        if (color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2) or \\
                           (color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2):
                            actors_to_remove.append(actor)
                
                # Remove all found origin actors
                for actor in actors_to_remove:
                    renderer.RemoveActor(actor)
                    print(f"✅ COMPREHENSIVE FIX: Removed origin actor")
                
                print(f"✅ COMPREHENSIVE FIX: Removed {len(actors_to_remove)} origin actors")
            
            # Method 3: Clear all origin-related lists
            if hasattr(self.vtk_renderer_left, 'origin_actors'):
                self.vtk_renderer_left.origin_actors = []
            if hasattr(self.vtk_renderer_left, 'part_origin_actors'):
                self.vtk_renderer_left.part_origin_actors = []
            if hasattr(self.vtk_renderer_left, 'part_origin_sphere'):
                self.vtk_renderer_left.part_origin_sphere = None'''
            
            # Find the location to insert the fix (after camera reset, before creating new overlays)
            target_pattern = '# Force render to update display\n            self.vtk_renderer_left.render_window.Render()'
            
            if target_pattern in content:
                # Insert the fix after the render call
                content = content.replace(
                    target_pattern,
                    target_pattern + '\n\n' + fix_code
                )
                
                # Write the fixed content back
                with open('step_viewer.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                print("✅ Comprehensive fix applied to step_viewer.py")
                self.fix_applied = True
            else:
                print("❌ Could not find insertion point for fix")
                
        except Exception as e:
            print(f"❌ Error applying fix: {e}")
            
    def test_fix(self):
        if not self.fix_applied:
            print("❌ Fix was not applied successfully")
            self.app.quit()
            return
            
        print("7. Testing the fix...")
        
        # Create new viewer with fixed code
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.test_fixed_behavior)
        
    def test_fixed_behavior(self):
        print("   Loading STEP file with fix...")
        
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file for testing")
            self.app.quit()
            return
            
        print("   Applying rotation...")
        self.viewer.rotate_shape('x', 30)
        
        QTimer.singleShot(1000, self.test_fixed_reset)
        
    def test_fixed_reset(self):
        print("   Testing reset with fix...")
        
        # Count before reset
        before_count = self.count_origin_actors()
        print(f"   Origin actors before reset: {before_count}")
        
        # Apply reset
        self.viewer.reset_to_original()
        
        QTimer.singleShot(1000, self.verify_fix)
        
    def verify_fix(self):
        print("\n8. Verifying fix results...")
        
        after_count = self.count_origin_actors()
        print(f"   Origin actors after reset: {after_count}")
        
        if after_count <= 6:  # Normal expected count
            print("🎉 SUCCESS: Fix applied successfully!")
            print("✅ No more duplicate origin markers")
            print("✅ Reset to Original now works correctly")
        else:
            print("❌ Fix did not resolve the issue completely")
            print("   Manual intervention may be required")
            
        print("\n" + "=" * 60)
        print("AUTOMATIC FIX COMPLETE")
        print("You can now use the GUI normally.")
        print("Direction buttons + Reset to Original should work without duplicates.")
        print("=" * 60)
        
        # Keep GUI open for manual testing
        QTimer.singleShot(5000, self.app.quit)

if __name__ == '__main__':
    fixer = OriginMarkerFixer()
    fixer.run_automatic_fix()
