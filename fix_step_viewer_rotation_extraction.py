#!/usr/bin/env python3
"""
Fix for STEP Viewer Rotation Extraction
This provides the corrected _extract_rotation_from_vtk_actor method
that actually reads VTK actor transformations
"""

import math
import numpy as np

def extract_rotation_from_vtk_actor_FIXED(self, viewer):
    """
    FIXED VERSION: Extract rotation values from actual VTK actor transformations
    This reads the transformation matrix from the VTK actors that were rotated
    """
    try:
        print(f"🔧 FIXED: Extracting rotation from VTK actors in {viewer} viewer")
        
        if viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right
        
        if not vtk_renderer:
            print(f"❌ No VTK renderer found for {viewer} viewer")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Get the VTK actors (prefer multi-actors for multi-color models)
        actors = None
        if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
            actors = vtk_renderer.step_actors
            print(f"🔧 Found {len(actors)} multi-color actors")
        elif hasattr(vtk_renderer, 'step_actor') and vtk_renderer.step_actor:
            actors = [vtk_renderer.step_actor]
            print(f"🔧 Found 1 single actor")
        else:
            print(f"❌ No VTK actors found in {viewer} viewer")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Extract rotation from the first actor (they should all have the same transformation)
        actor = actors[0]
        
        # Get the actor's transformation matrix
        matrix = actor.GetMatrix()
        if not matrix:
            print("❌ No transformation matrix found in actor")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}
        
        # Convert VTK matrix to numpy array
        np_matrix = np.zeros((4, 4))
        for i in range(4):
            for j in range(4):
                np_matrix[i][j] = matrix.GetElement(i, j)
        
        print(f"🔧 VTK Actor transformation matrix:")
        for row in np_matrix:
            print(f"   {row}")
        
        # Extract rotation angles from the 3x3 rotation part
        R = np_matrix[:3, :3]
        
        # Calculate rotation angles using ZYX Euler angle extraction
        sin_y = -R[2, 0]
        sin_y = max(-1.0, min(1.0, sin_y))  # Clamp to [-1, 1]
        y_rad = math.asin(sin_y)
        
        # Check for gimbal lock
        if abs(math.cos(y_rad)) > 1e-6:
            # No gimbal lock
            x_rad = math.atan2(R[2, 1], R[2, 2])
            z_rad = math.atan2(R[1, 0], R[0, 0])
        else:
            # Gimbal lock case
            x_rad = math.atan2(-R[1, 2], R[1, 1])
            z_rad = 0.0
        
        # Convert to degrees and negate (VTK uses opposite direction)
        x_deg = -math.degrees(x_rad)
        y_deg = -math.degrees(y_rad)
        z_deg = -math.degrees(z_rad)
        
        rotation = {'x': x_deg, 'y': y_deg, 'z': z_deg}
        print(f"✅ FIXED: Extracted actual VTK actor rotation: {rotation}")
        
        return rotation
        
    except Exception as e:
        print(f"❌ Error extracting rotation from VTK actor: {e}")
        import traceback
        traceback.print_exc()
        return {'x': 0.0, 'y': 0.0, 'z': 0.0}

def create_test_with_actual_rotations():
    """
    Create a test that demonstrates the difference between the two methods
    """
    print("🧪 DEMONSTRATION: Two Ways to Create STEP Files")
    print("=" * 60)
    
    print("📋 CURRENT PROBLEM:")
    print("   1. User rotates model visually (VTK actors get transformed)")
    print("   2. User clicks 'Save STEP File' (Green button)")
    print("   3. System calls _extract_rotation_from_vtk_actor()")
    print("   4. Method returns {'x': 0, 'y': 0, 'z': 0} (WRONG!)")
    print("   5. System saves original file (no transformations applied)")
    print()
    
    print("📋 SOLUTION:")
    print("   1. Fix _extract_rotation_from_vtk_actor() to read actual VTK matrices")
    print("   2. Extract real rotation values like {'x': 30, 'y': 45, 'z': 60}")
    print("   3. Apply those rotations to OpenCASCADE geometry")
    print("   4. Save transformed geometry to new STEP file")
    print()
    
    print("📋 TWO STEP FILE CREATION METHODS:")
    print("   METHOD 1 (Green Button): Transform geometry + save")
    print("   METHOD 2 (Blue Button): Copy original file")
    print()
    
    print("🔧 The fix is to replace the _extract_rotation_from_vtk_actor method")
    print("   with the FIXED version that actually reads VTK transformations.")

if __name__ == "__main__":
    create_test_with_actual_rotations()
