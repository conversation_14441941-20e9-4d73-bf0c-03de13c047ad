#!/usr/bin/env python3
"""
Test cursor visibility based on proper model loading detection
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_cursor_model_loading():
    """Test cursor visibility with proper model loading detection"""
    print("🔧 Testing cursor visibility with model loading detection...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Check INITIAL state (no model loaded)
        print("\n🔧 Checking INITIAL state (no model loaded)...")
        
        # Check model loading state
        top_has_model = hasattr(viewer, 'step_loader_left') and viewer.step_loader_left.current_polydata is not None
        bottom_has_model = hasattr(viewer, 'step_loader_right') and viewer.step_loader_right.current_polydata is not None
        
        print(f"TOP has model: {top_has_model}")
        print(f"BOTTOM has model: {bottom_has_model}")
        
        if not top_has_model:
            print("✅ GOOD: TOP model correctly detected as NOT loaded")
        else:
            print("❌ PROBLEM: TOP model incorrectly detected as loaded")
            
        if not bottom_has_model:
            print("✅ GOOD: BOTTOM model correctly detected as NOT loaded")
        else:
            print("❌ PROBLEM: BOTTOM model incorrectly detected as loaded")
        
        # Force text overlay update to see initial behavior
        print("\n🔧 Forcing text overlay update (no model loaded)...")
        viewer.update_text_overlays()
        
        # Check cursor text visibility BEFORE loading
        if hasattr(viewer, 'cursor_text_actor_left'):
            visibility_before = viewer.cursor_text_actor_left.GetVisibility()
            print(f"TOP cursor visibility BEFORE loading: {visibility_before} (should be 0)")
            if visibility_before == 0:
                print("✅ GOOD: TOP cursor hidden when no model loaded")
            else:
                print("❌ PROBLEM: TOP cursor visible when no model loaded")
        else:
            print("❌ TOP cursor text actor not found")
        
        if hasattr(viewer, 'cursor_text_actor_right'):
            visibility_before = viewer.cursor_text_actor_right.GetVisibility()
            print(f"BOTTOM cursor visibility BEFORE loading: {visibility_before} (should be 0)")
            if visibility_before == 0:
                print("✅ GOOD: BOTTOM cursor hidden when no model loaded")
            else:
                print("❌ PROBLEM: BOTTOM cursor visible when no model loaded")
        else:
            print("❌ BOTTOM cursor text actor not found")
        
        # Load STEP file in TOP viewer
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file in TOP viewer: {step_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded in TOP viewer")
            
            # Check model loading state AFTER loading
            print("\n🔧 Checking model loading state AFTER loading...")
            top_has_model_after = hasattr(viewer, 'step_loader_left') and viewer.step_loader_left.current_polydata is not None
            bottom_has_model_after = hasattr(viewer, 'step_loader_right') and viewer.step_loader_right.current_polydata is not None
            
            print(f"TOP has model AFTER loading: {top_has_model_after}")
            print(f"BOTTOM has model AFTER loading: {bottom_has_model_after}")
            
            if top_has_model_after:
                print("✅ GOOD: TOP model correctly detected as loaded")
            else:
                print("❌ PROBLEM: TOP model not detected as loaded after loading")
                
            if not bottom_has_model_after:
                print("✅ GOOD: BOTTOM model correctly detected as NOT loaded")
            else:
                print("❌ PROBLEM: BOTTOM model incorrectly detected as loaded")
            
            # Check cursor text visibility AFTER loading
            print("\n🔧 Checking cursor visibility AFTER loading...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility_after = viewer.cursor_text_actor_left.GetVisibility()
                input_text = viewer.cursor_text_actor_left.GetInput()
                print(f"TOP cursor visibility AFTER loading: {visibility_after} (should be 1)")
                print(f"TOP cursor text: {repr(input_text)}")
                
                if visibility_after == 1:
                    print("✅ GOOD: TOP cursor visible after model loaded")
                else:
                    print("❌ PROBLEM: TOP cursor hidden after model loaded")
            else:
                print("❌ TOP cursor text actor not found after loading")
            
            if hasattr(viewer, 'cursor_text_actor_right'):
                visibility_after = viewer.cursor_text_actor_right.GetVisibility()
                input_text = viewer.cursor_text_actor_right.GetInput()
                print(f"BOTTOM cursor visibility AFTER loading: {visibility_after} (should be 0)")
                print(f"BOTTOM cursor text: {repr(input_text)}")
                
                if visibility_after == 0:
                    print("✅ GOOD: BOTTOM cursor hidden when no model loaded")
                else:
                    print("❌ PROBLEM: BOTTOM cursor visible when no model loaded")
            else:
                print("❌ BOTTOM cursor text actor not found after loading")
            
            return True
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== CURSOR MODEL LOADING TEST ===")
    success = test_cursor_model_loading()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
