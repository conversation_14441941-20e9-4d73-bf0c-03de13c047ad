#!/usr/bin/env python3
"""
Comprehensive Mouse Rotation Debug Program
This will help us understand exactly what's happening during mouse rotation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton
from PyQt5.QtCore import QTimer, pyqtSignal
import time

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class MouseRotationDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)
        
        self.viewer = None
        self.debug_log = []
        self.monitoring = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup debug UI"""
        self.setWindowTitle("Mouse Rotation Debugger")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Debug log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # Control buttons
        self.start_btn = QPushButton("Start Viewer & Debug")
        self.start_btn.clicked.connect(self.start_debug)
        layout.addWidget(self.start_btn)
        
        self.analyze_btn = QPushButton("Analyze Current State")
        self.analyze_btn.clicked.connect(self.analyze_state)
        layout.addWidget(self.analyze_btn)
        
        self.monitor_btn = QPushButton("Start Monitoring")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        layout.addWidget(self.monitor_btn)
        
        # Timer for continuous monitoring
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_actors)
        
    def log(self, message):
        """Add message to debug log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.debug_log.append(log_entry)
        self.log_display.append(log_entry)
        print(log_entry)  # Also print to console
        
    def start_debug(self):
        """Start the viewer and begin debugging"""
        self.log("🚀 Starting 3D STEP Viewer for debugging...")
        
        try:
            # Create and show the viewer
            self.viewer = StepViewerTDK()
            self.viewer.show()
            
            self.log("✅ Viewer started successfully")
            
            # Wait for initialization, then analyze
            QTimer.singleShot(3000, self.initial_analysis)
            
        except Exception as e:
            self.log(f"❌ Error starting viewer: {e}")
            
    def initial_analysis(self):
        """Perform initial analysis after viewer loads"""
        self.log("\n🔍 INITIAL ANALYSIS")
        self.log("=" * 40)
        
        if not self.viewer:
            self.log("❌ No viewer available")
            return
            
        try:
            # Check interactor style
            left_renderer = self.viewer.vtk_renderer_left
            if hasattr(left_renderer, 'interactor') and left_renderer.interactor:
                style = left_renderer.interactor.GetInteractorStyle()
                style_name = style.__class__.__name__
                self.log(f"🖱️  Interactor Style: {style_name}")
                
                # Check if it's our custom style
                if hasattr(style, '_rotate_all_model_actors'):
                    self.log("✅ Custom SafeModelRotationStyle detected")
                    
                    # Check what actors it would rotate
                    if hasattr(style, 'vtk_renderer_obj'):
                        renderer_obj = style.vtk_renderer_obj
                        actors_count = 0
                        
                        if hasattr(renderer_obj, 'step_actors') and renderer_obj.step_actors:
                            actors_count += len(renderer_obj.step_actors)
                            self.log(f"📦 Model actors: {len(renderer_obj.step_actors)}")
                        elif hasattr(renderer_obj, 'step_actor') and renderer_obj.step_actor:
                            actors_count += 1
                            self.log(f"📦 Single model actor: 1")
                            
                        # Check part origin actors
                        part_origin_count = 0
                        if hasattr(renderer_obj, 'part_origin_sphere') and renderer_obj.part_origin_sphere:
                            part_origin_count += 1
                        if hasattr(renderer_obj, 'part_origin_x_arrow') and renderer_obj.part_origin_x_arrow:
                            part_origin_count += 1
                        if hasattr(renderer_obj, 'part_origin_y_arrow') and renderer_obj.part_origin_y_arrow:
                            part_origin_count += 1
                        if hasattr(renderer_obj, 'part_origin_z_arrow') and renderer_obj.part_origin_z_arrow:
                            part_origin_count += 1
                            
                        self.log(f"🟢 Part origin actors: {part_origin_count}")
                        self.log(f"🎯 Total actors that should rotate: {actors_count + part_origin_count}")
                        
                else:
                    self.log("❌ Custom style not detected")
            else:
                self.log("❌ No interactor found")
                
            self.log("\n📋 INSTRUCTIONS:")
            self.log("1. Load a STEP file in the viewer")
            self.log("2. Enable origin overlays if needed")
            self.log("3. Click 'Start Monitoring' below")
            self.log("4. Try mouse rotation in the TOP viewer")
            self.log("5. Watch the debug output for actor changes")
            
        except Exception as e:
            self.log(f"❌ Error in initial analysis: {e}")
            
    def analyze_state(self):
        """Analyze current state of actors"""
        if not self.viewer:
            self.log("❌ No viewer available")
            return
            
        self.log("\n🔍 CURRENT STATE ANALYSIS")
        self.log("=" * 30)
        
        try:
            left_renderer = self.viewer.vtk_renderer_left
            
            # Check model actors
            model_actors = []
            if hasattr(left_renderer, 'step_actors') and left_renderer.step_actors:
                model_actors = left_renderer.step_actors
                self.log(f"📦 Model actors: {len(model_actors)}")
                for i, actor in enumerate(model_actors):
                    if actor:
                        pos = actor.GetPosition()
                        orient = actor.GetOrientation()
                        self.log(f"   Actor {i}: Pos({pos[0]:.2f},{pos[1]:.2f},{pos[2]:.2f}) Orient({orient[0]:.1f}°,{orient[1]:.1f}°,{orient[2]:.1f}°)")
            elif hasattr(left_renderer, 'step_actor') and left_renderer.step_actor:
                model_actors = [left_renderer.step_actor]
                actor = left_renderer.step_actor
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                self.log(f"📦 Single model actor: Pos({pos[0]:.2f},{pos[1]:.2f},{pos[2]:.2f}) Orient({orient[0]:.1f}°,{orient[1]:.1f}°,{orient[2]:.1f}°)")
            else:
                self.log("❌ No model actors found")
                
            # Check part origin actors
            part_actors = []
            if hasattr(left_renderer, 'part_origin_sphere') and left_renderer.part_origin_sphere:
                part_actors.append(('sphere', left_renderer.part_origin_sphere))
            if hasattr(left_renderer, 'part_origin_x_arrow') and left_renderer.part_origin_x_arrow:
                part_actors.append(('x_arrow', left_renderer.part_origin_x_arrow))
            if hasattr(left_renderer, 'part_origin_y_arrow') and left_renderer.part_origin_y_arrow:
                part_actors.append(('y_arrow', left_renderer.part_origin_y_arrow))
            if hasattr(left_renderer, 'part_origin_z_arrow') and left_renderer.part_origin_z_arrow:
                part_actors.append(('z_arrow', left_renderer.part_origin_z_arrow))
                
            if part_actors:
                self.log(f"🟢 Part origin actors: {len(part_actors)}")
                for name, actor in part_actors:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    self.log(f"   {name}: Pos({pos[0]:.2f},{pos[1]:.2f},{pos[2]:.2f}) Orient({orient[0]:.1f}°,{orient[1]:.1f}°,{orient[2]:.1f}°)")
            else:
                self.log("❌ No part origin actors found")
                
            # Check world origin actors
            if hasattr(left_renderer, 'origin_actors') and left_renderer.origin_actors:
                self.log(f"🔴 World origin actors: {len(left_renderer.origin_actors)}")
            else:
                self.log("❌ No world origin actors found")
                
        except Exception as e:
            self.log(f"❌ Error analyzing state: {e}")
            
    def toggle_monitoring(self):
        """Toggle continuous monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_btn.setText("Stop Monitoring")
            self.monitor_timer.start(1000)  # Monitor every second
            self.log("🔄 Started continuous monitoring...")
        else:
            self.monitoring = False
            self.monitor_btn.setText("Start Monitoring")
            self.monitor_timer.stop()
            self.log("⏹️  Stopped monitoring")
            
    def monitor_actors(self):
        """Monitor actor changes continuously"""
        if not self.viewer:
            return
            
        try:
            left_renderer = self.viewer.vtk_renderer_left
            
            # Get current orientations
            current_orientations = {}
            
            # Model actors
            if hasattr(left_renderer, 'step_actors') and left_renderer.step_actors:
                for i, actor in enumerate(left_renderer.step_actors):
                    if actor:
                        orient = actor.GetOrientation()
                        current_orientations[f'model_{i}'] = orient
            elif hasattr(left_renderer, 'step_actor') and left_renderer.step_actor:
                orient = left_renderer.step_actor.GetOrientation()
                current_orientations['model_single'] = orient
                
            # Part origin actors
            if hasattr(left_renderer, 'part_origin_sphere') and left_renderer.part_origin_sphere:
                orient = left_renderer.part_origin_sphere.GetOrientation()
                current_orientations['part_sphere'] = orient
                
            # Check if anything changed significantly
            if hasattr(self, 'last_orientations'):
                changes_detected = False
                for key, orient in current_orientations.items():
                    if key in self.last_orientations:
                        last_orient = self.last_orientations[key]
                        if (abs(orient[0] - last_orient[0]) > 1.0 or 
                            abs(orient[1] - last_orient[1]) > 1.0 or 
                            abs(orient[2] - last_orient[2]) > 1.0):
                            changes_detected = True
                            self.log(f"🔄 {key} orientation changed: {last_orient} → {orient}")
                            
                if not changes_detected:
                    # Only log every 10 seconds if no changes
                    if not hasattr(self, 'last_no_change_log'):
                        self.last_no_change_log = time.time()
                    elif time.time() - self.last_no_change_log > 10:
                        self.log("⏸️  No orientation changes detected (monitoring...)")
                        self.last_no_change_log = time.time()
            
            self.last_orientations = current_orientations
            
        except Exception as e:
            self.log(f"❌ Monitoring error: {e}")

def main():
    app = QApplication(sys.argv)
    debugger = MouseRotationDebugger()
    debugger.show()
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
