#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_viewer import Step<PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

print("=== AUTOMATED ROTATION SAVE TEST ===")

# Create Qt application
app = QApplication(sys.argv)

# Create the viewer
viewer = StepViewerTDK()
viewer.show()

def run_test():
    print("\n1. Loading original STEP file...")
    
    # Load original file in TOP viewer
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct('SOIC16P127_1270X940X610L89X51.STEP')
    
    if not success:
        print("❌ Failed to load original file")
        app.quit()
        return
        
    print("✅ Original file loaded in TOP viewer")
    
    # Wait a moment for loading to complete
    QTimer.singleShot(1000, apply_rotations)

def apply_rotations():
    print("\n2. Applying rotations...")

    # Apply some rotations using the rotation methods
    print("   Applying X+ rotation (15 degrees)...")
    viewer.rotate_shape('x', 15)

    print("   Applying Y+ rotation (15 degrees)...")
    viewer.rotate_shape('y', 15)

    print("   Applying Z+ rotation (15 degrees)...")
    viewer.rotate_shape('z', 15)

    print("✅ Rotations applied")
    
    # Wait a moment for rotations to complete
    QTimer.singleShot(500, check_transforms)

def check_transforms():
    print("\n3. Checking VTK transforms...")
    
    # Check if transformations are detected
    renderer = viewer.vtk_renderer_left
    step_actor = renderer.step_actor if hasattr(renderer, 'step_actor') else None
    
    if step_actor:
        user_transform = step_actor.GetUserTransform()
        print(f"   VTK actor found: {step_actor}")
        print(f"   User transform: {user_transform}")
        
        if user_transform:
            print("✅ User transform detected - rotations should be saved")
        else:
            print("❌ No user transform - rotations will NOT be saved")
            print("   This is the problem - rotation buttons don't create user transforms")
    else:
        print("❌ No VTK actor found")
    
    # Wait a moment then save
    QTimer.singleShot(500, save_file)

def save_file():
    print("\n4. Saving with Option 1...")
    
    # Save with Option 1 (this will show debug output)
    success = viewer.save_step_file_option1()
    
    if success:
        print("✅ File saved")
    else:
        print("❌ File save failed")
    
    # Wait a moment then test loading
    QTimer.singleShot(500, test_loading)

def test_loading():
    print("\n5. Testing saved file...")
    
    # Load saved file in BOTTOM viewer
    viewer.active_viewer = "bottom"
    success = viewer.load_step_file_direct('test-opt1.step')
    
    if success:
        print("✅ Saved file loaded in BOTTOM viewer")
        print("\n6. VISUAL COMPARISON:")
        print("   Look at both viewers - do they have the same rotation?")
        print("   If YES: Option 1 is working correctly")
        print("   If NO: Option 1 is not saving rotations")
    else:
        print("❌ Failed to load saved file")
    
    print("\n=== TEST COMPLETE ===")
    print("Check the viewers visually to see if rotations match")

# Start the test after a short delay
QTimer.singleShot(2000, run_test)

# Run the application
app.exec_()
