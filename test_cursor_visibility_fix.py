#!/usr/bin/env python3
"""
Test the cursor visibility fix with enhanced properties
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_cursor_visibility_fix():
    """Test cursor text visibility fix with enhanced properties"""
    print("🔧 Testing cursor visibility fix...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load STEP file in TOP viewer
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file in TOP viewer: {step_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded in TOP viewer")
            
            # Check cursor text properties after loading with fixes
            print("\n🔧 Checking ENHANCED cursor text properties...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility = viewer.cursor_text_actor_left.GetVisibility()
                input_text = viewer.cursor_text_actor_left.GetInput()
                position = viewer.cursor_text_actor_left.GetPosition()
                font_size = viewer.cursor_text_actor_left.GetTextProperty().GetFontSize()
                color = viewer.cursor_text_actor_left.GetTextProperty().GetColor()
                bold = viewer.cursor_text_actor_left.GetTextProperty().GetBold()
                shadow = viewer.cursor_text_actor_left.GetTextProperty().GetShadow()
                layer = viewer.cursor_text_actor_left.GetLayerNumber()
                
                print(f"✅ TOP cursor text actor ENHANCED properties:")
                print(f"   Visibility: {visibility} (1=visible, 0=hidden)")
                print(f"   Text: {repr(input_text)}")
                print(f"   Position: {position}")
                print(f"   Font size: {font_size} (enhanced from 14 to 16)")
                print(f"   Color: {color} (bright yellow)")
                print(f"   Bold: {bold}")
                print(f"   Shadow: {shadow} (added for visibility)")
                print(f"   Layer: {layer} (should be 10 for top layer)")
                
                # Check if enhancements are applied
                if font_size >= 16:
                    print("✅ GOOD: Font size enhanced to 16+")
                else:
                    print("❌ PROBLEM: Font size not enhanced")
                    
                if shadow:
                    print("✅ GOOD: Shadow enabled for better visibility")
                else:
                    print("❌ PROBLEM: Shadow not enabled")
                    
                if layer == 10:
                    print("✅ GOOD: Layer set to 10 (top layer)")
                else:
                    print(f"❌ PROBLEM: Layer is {layer}, should be 10")
                    
                if visibility == 1:
                    print("✅ GOOD: TOP cursor text is VISIBLE with enhancements")
                else:
                    print("❌ PROBLEM: TOP cursor text is HIDDEN despite enhancements")
            else:
                print("❌ TOP cursor text actor not found after loading")
            
            # Check BOTTOM cursor text too
            if hasattr(viewer, 'cursor_text_actor_right'):
                visibility = viewer.cursor_text_actor_right.GetVisibility()
                input_text = viewer.cursor_text_actor_right.GetInput()
                font_size = viewer.cursor_text_actor_right.GetTextProperty().GetFontSize()
                shadow = viewer.cursor_text_actor_right.GetTextProperty().GetShadow()
                layer = viewer.cursor_text_actor_right.GetLayerNumber()
                
                print(f"✅ BOTTOM cursor text actor ENHANCED properties:")
                print(f"   Visibility: {visibility}")
                print(f"   Text: {repr(input_text)}")
                print(f"   Font size: {font_size}")
                print(f"   Shadow: {shadow}")
                print(f"   Layer: {layer}")
                
                if visibility == 1:
                    print("✅ GOOD: BOTTOM cursor text is VISIBLE with enhancements")
                else:
                    print("❌ PROBLEM: BOTTOM cursor text is HIDDEN despite enhancements")
            else:
                print("❌ BOTTOM cursor text actor not found after loading")
            
            # Check renderer layering support
            print("\n🔧 Checking renderer layering support...")
            if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
                renderer = viewer.vtk_renderer_left.renderer
                if renderer and hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    num_layers = renderer.GetRenderWindow().GetNumberOfLayers()
                    print(f"✅ TOP renderer supports {num_layers} layers")
                    if num_layers >= 11:
                        print("✅ GOOD: Sufficient layers for cursor text on top")
                    else:
                        print("❌ PROBLEM: Not enough layers for proper layering")
                else:
                    print("❌ TOP renderer or render window not found")
            
            if hasattr(viewer, 'vtk_renderer_right') and viewer.vtk_renderer_right:
                renderer = viewer.vtk_renderer_right.renderer
                if renderer and hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    num_layers = renderer.GetRenderWindow().GetNumberOfLayers()
                    print(f"✅ BOTTOM renderer supports {num_layers} layers")
                else:
                    print("❌ BOTTOM renderer or render window not found")
            
            return True
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== CURSOR VISIBILITY FIX TEST ===")
    success = test_cursor_visibility_fix()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
