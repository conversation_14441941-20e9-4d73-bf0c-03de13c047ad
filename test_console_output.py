#!/usr/bin/env python3
"""
Small test program to verify console output is working
This will print messages to console that we can read
"""

import sys
import time

def main():
    print("🔥🔥🔥 CONSOLE OUTPUT TEST STARTING 🔥🔥🔥")
    print("=" * 50)
    
    # Test basic output
    print("✅ Basic print working")
    
    # Test error output
    print("❌ This is a test error message", file=sys.stderr)
    
    # Test with flush
    print("🔧 Testing flush...", end="", flush=True)
    time.sleep(1)
    print(" DONE!")
    
    # Test exception handling
    try:
        print("🧪 Testing exception handling...")
        raise Exception("This is a test exception for console output")
    except Exception as e:
        print(f"❌ Caught exception: {e}")
        import traceback
        traceback.print_exc()
    
    # Test multiple lines
    print("\n📋 MULTI-LINE TEST:")
    for i in range(5):
        print(f"   Line {i+1}: Console output test message")
        time.sleep(0.5)
    
    print("\n🎯 FINAL TEST:")
    print("   If you can see all these messages,")
    print("   then console output is working correctly!")
    
    print("\n✅ CONSOLE OUTPUT TEST COMPLETE")
    print("=" * 50)
    
    # Keep program running for a bit so we can read output
    print("⏳ Keeping program alive for 10 seconds...")
    for i in range(10, 0, -1):
        print(f"   Countdown: {i} seconds remaining...")
        time.sleep(1)
    
    print("🏁 Test program ending")

if __name__ == "__main__":
    main()
