#!/usr/bin/env python3
"""
Test if the main program can start without crashing
"""

import sys
import os
import subprocess
import time

def test_main_program_startup():
    """Test if the main program starts without immediate crash"""
    print("🔧 Testing main program startup...")
    
    try:
        # Try to import the main program modules first
        print("🔧 Testing imports...")
        
        try:
            from step_loader import STEPLoader
            print("✅ step_loader import OK")
        except Exception as e:
            print(f"❌ step_loader import failed: {e}")
            return False
            
        try:
            from vtk_renderer import VTKRenderer
            print("✅ vtk_renderer import OK")
        except Exception as e:
            print(f"❌ vtk_renderer import failed: {e}")
            return False
            
        try:
            from gui_components import create_tool_dock
            print("✅ gui_components import OK")
        except Exception as e:
            print(f"❌ gui_components import failed: {e}")
            return False
        
        # Try to import the main class
        try:
            sys.path.insert(0, '.')
            import step_viewer_tdk_modular_fixed
            print("✅ Main program module import OK")
        except Exception as e:
            print(f"❌ Main program module import failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Error testing startup: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_file_loading():
    """Test loading the SOIC file directly"""
    print("\n🔧 Testing STEP file loading with real data...")
    
    try:
        from step_loader import STEPLoader
        
        # Load the SOIC file
        loader = STEPLoader()
        success, message = loader.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Get the AXIS2_PLACEMENT_3D data
            axis_data = loader.get_original_axis2_placement()
            if axis_data:
                print("✅ AXIS2_PLACEMENT_3D data available:")
                print(f"   Point: {axis_data['point']}")
                print(f"   Dir1: {axis_data['dir1']}")
                print(f"   Dir2: {axis_data['dir2']}")
                
                # Test the exact formatting used in the GUI
                original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                
                cursor_text = "CURSOR: X=0.10 Y=0.05 Z=-0.00"
                cursor_display = f"{cursor_text}{original_axis_text}"
                
                print(f"\n🔧 Expected GUI display:\n{cursor_display}")
                return True
            else:
                print("❌ No AXIS2_PLACEMENT_3D data found")
                return False
        else:
            print(f"❌ STEP file loading failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing STEP file loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run startup tests"""
    print("🔥 MAIN PROGRAM STARTUP TEST 🔥")
    print("=" * 50)
    
    # Test 1: Module imports
    print("\n1. TESTING MODULE IMPORTS")
    print("-" * 30)
    result1 = test_main_program_startup()
    
    # Test 2: STEP file loading
    print("\n2. TESTING STEP FILE LOADING")
    print("-" * 30)
    result2 = test_step_file_loading()
    
    # Summary
    print("\n" + "=" * 50)
    print("STARTUP TEST RESULTS:")
    print("=" * 50)
    print(f"Module Imports: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"STEP Loading: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if result1 and result2:
        print("\n🎉 STARTUP TESTS PASSED!")
        print("The main program should be able to start and load STEP files.")
        print("The AXIS2_PLACEMENT_3D fix is working correctly.")
    else:
        print("\n⚠️  STARTUP ISSUES DETECTED")
        if not result1:
            print("- Module import problems need to be fixed")
        if not result2:
            print("- STEP file loading problems need to be fixed")
    
    return result1 and result2

if __name__ == "__main__":
    main()
