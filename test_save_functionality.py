#!/usr/bin/env python3
"""
Test Save Functionality - Option 1 Green Button

This script will:
1. Load a STEP file
2. Apply rotations/movements 
3. Save the file using the green "Save" button functionality
4. Load the saved file and verify it matches the original transformed model
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class SaveFunctionalityTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'test_saved_model.STEP'
        self.test_rotation = {'x': 45, 'y': 30, 'z': 0}
        
    def run_test(self):
        print("🔧 TESTING SAVE FUNCTIONALITY (Option 1 Green Button)")
        print("=" * 60)
        print("This will test that saved models match the displayed model")
        print("=" * 60)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_original)
        self.app.exec_()
        
    def step1_load_original(self):
        print("\n1. Loading original STEP file...")
        
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        QTimer.singleShot(1000, self.step2_apply_transformations)
        
    def step2_apply_transformations(self):
        print("\n2. Applying transformations to the model...")
        
        # Apply X rotation
        print(f"   Rotating X by {self.test_rotation['x']}°")
        self.viewer.rotate_shape('x', self.test_rotation['x'])
        
        QTimer.singleShot(500, self.step2b_apply_y_rotation)
        
    def step2b_apply_y_rotation(self):
        # Apply Y rotation
        print(f"   Rotating Y by {self.test_rotation['y']}°")
        self.viewer.rotate_shape('y', self.test_rotation['y'])
        
        QTimer.singleShot(500, self.step3_capture_display_state)
        
    def step3_capture_display_state(self):
        print("\n3. Capturing current display state...")
        
        # Get current position and rotation values from the display
        if hasattr(self.viewer, 'current_pos_left'):
            self.original_pos = self.viewer.current_pos_left.copy()
            print(f"   Current position: {self.original_pos}")
        
        if hasattr(self.viewer, 'model_rot_left'):
            self.original_rot = self.viewer.model_rot_left.copy()
            print(f"   Current rotation: {self.original_rot}")
            
        # Get the text overlay values (what's actually displayed)
        self.capture_display_text()
        
        QTimer.singleShot(1000, self.step4_save_file)
        
    def capture_display_text(self):
        """Capture the actual text displayed in the overlay"""
        try:
            # Try to get the displayed text values
            if hasattr(self.viewer, 'combined_text_actor_left'):
                actor = self.viewer.combined_text_actor_left
                if actor and hasattr(actor, 'GetInput'):
                    self.displayed_text = actor.GetInput()
                    print(f"   Displayed text: {self.displayed_text}")
        except Exception as e:
            print(f"   Could not capture display text: {e}")
            self.displayed_text = None
            
    def step4_save_file(self):
        print("\n4. Saving the transformed model...")
        
        # Remove existing test file if it exists
        if os.path.exists(self.saved_file):
            os.remove(self.saved_file)
            print(f"   Removed existing {self.saved_file}")
            
        # Use the viewer's ACTUAL save functionality (Option 1 - green button)
        try:
            print("   Using ACTUAL Option 1 save method from viewer...")

            # Get the current transformation values from the viewer
            if hasattr(self.viewer, 'current_pos_left'):
                current_pos = self.viewer.current_pos_left
            else:
                current_pos = {'x': 0, 'y': 0, 'z': 0}

            if hasattr(self.viewer, 'model_rot_left'):
                current_rot = self.viewer.model_rot_left
            else:
                current_rot = {'x': 0, 'y': 0, 'z': 0}

            # Get original values
            if hasattr(self.viewer, 'orig_pos_left'):
                orig_pos = self.viewer.orig_pos_left
            else:
                orig_pos = {'x': 0, 'y': 0, 'z': 0}

            if hasattr(self.viewer, 'orig_rot_left'):
                orig_rot = self.viewer.orig_rot_left
            else:
                orig_rot = {'x': 0, 'y': 0, 'z': 0}

            print(f"   Current position: {current_pos}")
            print(f"   Current rotation: {current_rot}")
            print(f"   Original position: {orig_pos}")
            print(f"   Original rotation: {orig_rot}")

            # Use the actual save method from the viewer
            loader = self.viewer.step_loader_left

            # Call the real _save_step_with_transformations method
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )

            if success:
                print(f"✅ Saved to {self.saved_file}")
                QTimer.singleShot(1000, self.step5_verify_saved_file)
            else:
                print(f"❌ Failed to save {self.saved_file}")
                self.app.quit()

        except Exception as e:
            print(f"❌ Error during save: {e}")
            import traceback
            traceback.print_exc()
            self.app.quit()
            
    def step5_verify_saved_file(self):
        print("\n5. Verifying saved file exists and is valid...")
        
        if not os.path.exists(self.saved_file):
            print(f"❌ Saved file {self.saved_file} does not exist!")
            self.app.quit()
            return
            
        file_size = os.path.getsize(self.saved_file)
        print(f"✅ Saved file exists, size: {file_size} bytes")
        
        if file_size < 1000:  # STEP files should be at least 1KB
            print("❌ Saved file seems too small to be valid")
            self.app.quit()
            return
            
        QTimer.singleShot(1000, self.step6_load_saved_file)
        
    def step6_load_saved_file(self):
        print("\n6. Loading the saved file to verify it matches...")
        
        # Clear the current model first
        self.viewer.reset_to_original()
        
        QTimer.singleShot(1000, self.step6b_load_saved)
        
    def step6b_load_saved(self):
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        
        if not success:
            print(f"❌ Failed to load saved file {self.saved_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded saved file {self.saved_file}")
        QTimer.singleShot(1000, self.step7_compare_results)
        
    def step7_compare_results(self):
        print("\n7. Comparing original transformed model vs saved/loaded model...")
        
        # Get the current state after loading saved file
        if hasattr(self.viewer, 'current_pos_left'):
            saved_pos = self.viewer.current_pos_left.copy()
            print(f"   Saved file position: {saved_pos}")
        
        if hasattr(self.viewer, 'model_rot_left'):
            saved_rot = self.viewer.model_rot_left.copy()
            print(f"   Saved file rotation: {saved_rot}")
            
        # Compare positions
        pos_match = self.compare_positions(self.original_pos, saved_pos)
        rot_match = self.compare_rotations(self.original_rot, saved_rot)
        
        print("\n📊 COMPARISON RESULTS:")
        print(f"   Position match: {'✅ YES' if pos_match else '❌ NO'}")
        print(f"   Rotation match: {'✅ YES' if rot_match else '❌ NO'}")
        
        if pos_match and rot_match:
            print("\n🎉 SUCCESS: Saved model matches original transformed model!")
            print("✅ Save functionality is working correctly")
        else:
            print("\n❌ FAILURE: Saved model does not match original")
            print("❌ Save functionality has issues")
            
        QTimer.singleShot(3000, self.cleanup_and_exit)
        
    def compare_positions(self, pos1, pos2, tolerance=0.01):
        """Compare two position dictionaries with tolerance"""
        try:
            for axis in ['x', 'y', 'z']:
                diff = abs(pos1[axis] - pos2[axis])
                if diff > tolerance:
                    print(f"   Position {axis}: {pos1[axis]:.3f} vs {pos2[axis]:.3f} (diff: {diff:.3f})")
                    return False
            return True
        except Exception as e:
            print(f"   Error comparing positions: {e}")
            return False
            
    def compare_rotations(self, rot1, rot2, tolerance=1.0):
        """Compare two rotation dictionaries with tolerance (degrees)"""
        try:
            for axis in ['x', 'y', 'z']:
                diff = abs(rot1[axis] - rot2[axis])
                if diff > tolerance:
                    print(f"   Rotation {axis}: {rot1[axis]:.1f}° vs {rot2[axis]:.1f}° (diff: {diff:.1f}°)")
                    return False
            return True
        except Exception as e:
            print(f"   Error comparing rotations: {e}")
            return False
            
    def cleanup_and_exit(self):
        print("\n8. Cleaning up...")
        
        # Remove test file
        if os.path.exists(self.saved_file):
            os.remove(self.saved_file)
            print(f"   Removed test file {self.saved_file}")
            
        print("\n" + "=" * 60)
        print("SAVE FUNCTIONALITY TEST COMPLETE")
        print("=" * 60)
        
        self.app.quit()

if __name__ == '__main__':
    tester = SaveFunctionalityTester()
    tester.run_test()
