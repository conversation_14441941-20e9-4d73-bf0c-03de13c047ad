#!/usr/bin/env python3
"""
Quick Unicode fix for step_viewer_tdk_modular.py
Replace problematic Unicode characters with ASCII equivalents
"""

import re

def fix_unicode_in_file(filename):
    """Fix Unicode characters in a Python file"""
    print(f"Fixing Unicode characters in {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace Unicode characters with ASCII equivalents
    replacements = {
        '🔥': '***',
        '🔴': 'TOP',
        '🔵': 'BOTTOM', 
        '🖱️': 'MOUSE',
        '°': 'deg',
        '📅': 'DATE',
        '═': '=',
        '📺': 'VIEWER',
        '🎮': 'CONTROLS',
        '📍': 'POSITION',
        '👁️': 'VIEW',
        '🔄': 'OVERLAY',
        'TARGET': 'TARGET',
        'INFO': 'INFO'
    }
    
    # Apply replacements
    for unicode_char, ascii_replacement in replacements.items():
        content = content.replace(unicode_char, ascii_replacement)
    
    # Also replace any remaining non-ASCII characters with safe equivalents
    # Keep only printable ASCII characters, newlines, and tabs
    content = re.sub(r'[^\x20-\x7E\n\t\r]', '?', content)
    
    # Write the fixed content back
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Unicode characters fixed in {filename}")

if __name__ == "__main__":
    fix_unicode_in_file("step_viewer_tdk_modular.py")
    print("Unicode fix complete!")
