#!/usr/bin/env python3

print("CHECKING ACTUAL DISPLAYED COLORS - FIXED VERSION")
print("=" * 50)

import sys
from PyQt5.QtWidgets import QApplication

# Create QApplication first
app = QApplication(sys.argv)

from step_loader import <PERSON><PERSON><PERSON>oader
from vtk_renderer import VTK<PERSON>enderer

# Load the STEP file
print("Step 1: Loading STEP file...")
loader = STEPLoader()
result = loader.load_step_file('SOIC16P127_1270X940X610L89X51.STEP')

if len(result) == 3:
    polydata, success, message = result
else:
    success, message = result
    polydata = loader.current_polydata

print(f"Load result: {success}")
print(f"Message: {message}")

if success and polydata:
    print("\nStep 2: Checking polydata colors...")
    
    # Check if colors exist in polydata
    colors_array = polydata.GetCellData().GetScalars("Colors")
    if colors_array:
        print(f"Colors found: {colors_array.GetNumberOfTuples()} cells have colors")
        
        # Check first 10 colors
        print("\nFirst 10 cell colors:")
        for i in range(min(10, colors_array.GetNumberOfTuples())):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            print(f"Cell {i}: RGB({r}, {g}, {b})")
        
        # Count unique colors
        unique_colors = {}
        for i in range(colors_array.GetNumberOfTuples()):
            r = int(colors_array.GetComponent(i, 0))
            g = int(colors_array.GetComponent(i, 1))
            b = int(colors_array.GetComponent(i, 2))
            color = (r, g, b)
            unique_colors[color] = unique_colors.get(color, 0) + 1
        
        print(f"\nUnique colors found:")
        for color, count in unique_colors.items():
            print(f"RGB{color}: {count} cells")
            
    else:
        print("No colors found in polydata")
        
    print("\nStep 3: Creating VTK renderer to check actor colors...")
    
    # Create renderer and display polydata
    renderer = VTKRenderer()
    if renderer.display_polydata(polydata):
        print("Polydata displayed successfully")
        
        # Check actor colors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"\nStep 4: Checking {len(renderer.step_actors)} actor colors:")
            for i, actor in enumerate(renderer.step_actors):
                prop = actor.GetProperty()
                color = prop.GetColor()
                print(f"Actor {i}: RGB({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})")
                print(f"         RGB({int(color[0]*255)}, {int(color[1]*255)}, {int(color[2]*255)})")
        
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            print("\nStep 4: Checking single actor color:")
            prop = renderer.step_actor.GetProperty()
            color = prop.GetColor()
            print(f"Actor: RGB({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})")
            print(f"       RGB({int(color[0]*255)}, {int(color[1]*255)}, {int(color[2]*255)})")
        
        else:
            print("No actors found")
    else:
        print("Failed to display polydata")
        
else:
    print("Failed to load STEP file")

print("=" * 50)
print("COLOR CHECK COMPLETE")

# Clean up
app.quit()
