#!/usr/bin/env python3
"""
VERIFY THE NUMBERS: Test to actually measure coordinates and rotations
before and after button clicks to verify the fix is working correctly.
"""

import sys
import os
import math
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🔍 VERIFYING THE NUMBERS: MEASURING ACTUAL COORDINATES AND ROTATIONS")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    # Test data storage
    test_data = {
        'measurements': [],
        'click_count': 0,
        'max_clicks': 3  # Test 3 clicks (45 degrees total)
    }
    
    def measure_current_state(label):
        """Measure and record current state of model and origin markers"""
        print(f"\n📏 MEASURING STATE: {label}")
        print("-" * 50)
        
        measurement = {
            'label': label,
            'model_position': None,
            'model_orientation': None,
            'green_sphere_position': None,
            'yellow_text_origin': None
        }
        
        renderer = viewer.vtk_renderer_left
        
        # 1. Measure model position and orientation
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            first_actor = renderer.step_actors[0]
            model_pos = first_actor.GetPosition()
            model_orient = first_actor.GetOrientation()
            measurement['model_position'] = model_pos
            measurement['model_orientation'] = model_orient
            print(f"   Model Position: {model_pos}")
            print(f"   Model Orientation: {model_orient}")
        else:
            print("   ❌ No model actors found")
            
        # 2. Measure green sphere position
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            sphere_pos = renderer.part_origin_sphere.GetPosition()
            measurement['green_sphere_position'] = sphere_pos
            print(f"   Green Sphere Position: {sphere_pos}")
        else:
            print("   ❌ No green sphere found")
            
        # 3. Measure yellow text origin values
        if hasattr(viewer, 'current_pos_left'):
            origin_values = viewer.current_pos_left
            measurement['yellow_text_origin'] = origin_values
            print(f"   Yellow Text Origin: {origin_values}")
        else:
            print("   ❌ No yellow text origin values found")
            
        test_data['measurements'].append(measurement)
        return measurement
    
    def record_initial_state():
        print("\n3. RECORDING INITIAL STATE")
        print("=" * 50)
        measure_current_state("INITIAL")
        
        # Start clicking buttons after delay
        QTimer.singleShot(2000, click_and_measure)
    
    def click_and_measure():
        test_data['click_count'] += 1
        click_num = test_data['click_count']
        
        print(f"\n4.{click_num} CLICKING X+ BUTTON (Click #{click_num})")
        print("=" * 50)
        
        # Find and click X+ button
        buttons = viewer.findChildren(QPushButton)
        x_plus_button = None
        for button in buttons:
            if button.text() == "X+":
                x_plus_button = button
                break
        
        if x_plus_button:
            print(f"   Clicking X+ button...")
            x_plus_button.click()
            # Wait for rotation to complete, then measure
            QTimer.singleShot(1500, lambda: measure_after_click(click_num))
        else:
            print("   ❌ Could not find X+ button")
    
    def measure_after_click(click_num):
        print(f"\n5.{click_num} MEASURING AFTER CLICK #{click_num}")
        print("=" * 50)
        
        measurement = measure_current_state(f"AFTER_CLICK_{click_num}")
        
        # Compare with previous measurement
        if len(test_data['measurements']) >= 2:
            prev_measurement = test_data['measurements'][-2]
            current_measurement = test_data['measurements'][-1]
            
            print(f"\n📊 COMPARISON: {prev_measurement['label']} vs {current_measurement['label']}")
            print("-" * 60)
            
            # Compare model orientation
            if prev_measurement['model_orientation'] and current_measurement['model_orientation']:
                prev_orient = prev_measurement['model_orientation']
                curr_orient = current_measurement['model_orientation']
                
                dx = curr_orient[0] - prev_orient[0]
                dy = curr_orient[1] - prev_orient[1] 
                dz = curr_orient[2] - prev_orient[2]
                
                print(f"   Model Orientation Change: ΔX={dx:.3f}°, ΔY={dy:.3f}°, ΔZ={dz:.3f}°")
                
                # Expected: 15° X rotation per click
                expected_x_change = 15.0
                if abs(dx - expected_x_change) < 1.0:
                    print(f"   ✅ Model X rotation correct: {dx:.1f}° (expected {expected_x_change}°)")
                else:
                    print(f"   ❌ Model X rotation incorrect: {dx:.1f}° (expected {expected_x_change}°)")
            
            # Compare green sphere position
            if prev_measurement['green_sphere_position'] and current_measurement['green_sphere_position']:
                prev_sphere = prev_measurement['green_sphere_position']
                curr_sphere = current_measurement['green_sphere_position']
                
                dx = curr_sphere[0] - prev_sphere[0]
                dy = curr_sphere[1] - prev_sphere[1]
                dz = curr_sphere[2] - prev_sphere[2]
                distance = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                print(f"   Green Sphere Position Change: ΔX={dx:.6f}, ΔY={dy:.6f}, ΔZ={dz:.6f}")
                print(f"   Green Sphere Distance Moved: {distance:.6f}")
                
                if distance > 0.01:  # More than 0.01 units
                    print(f"   ✅ Green sphere moved significantly: {distance:.6f} units")
                else:
                    print(f"   ❌ Green sphere did not move enough: {distance:.6f} units")
            
            # Compare yellow text origin values
            if prev_measurement['yellow_text_origin'] and current_measurement['yellow_text_origin']:
                prev_text = prev_measurement['yellow_text_origin']
                curr_text = current_measurement['yellow_text_origin']
                
                dx = curr_text['x'] - prev_text['x']
                dy = curr_text['y'] - prev_text['y']
                dz = curr_text['z'] - prev_text['z']
                distance = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                print(f"   Yellow Text Origin Change: ΔX={dx:.6f}, ΔY={dy:.6f}, ΔZ={dz:.6f}")
                print(f"   Yellow Text Distance Change: {distance:.6f}")
                
                if distance > 0.01:  # More than 0.01 units
                    print(f"   ✅ Yellow text values updated: {distance:.6f} units")
                else:
                    print(f"   ❌ Yellow text values did not change enough: {distance:.6f} units")
                    
            # Check if green sphere position matches yellow text values
            if current_measurement['green_sphere_position'] and current_measurement['yellow_text_origin']:
                sphere_pos = current_measurement['green_sphere_position']
                text_values = current_measurement['yellow_text_origin']
                
                dx = abs(sphere_pos[0] - text_values['x'])
                dy = abs(sphere_pos[1] - text_values['y'])
                dz = abs(sphere_pos[2] - text_values['z'])
                max_diff = max(dx, dy, dz)
                
                print(f"   Green Sphere vs Yellow Text Difference: ΔX={dx:.6f}, ΔY={dy:.6f}, ΔZ={dz:.6f}")
                
                if max_diff < 0.001:  # Within 0.001 units
                    print(f"   ✅ Green sphere and yellow text match perfectly: max diff {max_diff:.6f}")
                else:
                    print(f"   ⚠️ Green sphere and yellow text don't match: max diff {max_diff:.6f}")
        
        # Continue clicking if we haven't reached max clicks
        if test_data['click_count'] < test_data['max_clicks']:
            QTimer.singleShot(2000, click_and_measure)
        else:
            # Finished all clicks, analyze final results
            QTimer.singleShot(2000, analyze_final_results)
    
    def analyze_final_results():
        print(f"\n6. FINAL ANALYSIS AFTER {test_data['max_clicks']} CLICKS")
        print("=" * 80)
        
        if len(test_data['measurements']) < 2:
            print("❌ FAILED: Not enough measurements collected")
            return
        
        initial = test_data['measurements'][0]
        final = test_data['measurements'][-1]
        
        print(f"INITIAL STATE:")
        print(f"   Model Orientation: {initial['model_orientation']}")
        print(f"   Green Sphere Position: {initial['green_sphere_position']}")
        print(f"   Yellow Text Origin: {initial['yellow_text_origin']}")
        
        print(f"\nFINAL STATE:")
        print(f"   Model Orientation: {final['model_orientation']}")
        print(f"   Green Sphere Position: {final['green_sphere_position']}")
        print(f"   Yellow Text Origin: {final['yellow_text_origin']}")
        
        # Calculate total changes
        if initial['model_orientation'] and final['model_orientation']:
            total_x_rotation = final['model_orientation'][0] - initial['model_orientation'][0]
            expected_total_rotation = 15.0 * test_data['max_clicks']
            
            print(f"\nTOTAL ROTATION ANALYSIS:")
            print(f"   Expected total X rotation: {expected_total_rotation}°")
            print(f"   Actual total X rotation: {total_x_rotation:.1f}°")
            
            rotation_correct = abs(total_x_rotation - expected_total_rotation) < 2.0
            if rotation_correct:
                print(f"   ✅ Total rotation is correct!")
            else:
                print(f"   ❌ Total rotation is incorrect!")
        
        if initial['green_sphere_position'] and final['green_sphere_position']:
            dx = final['green_sphere_position'][0] - initial['green_sphere_position'][0]
            dy = final['green_sphere_position'][1] - initial['green_sphere_position'][1]
            dz = final['green_sphere_position'][2] - initial['green_sphere_position'][2]
            total_sphere_movement = math.sqrt(dx*dx + dy*dy + dz*dz)
            
            print(f"\nTOTAL SPHERE MOVEMENT ANALYSIS:")
            print(f"   Total sphere movement: {total_sphere_movement:.6f} units")
            print(f"   Movement vector: ({dx:.6f}, {dy:.6f}, {dz:.6f})")
            
            sphere_moved = total_sphere_movement > 0.1
            if sphere_moved:
                print(f"   ✅ Green sphere moved significantly!")
            else:
                print(f"   ❌ Green sphere did not move enough!")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if 'rotation_correct' in locals() and 'sphere_moved' in locals():
            if rotation_correct and sphere_moved:
                print(f"🎉 SUCCESS! The fix is working correctly!")
                print(f"   ✅ Model rotates properly")
                print(f"   ✅ Origin markers follow the model")
                print(f"   ✅ Numbers are verified and correct!")
            else:
                print(f"❌ FAILED! The fix is not working correctly!")
                if not rotation_correct:
                    print(f"   ❌ Model rotation is incorrect")
                if not sphere_moved:
                    print(f"   ❌ Origin markers are not following the model")
        else:
            print(f"⚠️ INCOMPLETE: Could not verify all measurements")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(4000, record_initial_state)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will measure ACTUAL coordinates and rotations")
    print("   - Before and after each button click")
    print("   - Verify that the numbers are mathematically correct")
    print("   - Final analysis will confirm if the fix works")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
