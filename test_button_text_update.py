#!/usr/bin/env python3
"""
AUTOMATED test to verify button rotation and text update behavior
- Loads actual STEP file
- Simulates button clicks
- Captures debug output
- NO USER INTERACTION REQUIRED
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class ButtonTextUpdateTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        
    def start_test(self):
        """Start the test"""
        print("🧪 TESTING BUTTON ROTATION AND TEXT UPDATE")
        print("=" * 50)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize, then start test
        QTimer.singleShot(2000, self.begin_test)
        
        return self.app.exec_()
        
    def begin_test(self):
        """Begin the test sequence"""
        print("🔧 GUI initialized, starting test sequence...")
        self.timer.start(3000)  # Run test steps every 3 seconds
        
    def run_test_step(self):
        """Run each test step"""
        if self.test_step == 0:
            print("\n=== STEP 1: Load test STEP file ===")
            # Try to load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"📁 Loading {test_file}...")
                    # Use the correct method - open_step_file_dialog or similar
                    try:
                        # Try to call the file loading method directly
                        if hasattr(self.viewer, 'open_step_file'):
                            success = self.viewer.open_step_file(test_file)
                        else:
                            print("❌ No open_step_file method found")
                            success = False

                        if success:
                            print(f"✅ File loaded successfully")
                            loaded = True
                            break
                        else:
                            print(f"❌ Failed to load {test_file}")
                    except Exception as e:
                        print(f"❌ Error loading {test_file}: {e}")
            
            if not loaded:
                print("❌ No test files found, creating a simple test")
                # Continue anyway to test the button behavior
            
        elif self.test_step == 1:
            print("\n=== STEP 2: Check initial rotation values ===")
            self.check_rotation_values("BEFORE rotation")
            
        elif self.test_step == 2:
            print("\n=== STEP 3: Click X+15° button ===")
            print("🔘 Simulating X+15° button click...")
            # Set active viewer to top
            self.viewer.active_viewer = "top"
            # Call rotate_shape method directly
            self.viewer.rotate_shape('x', 15.0)
            print("✅ X+15° rotation applied")
            
        elif self.test_step == 3:
            print("\n=== STEP 4: Check rotation values after X+15° ===")
            self.check_rotation_values("AFTER X+15° rotation")
            
        elif self.test_step == 4:
            print("\n=== STEP 5: Click Y+15° button ===")
            print("🔘 Simulating Y+15° button click...")
            self.viewer.rotate_shape('y', 15.0)
            print("✅ Y+15° rotation applied")
            
        elif self.test_step == 5:
            print("\n=== STEP 6: Check rotation values after Y+15° ===")
            self.check_rotation_values("AFTER Y+15° rotation")
            
        elif self.test_step == 6:
            print("\n=== STEP 7: Check text overlay content ===")
            self.check_text_overlay_content()
            
        elif self.test_step == 7:
            print("\n=== TEST COMPLETE ===")
            print("🏁 Button rotation and text update test finished")
            self.timer.stop()
            # Keep the GUI open for manual inspection
            return
            
        self.test_step += 1
        
    def check_rotation_values(self, stage):
        """Check the current rotation values"""
        print(f"🔍 CHECKING ROTATION VALUES - {stage}")
        
        # Check current_rot_left values
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"   current_rot_left: {self.viewer.current_rot_left}")
        else:
            print("   ❌ current_rot_left not found")
            
        # Check model_rot_left values
        if hasattr(self.viewer, 'model_rot_left'):
            print(f"   model_rot_left: {self.viewer.model_rot_left}")
        else:
            print("   ❌ model_rot_left not found")
            
    def check_text_overlay_content(self):
        """Check what's actually displayed in the text overlay"""
        print("🔍 CHECKING TEXT OVERLAY CONTENT")
        
        # Check if text actors exist
        if hasattr(self.viewer, 'combined_text_actor_left'):
            print("   ✅ combined_text_actor_left exists")
            # Try to get the text content
            try:
                text_input = self.viewer.combined_text_actor_left.GetInput()
                print(f"   📝 Text content: {repr(text_input)}")
            except Exception as e:
                print(f"   ❌ Error getting text content: {e}")
        else:
            print("   ❌ combined_text_actor_left not found")
            
        # Force a text overlay update
        print("   🔄 Forcing text overlay update...")
        try:
            self.viewer.update_text_overlays()
            print("   ✅ Text overlay update called")
        except Exception as e:
            print(f"   ❌ Error updating text overlay: {e}")

if __name__ == "__main__":
    test = ButtonTextUpdateTest()
    sys.exit(test.start_test())
