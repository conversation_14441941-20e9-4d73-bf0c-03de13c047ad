#!/usr/bin/env python3
"""
Debug what the actual GUI is doing when loading SOIC file
This will simulate the exact GUI workflow to see where the wrong values come from
"""

import sys
import os

def test_actual_gui_workflow():
    """Test the exact workflow that happens in the GUI"""
    print("🔧 Testing actual GUI workflow...")
    
    try:
        # Import the actual GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load the SOIC file (this is what happens when user clicks Load)
        print("\n🔧 Loading SOIC file...")
        success = viewer.load_step_file_left("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded in GUI")
            
            # Check if step_loader_left exists and has the method
            if hasattr(viewer, 'step_loader_left'):
                print("✅ step_loader_left exists")
                
                if hasattr(viewer.step_loader_left, 'get_original_axis2_placement'):
                    print("✅ get_original_axis2_placement method exists")
                    
                    # Get the data
                    axis_data = viewer.step_loader_left.get_original_axis2_placement()
                    if axis_data:
                        print("✅ AXIS2_PLACEMENT_3D data retrieved:")
                        print(f"   Point: {axis_data['point']}")
                        print(f"   Dir1: {axis_data['dir1']}")
                        print(f"   Dir2: {axis_data['dir2']}")
                        
                        # Now test the update_text_overlays method directly
                        print("\n🔧 Testing update_text_overlays method...")
                        
                        # Set up some mock cursor position
                        viewer.cursor_pos_left = {'x': 0.10, 'y': 0.05, 'z': -0.00}
                        
                        # Call the method
                        viewer.update_text_overlays()
                        
                        # Check what text was actually set
                        if hasattr(viewer, 'cursor_text_actor_left'):
                            actual_text = viewer.cursor_text_actor_left.GetInput()
                            print(f"\n🔧 ACTUAL TEXT SET IN GUI:")
                            print(f"'{actual_text}'")
                            
                            # Check if it contains the correct values
                            if "(-4.19, -3.6673, 0.4914)" in actual_text:
                                print("✅ Text contains correct STEP file values!")
                            elif "(-4.190000000000000, -3.667300000000000, 0.491400000000000)" in actual_text:
                                print("❌ Text contains OLD HARDCODED values!")
                            else:
                                print("⚠️  Text contains different values")
                                
                            return True
                        else:
                            print("❌ cursor_text_actor_left not found")
                            return False
                    else:
                        print("❌ No AXIS2_PLACEMENT_3D data returned")
                        return False
                else:
                    print("❌ get_original_axis2_placement method not found")
                    return False
            else:
                print("❌ step_loader_left not found")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_if_wrong_file_is_running():
    """Check if we're running the wrong version of the file"""
    print("\n🔧 Checking which file is actually being imported...")
    
    try:
        import step_viewer_tdk_modular_fixed
        import inspect
        
        # Get the file path of the imported module
        file_path = inspect.getfile(step_viewer_tdk_modular_fixed)
        print(f"✅ Imported from: {file_path}")
        
        # Check the update_text_overlays method source
        if hasattr(step_viewer_tdk_modular_fixed.StepViewerTDK, 'update_text_overlays'):
            method = step_viewer_tdk_modular_fixed.StepViewerTDK.update_text_overlays
            source = inspect.getsource(method)
            
            # Check for our fixes
            if 'get_original_axis2_placement' in source:
                print("✅ Method contains get_original_axis2_placement call")
            else:
                print("❌ Method does NOT contain get_original_axis2_placement call")
                
            if 'USING REAL STEP FILE' in source:
                print("✅ Method contains USING REAL STEP FILE debug message")
            else:
                print("❌ Method does NOT contain USING REAL STEP FILE debug message")
                
            if 'display_angle = 9.0' in source:
                print("❌ Method still contains hardcoded display_angle = 9.0")
                return False
            else:
                print("✅ Method does not contain hardcoded display_angle = 9.0")
                
            if 'USER REQUESTED: Angle should be 9°' in source:
                print("❌ Method still contains hardcoded angle comment")
                return False
            else:
                print("✅ Method does not contain hardcoded angle comment")
                
            return True
        else:
            print("❌ update_text_overlays method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking imported file: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debugging tests"""
    print("🔥 DEBUG ACTUAL GUI BEHAVIOR 🔥")
    print("=" * 50)
    
    # Test 1: Check which file is being imported
    print("1. CHECKING IMPORTED FILE")
    print("-" * 30)
    result1 = check_if_wrong_file_is_running()
    
    # Test 2: Test actual GUI workflow
    print("\n2. TESTING ACTUAL GUI WORKFLOW")
    print("-" * 30)
    result2 = test_actual_gui_workflow()
    
    # Summary
    print("\n" + "=" * 50)
    print("DEBUG RESULTS:")
    print("=" * 50)
    print(f"Correct File Imported: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"GUI Workflow: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if not result1:
        print("\n⚠️  WRONG FILE IS BEING IMPORTED!")
        print("The Python import system is loading an old version of the file.")
        print("This explains why the fixes aren't working.")
    elif not result2:
        print("\n⚠️  GUI WORKFLOW HAS ISSUES")
        print("The file is correct but something in the GUI logic is wrong.")
    else:
        print("\n🤔 BOTH TESTS PASSED - The issue might be elsewhere")

if __name__ == "__main__":
    main()
