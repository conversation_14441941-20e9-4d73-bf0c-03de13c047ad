#!/usr/bin/env python3
"""
GUI Button Diagnosis Test Program
Tests why GUI buttons aren't showing accumulated values in text display
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class GUIButtonTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = []
        
    def setup_viewer(self):
        """Initialize the viewer"""
        print("🔥🔥🔥 SETTING UP GUI BUTTON DIAGNOSIS TEST 🔥🔥🔥")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize
        self.app.processEvents()
        time.sleep(2)
        
        print("✅ Viewer initialized and displayed")
        
    def test_direct_function_call(self):
        """Test calling rotate_shape directly"""
        print("\n" + "="*80)
        print("🧪 TEST 1: DIRECT FUNCTION CALL")
        print("="*80)
        
        # Call rotate_shape directly
        print("🔧 Calling viewer.rotate_shape('x', 15) directly...")
        self.viewer.rotate_shape('x', 15)
        
        # Check the stored values
        if hasattr(self.viewer, 'direction_values_left'):
            print(f"✅ direction_values_left exists: {self.viewer.direction_values_left}")
            self.test_results.append(f"DIRECT CALL: direction_values_left = {self.viewer.direction_values_left}")
        else:
            print("❌ direction_values_left does NOT exist")
            self.test_results.append("DIRECT CALL: direction_values_left = MISSING")
            
        # Check what get_actual_vtk_transformation_values returns
        actual_values = self.viewer.get_actual_vtk_transformation_values("top")
        print(f"✅ get_actual_vtk_transformation_values returned: {actual_values}")
        self.test_results.append(f"DIRECT CALL: get_actual_vtk_transformation_values = {actual_values}")
        
    def simulate_gui_button_click(self):
        """Simulate clicking the GUI button"""
        print("\n" + "="*80)
        print("🧪 TEST 2: SIMULATE GUI BUTTON CLICK")
        print("="*80)
        
        # Find the X+ button and simulate click
        try:
            # Look for the X+ button in the GUI
            x_plus_buttons = self.viewer.findChildren(type(self.viewer).__bases__[0])
            print(f"🔧 Found {len(x_plus_buttons)} potential button widgets")
            
            # Try to find and click the X+ rotation button
            # The button should be connected to: lambda: parent.rotate_shape('x', parent.rotation_increment.value())
            rotation_increment = self.viewer.rotation_increment.value()
            print(f"🔧 Current rotation increment: {rotation_increment}")
            
            # Simulate the button click by calling the same lambda function
            print("🔧 Simulating X+ button click...")
            self.viewer.rotate_shape('x', rotation_increment)
            
            # Check the stored values after GUI button simulation
            if hasattr(self.viewer, 'direction_values_left'):
                print(f"✅ After GUI simulation - direction_values_left: {self.viewer.direction_values_left}")
                self.test_results.append(f"GUI SIMULATION: direction_values_left = {self.viewer.direction_values_left}")
            else:
                print("❌ After GUI simulation - direction_values_left does NOT exist")
                self.test_results.append("GUI SIMULATION: direction_values_left = MISSING")
                
            # Check what get_actual_vtk_transformation_values returns
            actual_values = self.viewer.get_actual_vtk_transformation_values("top")
            print(f"✅ After GUI simulation - get_actual_vtk_transformation_values: {actual_values}")
            self.test_results.append(f"GUI SIMULATION: get_actual_vtk_transformation_values = {actual_values}")
            
        except Exception as e:
            print(f"❌ Error simulating GUI button click: {e}")
            self.test_results.append(f"GUI SIMULATION: ERROR = {e}")
    
    def test_text_display_update(self):
        """Test the text display update mechanism"""
        print("\n" + "="*80)
        print("🧪 TEST 3: TEXT DISPLAY UPDATE MECHANISM")
        print("="*80)
        
        # Force a text display update
        print("🔧 Forcing text display update...")
        
        # Check if the text display functions are being called
        try:
            # Simulate cursor movement to trigger text update
            if hasattr(self.viewer, 'cursor_pos_left'):
                self.viewer.cursor_pos_left = {'x': 1.0, 'y': 2.0, 'z': 3.0}
                print("✅ Set cursor_pos_left to trigger text update")
            
            # Force text overlay update
            self.app.processEvents()
            time.sleep(1)
            
            print("✅ Text display update mechanism tested")
            self.test_results.append("TEXT DISPLAY: Update mechanism triggered")
            
        except Exception as e:
            print(f"❌ Error testing text display: {e}")
            self.test_results.append(f"TEXT DISPLAY: ERROR = {e}")
    
    def run_comprehensive_test(self):
        """Run all tests"""
        print("🚀 STARTING COMPREHENSIVE GUI BUTTON DIAGNOSIS")
        print("="*80)
        
        # Setup
        self.setup_viewer()
        
        # Run tests
        self.test_direct_function_call()
        self.simulate_gui_button_click()
        self.test_text_display_update()
        
        # Print results
        print("\n" + "="*80)
        print("📊 TEST RESULTS SUMMARY")
        print("="*80)
        for i, result in enumerate(self.test_results, 1):
            print(f"{i}. {result}")
        
        print("\n🔥🔥🔥 DIAGNOSIS COMPLETE 🔥🔥🔥")
        print("Now click the Direction X+ button in the GUI and watch the terminal output!")
        
        # Keep the application running for manual testing
        return self.app.exec_()

def main():
    """Main test function"""
    tester = GUIButtonTester()
    return tester.run_comprehensive_test()

if __name__ == "__main__":
    sys.exit(main())
