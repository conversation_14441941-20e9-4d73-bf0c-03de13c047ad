#!/usr/bin/env python3
"""
Final test to verify the origin markers now follow the model correctly.
This test will:
1. Load a model
2. Record initial positions
3. Click rotation buttons multiple times
4. Verify origin markers move to follow the model
"""

import sys
import os
import math
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🎯 FINAL TEST: VERIFYING ORIGIN MARKERS FOLLOW MODEL")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    # Store test data
    test_data = {
        'initial_pos': None,
        'positions_after_clicks': [],
        'click_count': 0,
        'max_clicks': 6  # Test 6 clicks (90 degrees total)
    }
    
    def record_initial_position():
        print("\n3. RECORDING INITIAL POSITION")
        print("-" * 40)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            initial_pos = renderer.part_origin_sphere.GetPosition()
            test_data['initial_pos'] = initial_pos
            print(f"   Initial green sphere position: {initial_pos}")
            
            # Start clicking buttons
            QTimer.singleShot(1000, click_button_and_measure)
        else:
            print("   ❌ No green sphere found")
    
    def click_button_and_measure():
        test_data['click_count'] += 1
        click_num = test_data['click_count']
        
        print(f"\n4.{click_num} CLICKING X+ BUTTON (Click #{click_num})")
        print("-" * 40)
        
        # Find and click X+ button
        buttons = viewer.findChildren(QPushButton)
        x_plus_button = None
        for button in buttons:
            if button.text() == "X+":
                x_plus_button = button
                break
        
        if x_plus_button:
            x_plus_button.click()
            # Wait for position to update, then measure
            QTimer.singleShot(1000, lambda: measure_position_after_click(click_num))
        else:
            print("   ❌ Could not find X+ button")
    
    def measure_position_after_click(click_num):
        print(f"\n5.{click_num} MEASURING POSITION AFTER CLICK #{click_num}")
        print("-" * 40)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            current_pos = renderer.part_origin_sphere.GetPosition()
            test_data['positions_after_clicks'].append(current_pos)
            
            print(f"   Position after click #{click_num}: {current_pos}")
            
            # Calculate distance moved from initial position
            if test_data['initial_pos']:
                dx = current_pos[0] - test_data['initial_pos'][0]
                dy = current_pos[1] - test_data['initial_pos'][1]
                dz = current_pos[2] - test_data['initial_pos'][2]
                distance = math.sqrt(dx*dx + dy*dy + dz*dz)
                print(f"   Distance from initial: {distance:.6f}")
                
                # Check if position changed significantly
                if distance > 0.01:  # More than 0.01 units
                    print(f"   ✅ GOOD: Origin marker moved significantly!")
                else:
                    print(f"   ⚠️ WARNING: Origin marker moved very little")
            
            # Continue clicking if we haven't reached max clicks
            if test_data['click_count'] < test_data['max_clicks']:
                QTimer.singleShot(1000, click_button_and_measure)
            else:
                # Finished all clicks, analyze results
                QTimer.singleShot(1000, analyze_final_results)
        else:
            print("   ❌ No green sphere found after click")
    
    def analyze_final_results():
        print(f"\n6. FINAL ANALYSIS AFTER {test_data['max_clicks']} CLICKS")
        print("=" * 60)
        
        if not test_data['initial_pos'] or not test_data['positions_after_clicks']:
            print("❌ FAILED: Could not collect position data")
            return
        
        initial = test_data['initial_pos']
        final = test_data['positions_after_clicks'][-1]
        
        print(f"   Initial position: {initial}")
        print(f"   Final position:   {final}")
        
        # Calculate total movement
        dx = final[0] - initial[0]
        dy = final[1] - initial[1] 
        dz = final[2] - initial[2]
        total_distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        print(f"   Total movement: ({dx:.6f}, {dy:.6f}, {dz:.6f})")
        print(f"   Total distance: {total_distance:.6f}")
        
        # Check if movement is reasonable for 90 degrees of rotation
        expected_min_movement = 0.1  # Expect at least 0.1 units of movement
        
        if total_distance > expected_min_movement:
            print(f"\n🎉 SUCCESS! Origin markers are following the model!")
            print(f"   ✅ Total movement of {total_distance:.6f} units is significant")
            print(f"   ✅ This indicates the fix is working correctly")
        else:
            print(f"\n❌ FAILED! Origin markers are NOT following the model!")
            print(f"   ❌ Total movement of {total_distance:.6f} units is too small")
            print(f"   ❌ Expected at least {expected_min_movement} units of movement")
        
        # Check if movement is progressive (each click should move further)
        print(f"\n   Progressive movement analysis:")
        for i, pos in enumerate(test_data['positions_after_clicks']):
            dx = pos[0] - initial[0]
            dy = pos[1] - initial[1]
            dz = pos[2] - initial[2]
            dist = math.sqrt(dx*dx + dy*dy + dz*dz)
            print(f"   Click {i+1}: distance = {dist:.6f}")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(3000, record_initial_position)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will test 6 button clicks (90° total rotation)")
    print("   - Each click should move the green origin markers")
    print("   - Final analysis will determine if the fix works")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
