#!/usr/bin/env python3
"""
Final Save Test - Load both original and saved files to verify coordinates match
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class FinalSaveTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'test_coordinate_verification.STEP'
        self.original_values = None
        self.transformed_values = None
        self.saved_values = None
        
    def run_test(self):
        print("🔧 FINAL SAVE FUNCTIONALITY TEST")
        print("=" * 50)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_original)
        self.app.exec_()
        
    def step1_load_original(self):
        print("\n1. Loading original file...")
        
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        
        # Capture original values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        self.original_values = self.extract_origin_values(display_text)
        print(f"📊 ORIGINAL VALUES: {self.original_values}")
        
        QTimer.singleShot(1000, self.step2_transform_and_save)
        
    def step2_transform_and_save(self):
        print("\n2. Transforming and saving...")
        
        # Apply transformation
        self.viewer.rotate_shape('x', 45)
        print("✅ Applied 45° X rotation")
        
        # Capture transformed values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        self.transformed_values = self.extract_origin_values(display_text)
        print(f"📊 TRANSFORMED VALUES: {self.transformed_values}")
        
        # Save the file
        try:
            self.viewer.active_viewer = "top"
            loader = self.viewer.step_loader_left
            current_pos = self.viewer._extract_position_from_display("top")
            current_rot = self.viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            orig_rot = self.viewer.orig_rot_left if hasattr(self.viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
            
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"✅ Saved to {self.saved_file}")
                QTimer.singleShot(1000, self.step3_load_saved)
            else:
                print("❌ Save failed")
                self.app.quit()
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            self.app.quit()
            
    def step3_load_saved(self):
        print("\n3. Loading saved file into BOTTOM screen...")

        # Set active viewer to bottom before loading
        self.viewer.active_viewer = "bottom"

        # Load the saved file into the BOTTOM viewer
        success = self.viewer.load_step_file_direct(self.saved_file)

        if not success:
            print(f"❌ Failed to load {self.saved_file} into BOTTOM viewer")
            self.app.quit()
            return

        print(f"✅ Loaded {self.saved_file} into BOTTOM viewer")

        # Capture saved file values from BOTTOM viewer
        display_text = self.viewer.combined_text_actor_right.GetInput()
        self.saved_values = self.extract_origin_values(display_text)
        print(f"📊 SAVED FILE VALUES (BOTTOM): {self.saved_values}")

        QTimer.singleShot(1000, self.step4_show_comparison)
        
    def step4_show_comparison(self):
        print("\n4. FINAL COMPARISON:")
        print("=" * 50)
        
        print(f"📊 ORIGINAL FILE:    {self.original_values}")
        print(f"📊 TRANSFORMED:      {self.transformed_values}")
        print(f"📊 SAVED FILE:       {self.saved_values}")
        
        # Check if transformed and saved values match
        if self.transformed_values and self.saved_values:
            tolerance = 0.01
            matches = (
                abs(self.transformed_values['x'] - self.saved_values['x']) < tolerance and
                abs(self.transformed_values['y'] - self.saved_values['y']) < tolerance and
                abs(self.transformed_values['z'] - self.saved_values['z']) < tolerance
            )
            
            if matches:
                print("\n🎉 SUCCESS: SAVE FUNCTIONALITY IS WORKING!")
                print("✅ Transformed values match saved file values")
                print("✅ The fix is working correctly")
                
                # Show success dialog
                QMessageBox.information(
                    self.viewer, 
                    "Save Test SUCCESS", 
                    f"✅ SAVE FUNCTIONALITY IS WORKING!\n\n"
                    f"Original:    {self.original_values}\n"
                    f"Transformed: {self.transformed_values}\n"
                    f"Saved File:  {self.saved_values}\n\n"
                    f"The transformed and saved values match perfectly!\n"
                    f"Your save functionality is now fixed."
                )
            else:
                print("\n❌ FAILURE: Values do not match")
                print("❌ Save functionality still has issues")
                
                # Show failure dialog
                QMessageBox.critical(
                    self.viewer, 
                    "Save Test FAILED", 
                    f"❌ SAVE FUNCTIONALITY STILL HAS ISSUES!\n\n"
                    f"Original:    {self.original_values}\n"
                    f"Transformed: {self.transformed_values}\n"
                    f"Saved File:  {self.saved_values}\n\n"
                    f"The values do not match - more debugging needed."
                )
        else:
            print("\n❌ ERROR: Could not extract values for comparison")
            
        print("\n" + "=" * 50)
        print("TEST COMPLETE")
        print("TOP screen shows: TRANSFORMED model")
        print("BOTTOM screen shows: SAVED FILE loaded")
        print("You can now visually compare both screens")
        print("=" * 50)
        
    def extract_origin_values(self, display_text):
        """Extract Origin values from display text"""
        import re
        origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        match = re.search(origin_pattern, display_text)
        
        if match:
            return {
                'x': float(match.group(1)),
                'y': float(match.group(2)),
                'z': float(match.group(3))
            }
        return None

if __name__ == '__main__':
    test = FinalSaveTest()
    test.run_test()
