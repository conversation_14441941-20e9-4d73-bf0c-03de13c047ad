#!/usr/bin/env python3
"""
REAL TEST: Read actual VTK actor positions of BOTH origin markers
This will tell us the TRUTH about whether they're moving or not.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import <PERSON><PERSON>iewerTDK

def get_all_origin_marker_positions(viewer):
    """Get positions of ALL origin markers"""
    positions = {}
    
    # Green origin markers (part origin)
    if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
        positions['green_sphere'] = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
    else:
        positions['green_sphere'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'part_origin_x_arrow') and viewer.vtk_renderer_left.part_origin_x_arrow:
        positions['green_x_arrow'] = viewer.vtk_renderer_left.part_origin_x_arrow.GetPosition()
    else:
        positions['green_x_arrow'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'part_origin_y_arrow') and viewer.vtk_renderer_left.part_origin_y_arrow:
        positions['green_y_arrow'] = viewer.vtk_renderer_left.part_origin_y_arrow.GetPosition()
    else:
        positions['green_y_arrow'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'part_origin_z_arrow') and viewer.vtk_renderer_left.part_origin_z_arrow:
        positions['green_z_arrow'] = viewer.vtk_renderer_left.part_origin_z_arrow.GetPosition()
    else:
        positions['green_z_arrow'] = None
    
    # Red origin markers (world origin)
    if hasattr(viewer.vtk_renderer_left, 'origin_sphere') and viewer.vtk_renderer_left.origin_sphere:
        positions['red_sphere'] = viewer.vtk_renderer_left.origin_sphere.GetPosition()
    else:
        positions['red_sphere'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'origin_x_arrow') and viewer.vtk_renderer_left.origin_x_arrow:
        positions['red_x_arrow'] = viewer.vtk_renderer_left.origin_x_arrow.GetPosition()
    else:
        positions['red_x_arrow'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'origin_y_arrow') and viewer.vtk_renderer_left.origin_y_arrow:
        positions['red_y_arrow'] = viewer.vtk_renderer_left.origin_y_arrow.GetPosition()
    else:
        positions['red_y_arrow'] = None
        
    if hasattr(viewer.vtk_renderer_left, 'origin_z_arrow') and viewer.vtk_renderer_left.origin_z_arrow:
        positions['red_z_arrow'] = viewer.vtk_renderer_left.origin_z_arrow.GetPosition()
    else:
        positions['red_z_arrow'] = None
    
    return positions

def print_positions(positions, label):
    """Print all positions in a readable format"""
    print(f"\n{label}:")
    print("=" * 60)
    
    # Green markers
    print("🟢 GREEN ORIGIN MARKERS (Part Origin):")
    for name, pos in positions.items():
        if name.startswith('green_') and pos is not None:
            print(f"   {name}: ({pos[0]:.6f}, {pos[1]:.6f}, {pos[2]:.6f})")
        elif name.startswith('green_'):
            print(f"   {name}: NOT FOUND")
    
    # Red markers  
    print("🔴 RED ORIGIN MARKERS (World Origin):")
    for name, pos in positions.items():
        if name.startswith('red_') and pos is not None:
            print(f"   {name}: ({pos[0]:.6f}, {pos[1]:.6f}, {pos[2]:.6f})")
        elif name.startswith('red_'):
            print(f"   {name}: NOT FOUND")

def compare_positions(before, after):
    """Compare positions and report movement"""
    print(f"\n📊 MOVEMENT ANALYSIS:")
    print("=" * 60)
    
    any_movement = False
    
    for name in before.keys():
        if before[name] is not None and after[name] is not None:
            before_pos = before[name]
            after_pos = after[name]
            
            # Calculate movement
            dx = after_pos[0] - before_pos[0]
            dy = after_pos[1] - before_pos[1] 
            dz = after_pos[2] - before_pos[2]
            distance = (dx**2 + dy**2 + dz**2)**0.5
            
            if distance > 0.000001:  # Very small threshold
                print(f"✅ {name}: MOVED {distance:.6f} units")
                print(f"   Movement: ({dx:.6f}, {dy:.6f}, {dz:.6f})")
                any_movement = True
            else:
                print(f"❌ {name}: NO MOVEMENT (distance: {distance:.9f})")
        elif before[name] is None and after[name] is None:
            print(f"⚪ {name}: NOT FOUND")
        else:
            print(f"⚠️ {name}: INCONSISTENT STATE")
    
    return any_movement

def main():
    print("🔍 REAL ORIGIN MARKER POSITION TEST")
    print("=" * 60)
    print("This test will read the ACTUAL VTK actor positions")
    print("of ALL origin markers before and after rotation.")
    print("No guessing, no debug output - just raw position data.")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load STEP file
    step_file = "e:/Python/3d-view/debug_save_test.STEP"
    if not os.path.exists(step_file):
        print(f"❌ ERROR: STEP file not found: {step_file}")
        return False
        
    print(f"\n📁 Loading STEP file: {step_file}")
    success = viewer.load_step_file_direct(step_file)
    if not success:
        print("❌ ERROR: Failed to load STEP file")
        return False
        
    print("✅ STEP file loaded successfully")
    
    # Wait for loading to complete
    app.processEvents()
    time.sleep(2)
    
    # Get BEFORE positions
    positions_before = get_all_origin_marker_positions(viewer)
    print_positions(positions_before, "📍 POSITIONS BEFORE ROTATION")
    
    # Apply X+ rotation
    print(f"\n🔄 Applying X+ rotation (15 degrees)...")
    viewer.rotate_shape('x', 15)
    app.processEvents()
    time.sleep(1)
    
    # Get AFTER positions
    positions_after = get_all_origin_marker_positions(viewer)
    print_positions(positions_after, "📍 POSITIONS AFTER ROTATION")
    
    # Compare and analyze
    any_movement = compare_positions(positions_before, positions_after)
    
    # Final verdict
    print(f"\n🎯 FINAL VERDICT:")
    print("=" * 60)
    if any_movement:
        print("✅ SUCCESS: Origin markers ARE moving during rotation!")
        result = True
    else:
        print("❌ FAILURE: Origin markers are NOT moving during rotation!")
        result = False
    
    # Keep viewer open briefly
    print(f"\n👁️ Keeping viewer open for 3 seconds...")
    QTimer.singleShot(3000, app.quit)
    app.exec_()
    
    return result

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ TEST RESULT: The fix is working!")
        sys.exit(0)
    else:
        print("\n❌ TEST RESULT: The fix is NOT working!")
        sys.exit(1)
