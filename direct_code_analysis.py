#!/usr/bin/env python3
"""
DIRECT CODE ANALYSIS - Find the exact issue without running GUI
============================================================

This analyzes the code directly to find why the left button fix isn't working.
"""

import re
import os

def analyze_rotate_shape_fix():
    """Analyze the rotate_shape method to see if the fix is correct"""
    
    print("=" * 80)
    print("DIRECT CODE ANALYSIS - LEFT BUTTON ORIGIN MOVEMENT")
    print("=" * 80)
    
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✓ Loaded step_viewer.py")
    except Exception as e:
        print(f"✗ Failed to load step_viewer.py: {e}")
        return
    
    # Extract the rotate_shape method
    print("\n1. EXTRACTING rotate_shape METHOD...")
    
    method_pattern = r'def rotate_shape\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    matches = re.findall(method_pattern, content, re.DOTALL)
    
    if not matches:
        print("✗ Could not find rotate_shape method")
        return
    
    method_body = matches[0]
    print(f"✓ Found rotate_shape method ({len(method_body)} characters)")
    
    # Analyze the fix implementation
    print("\n2. ANALYZING FIX IMPLEMENTATION...")
    
    # Check for the new calculation method call
    if '_calculate_model_center_after_rotation' in method_body:
        print("✓ Found _calculate_model_center_after_rotation call")
    else:
        print("✗ Missing _calculate_model_center_after_rotation call")
        return
    
    # Check for delta initialization
    if 'delta_x = delta_y = delta_z = 0.0' in method_body:
        print("✓ Found delta initialization")
    else:
        print("✗ Missing delta initialization")
    
    # Check for current_pos updates
    if "self.current_pos_left['x'] = new_center[0]" in method_body:
        print("✓ Found current_pos_left updates")
    else:
        print("✗ Missing current_pos_left updates")
    
    # Check for AddPosition calls
    if 'origin_actor.AddPosition(delta_x, delta_y, delta_z)' in method_body:
        print("✓ Found AddPosition calls with delta")
    else:
        print("✗ Missing AddPosition calls with delta")
    
    # Now let's look at the actual implementation details
    print("\n3. DETAILED IMPLEMENTATION ANALYSIS...")
    
    # Extract the specific fix sections
    lines = method_body.split('\n')
    
    # Find the origin actor fix section
    origin_fix_start = -1
    for i, line in enumerate(lines):
        if 'CRITICAL FIX: Calculate and update the new model center' in line:
            origin_fix_start = i
            break
    
    if origin_fix_start >= 0:
        print("✓ Found origin fix section starting at line", origin_fix_start)
        
        # Show the fix implementation
        print("\n   ORIGIN FIX IMPLEMENTATION:")
        for i in range(origin_fix_start, min(origin_fix_start + 25, len(lines))):
            if i < len(lines):
                print(f"   {i:3d}: {lines[i]}")
        
        # Check for potential issues
        print("\n4. CHECKING FOR POTENTIAL ISSUES...")
        
        # Issue 1: Check if the condition is correct
        condition_line = None
        for i in range(origin_fix_start, min(origin_fix_start + 10, len(lines))):
            if 'if hasattr(self, \'current_pos_left\') and actors_rotated:' in lines[i]:
                condition_line = lines[i]
                break
        
        if condition_line:
            print("✓ Found correct condition check")
            print(f"   Condition: {condition_line.strip()}")
        else:
            print("✗ Missing or incorrect condition check")
        
        # Issue 2: Check if new_center is used correctly
        new_center_usage = []
        for i in range(origin_fix_start, min(origin_fix_start + 25, len(lines))):
            if 'new_center' in lines[i]:
                new_center_usage.append(lines[i].strip())
        
        if new_center_usage:
            print("✓ Found new_center usage:")
            for usage in new_center_usage:
                print(f"     {usage}")
        else:
            print("✗ new_center not used correctly")
        
        # Issue 3: Check the method call
        method_call_line = None
        for i in range(origin_fix_start, min(origin_fix_start + 15, len(lines))):
            if '_calculate_model_center_after_rotation' in lines[i]:
                method_call_line = lines[i]
                break
        
        if method_call_line:
            print("✓ Found method call:")
            print(f"   {method_call_line.strip()}")
        else:
            print("✗ Method call not found")
    
    else:
        print("✗ Could not find origin fix section")
    
    # Check the _calculate_model_center_after_rotation method
    print("\n5. CHECKING _calculate_model_center_after_rotation METHOD...")
    
    calc_method_pattern = r'def _calculate_model_center_after_rotation\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    calc_matches = re.findall(calc_method_pattern, content, re.DOTALL)
    
    if calc_matches:
        calc_method_body = calc_matches[0]
        print("✓ Found _calculate_model_center_after_rotation method")
        
        # Check key components
        if 'GetBounds()' in calc_method_body:
            print("✓ Method uses GetBounds()")
        else:
            print("✗ Method missing GetBounds()")
        
        if 'return (center_x, center_y, center_z)' in calc_method_body:
            print("✓ Method returns center coordinates")
        else:
            print("✗ Method doesn't return coordinates")
        
        # Show the method
        print("\n   METHOD IMPLEMENTATION:")
        calc_lines = calc_method_body.split('\n')
        for i, line in enumerate(calc_lines[:15]):
            print(f"   {i:2d}: {line}")
    else:
        print("✗ _calculate_model_center_after_rotation method not found")
    
    print("\n" + "=" * 80)
    print("DIAGNOSIS")
    print("=" * 80)
    
    # Provide diagnosis based on findings
    if ('_calculate_model_center_after_rotation' in method_body and 
        'origin_actor.AddPosition(delta_x, delta_y, delta_z)' in method_body and
        "self.current_pos_left['x'] = new_center[0]" in method_body):
        
        print("🎯 THE FIX IMPLEMENTATION LOOKS CORRECT!")
        print()
        print("Possible reasons it's still not working:")
        print("1. The _calculate_model_center_after_rotation method returns None")
        print("2. The actors_rotated flag is not being set to True")
        print("3. The origin_actors list is empty or doesn't exist")
        print("4. The new_center calculation is returning the same values")
        print("5. The AddPosition calls are being made but with zero deltas")
        print()
        print("NEXT STEP: Run a simple test to see which of these is the issue.")
    else:
        print("❌ THE FIX IMPLEMENTATION HAS ISSUES!")
        print("The code is missing key components.")

if __name__ == "__main__":
    analyze_rotate_shape_fix()
