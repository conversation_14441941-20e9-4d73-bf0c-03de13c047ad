#!/usr/bin/env python3
"""
Test to analyze the rotation problem:
1. Calculate where the origin markers SHOULD be after rotation
2. Compare with where they actually are
3. Verify the coordinate conversion from degrees to actual positions
"""

import sys
import os
import math
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer
import vtk

# Import the main program
from step_viewer import StepViewerTDK

def calculate_expected_position(original_pos, rotation_degrees, axis):
    """Calculate where a point should be after rotation around world origin"""
    x, y, z = original_pos
    rad = math.radians(rotation_degrees)
    
    if axis == 'x':
        # Rotation around X-axis
        new_y = y * math.cos(rad) - z * math.sin(rad)
        new_z = y * math.sin(rad) + z * math.cos(rad)
        return (x, new_y, new_z)
    elif axis == 'y':
        # Rotation around Y-axis  
        new_x = x * math.cos(rad) + z * math.sin(rad)
        new_z = -x * math.sin(rad) + z * math.cos(rad)
        return (new_x, y, new_z)
    elif axis == 'z':
        # Rotation around Z-axis
        new_x = x * math.cos(rad) - y * math.sin(rad)
        new_y = x * math.sin(rad) + y * math.cos(rad)
        return (new_x, new_y, z)
    
    return original_pos

def main():
    print("🔧 ANALYZING ROTATION COORDINATE CONVERSION")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    def analyze_rotation():
        print("\n3. ANALYZING ROTATION COORDINATE CONVERSION")
        print("-" * 50)
        
        # Get model center (this is where origin markers should be)
        renderer = viewer.vtk_renderer_left
        if hasattr(viewer, 'step_actors_left') and viewer.step_actors_left:
            # Get model bounds
            bounds = viewer.step_actors_left[0].GetBounds()
            model_center = [
                (bounds[0] + bounds[1]) / 2,  # X center
                (bounds[2] + bounds[3]) / 2,  # Y center  
                (bounds[4] + bounds[5]) / 2   # Z center
            ]
            print(f"   Model center (where origin should be): {model_center}")
        else:
            print("   ❌ No model actors found")
            return
            
        # Get current origin marker position
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            current_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Current green sphere position: {current_pos}")
        else:
            print("   ❌ No green sphere found")
            return
            
        # Calculate where origin should be after 15° X rotation
        print(f"\n4. CALCULATING EXPECTED POSITION AFTER 15° X ROTATION")
        print("-" * 50)
        
        # The origin markers should rotate around the model center, not world origin
        # So we need to:
        # 1. Translate to origin (subtract model center)
        # 2. Rotate
        # 3. Translate back (add model center)
        
        relative_pos = [
            current_pos[0] - model_center[0],
            current_pos[1] - model_center[1], 
            current_pos[2] - model_center[2]
        ]
        print(f"   Origin relative to model center: {relative_pos}")
        
        # Rotate the relative position
        rotated_relative = calculate_expected_position(relative_pos, 15, 'x')
        print(f"   After 15° X rotation (relative): {rotated_relative}")
        
        # Translate back to world coordinates
        expected_final_pos = [
            rotated_relative[0] + model_center[0],
            rotated_relative[1] + model_center[1],
            rotated_relative[2] + model_center[2]
        ]
        print(f"   Expected final position: {expected_final_pos}")
        
        # Now click the X+ button and see what actually happens
        print(f"\n5. CLICKING X+ BUTTON AND MEASURING ACTUAL RESULT")
        print("-" * 50)
        
        buttons = viewer.findChildren(QPushButton)
        x_plus_button = None
        for button in buttons:
            if button.text() == "X+":
                x_plus_button = button
                break
        
        if x_plus_button:
            x_plus_button.click()
            QTimer.singleShot(1000, lambda: measure_actual_result(expected_final_pos))
        else:
            print("   ❌ Could not find X+ button")
    
    def measure_actual_result(expected_pos):
        print(f"\n6. MEASURING ACTUAL RESULT AFTER BUTTON CLICK")
        print("-" * 50)
        
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            actual_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Actual green sphere position: {actual_pos}")
            print(f"   Expected position: {expected_pos}")
            
            # Calculate the difference
            diff = [
                actual_pos[0] - expected_pos[0],
                actual_pos[1] - expected_pos[1],
                actual_pos[2] - expected_pos[2]
            ]
            print(f"   Difference (actual - expected): {diff}")
            
            # Calculate distance
            distance = math.sqrt(diff[0]**2 + diff[1]**2 + diff[2]**2)
            print(f"   Distance error: {distance:.6f}")
            
            if distance < 0.001:
                print("   ✅ ORIGIN MARKERS ARE FOLLOWING MODEL CORRECTLY!")
            else:
                print("   ❌ ORIGIN MARKERS ARE NOT FOLLOWING MODEL CORRECTLY!")
                print("   🔧 The problem is that origin markers are rotating around world origin (0,0,0)")
                print("   🔧 instead of rotating around the model center and following it.")
        else:
            print("   ❌ No green sphere found after rotation")
    
    # Start analysis after viewer is fully loaded
    QTimer.singleShot(3000, analyze_rotation)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will show the mathematical analysis of the rotation problem")
    print("   - Compare expected vs actual positions")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
