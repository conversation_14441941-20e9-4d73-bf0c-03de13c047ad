#!/usr/bin/env python3
"""
AUTOMATED DEBUG TEST - NO USER INTERACTION REQUIRED
Tests button rotation and yellow text updates with real STEP file
Captures all debug output to identify the exact issue
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class AutomatedButtonTextDebug:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        self.debug_log = []
        
    def log(self, message):
        """Log debug messages"""
        print(message)
        self.debug_log.append(message)
        
    def start_test(self):
        """Start the automated test"""
        self.log("🤖 AUTOMATED BUTTON TEXT DEBUG TEST - NO USER INTERACTION")
        self.log("=" * 60)
        
        # Create viewer but don't show it (headless)
        self.viewer = StepViewerTDK()
        # Don't show the GUI - run headless
        # self.viewer.show()
        
        # Start test immediately
        QTimer.singleShot(1000, self.begin_test)
        
        return self.app.exec_()
        
    def begin_test(self):
        """Begin the test sequence"""
        self.log("🔧 Starting automated test sequence...")
        self.timer.start(2000)  # Run test steps every 2 seconds
        
    def run_test_step(self):
        """Run each test step"""
        if self.test_step == 0:
            self.log("\n=== STEP 1: Load STEP file directly ===")
            self.load_step_file()
            
        elif self.test_step == 1:
            self.log("\n=== STEP 2: Check initial state ===")
            self.check_initial_state()
            
        elif self.test_step == 2:
            self.log("\n=== STEP 3: Simulate X+15° button click ===")
            self.simulate_button_click('x', 15.0)
            
        elif self.test_step == 3:
            self.log("\n=== STEP 4: Check state after X+15° ===")
            self.check_rotation_state("AFTER X+15°")
            
        elif self.test_step == 4:
            self.log("\n=== STEP 5: Simulate Y+15° button click ===")
            self.simulate_button_click('y', 15.0)
            
        elif self.test_step == 5:
            self.log("\n=== STEP 6: Check state after Y+15° ===")
            self.check_rotation_state("AFTER Y+15°")
            
        elif self.test_step == 6:
            self.log("\n=== STEP 7: Check text overlay content ===")
            self.check_text_overlay()
            
        elif self.test_step == 7:
            self.log("\n=== STEP 8: Test mouse rotation simulation ===")
            self.simulate_mouse_rotation()
            
        elif self.test_step == 8:
            self.log("\n=== FINAL RESULTS ===")
            self.show_final_results()
            self.timer.stop()
            self.app.quit()
            return
            
        self.test_step += 1
        
    def load_step_file(self):
        """Load STEP file using step_loader"""
        test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
        loaded = False
        
        for test_file in test_files:
            if os.path.exists(test_file):
                self.log(f"📁 Found {test_file}, attempting to load...")
                try:
                    # Load using step_loader directly
                    if hasattr(self.viewer, 'step_loader_left'):
                        success, message = self.viewer.step_loader_left.load_step_file(test_file)
                        if success:
                            self.log(f"✅ STEP loader success: {message}")

                            # Load into VTK renderer using the CORRECT method
                            if hasattr(self.viewer, 'vtk_renderer_left'):
                                # Use display_polydata method like the main program does
                                self.viewer.vtk_renderer_left.clear_view()
                                display_success = self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
                                if display_success:
                                    self.log(f"✅ VTK renderer displayed polydata successfully")

                                    # Check if actors were created
                                    if hasattr(self.viewer.vtk_renderer_left, 'step_actors'):
                                        actor_count = len(self.viewer.vtk_renderer_left.step_actors) if self.viewer.vtk_renderer_left.step_actors else 0
                                        self.log(f"✅ Created {actor_count} VTK actors")
                                    elif hasattr(self.viewer.vtk_renderer_left, 'step_actor'):
                                        if self.viewer.vtk_renderer_left.step_actor:
                                            self.log(f"✅ Created 1 VTK actor")
                                        else:
                                            self.log(f"❌ No VTK actor created")

                                    loaded = True
                                    break
                                else:
                                    self.log(f"❌ VTK renderer failed to display polydata")
                            else:
                                self.log(f"❌ No vtk_renderer_left found")
                        else:
                            self.log(f"❌ STEP loader failed: {message}")
                    else:
                        self.log(f"❌ No step_loader_left found")
                except Exception as e:
                    self.log(f"❌ Exception loading {test_file}: {e}")
        
        if not loaded:
            self.log("❌ No STEP files loaded - continuing with button test only")
            
    def check_initial_state(self):
        """Check initial rotation values"""
        self.log("🔍 CHECKING INITIAL STATE:")
        
        # Check rotation values
        if hasattr(self.viewer, 'current_rot_left'):
            self.log(f"   current_rot_left: {self.viewer.current_rot_left}")
        else:
            self.log("   ❌ current_rot_left not found")
            
        if hasattr(self.viewer, 'model_rot_left'):
            self.log(f"   model_rot_left: {self.viewer.model_rot_left}")
        else:
            self.log("   ❌ model_rot_left not found")
            
    def simulate_button_click(self, axis, degrees):
        """Simulate clicking a rotation button"""
        self.log(f"🔘 SIMULATING {axis.upper()}{'+' if degrees > 0 else ''}{degrees}° BUTTON CLICK")
        
        # Set active viewer
        self.viewer.active_viewer = "top"
        
        # Call rotate_shape method directly (this is what buttons do)
        try:
            self.viewer.rotate_shape(axis, degrees)
            self.log(f"✅ rotate_shape({axis}, {degrees}) called successfully")
        except Exception as e:
            self.log(f"❌ Error in rotate_shape: {e}")
            
    def check_rotation_state(self, stage):
        """Check rotation values after button click"""
        self.log(f"🔍 CHECKING ROTATION STATE - {stage}:")
        
        if hasattr(self.viewer, 'current_rot_left'):
            self.log(f"   current_rot_left: {self.viewer.current_rot_left}")
        else:
            self.log("   ❌ current_rot_left not found")
            
        if hasattr(self.viewer, 'model_rot_left'):
            self.log(f"   model_rot_left: {self.viewer.model_rot_left}")
        else:
            self.log("   ❌ model_rot_left not found")
            
    def check_text_overlay(self):
        """Check what's in the text overlay"""
        self.log("🔍 CHECKING TEXT OVERLAY CONTENT:")

        # Check if text actors exist
        if hasattr(self.viewer, 'combined_text_actor_left'):
            self.log("   ✅ combined_text_actor_left exists")
            try:
                text_content = self.viewer.combined_text_actor_left.GetInput()
                self.log(f"   📝 Current text content: {repr(text_content)}")

                # Check visibility
                visibility = self.viewer.combined_text_actor_left.GetVisibility()
                self.log(f"   👁️ Text actor visibility: {visibility}")
            except Exception as e:
                self.log(f"   ❌ Error getting text content: {e}")
        else:
            self.log("   ❌ combined_text_actor_left not found")

        # Check if step_loader_left.current_polydata exists (critical for visibility)
        if hasattr(self.viewer, 'step_loader_left') and self.viewer.step_loader_left.current_polydata is not None:
            self.log("   ✅ step_loader_left.current_polydata EXISTS - text should be visible")
        else:
            self.log("   ❌ step_loader_left.current_polydata is NONE - text will be HIDDEN!")

        # Force text overlay update
        self.log("   🔄 Forcing text overlay update...")
        try:
            self.viewer.update_text_overlays()
            self.log("   ✅ update_text_overlays() called")

            # Check text content again after update
            if hasattr(self.viewer, 'combined_text_actor_left'):
                text_content = self.viewer.combined_text_actor_left.GetInput()
                self.log(f"   📝 Text after update: {repr(text_content)}")

                # Check visibility after update
                visibility_after = self.viewer.combined_text_actor_left.GetVisibility()
                self.log(f"   👁️ Text actor visibility after update: {visibility_after}")
        except Exception as e:
            self.log(f"   ❌ Error in update_text_overlays: {e}")
            
    def simulate_mouse_rotation(self):
        """Simulate mouse rotation by directly changing actor orientation"""
        self.log("🖱️ SIMULATING MOUSE ROTATION:")
        
        # Check if we have actors to rotate
        if hasattr(self.viewer.vtk_renderer_left, 'step_actor') and self.viewer.vtk_renderer_left.step_actor:
            self.log("   ✅ Found step_actor for mouse rotation test")
            try:
                # Simulate mouse rotation by rotating the actor directly
                self.viewer.vtk_renderer_left.step_actor.RotateWXYZ(30, 1, 0, 0)  # 30° around X
                self.log("   ✅ Applied 30° X rotation to actor")
                
                # Check if text updates
                self.viewer.update_text_overlays()
                self.log("   ✅ Called update_text_overlays after mouse rotation")
                
            except Exception as e:
                self.log(f"   ❌ Error in mouse rotation simulation: {e}")
        else:
            self.log("   ❌ No step_actor found for mouse rotation test")
            
    def show_final_results(self):
        """Show final test results"""
        self.log("\n" + "="*60)
        self.log("🏁 FINAL TEST RESULTS:")
        self.log("="*60)
        
        # Summary of findings
        if hasattr(self.viewer, 'current_rot_left'):
            self.log(f"✅ Final current_rot_left: {self.viewer.current_rot_left}")
        
        if hasattr(self.viewer, 'combined_text_actor_left'):
            try:
                final_text = self.viewer.combined_text_actor_left.GetInput()
                self.log(f"✅ Final text overlay: {repr(final_text)}")
            except:
                self.log("❌ Could not get final text overlay content")
        
        self.log("\n🤖 AUTOMATED TEST COMPLETE - Check output above for issues")

if __name__ == "__main__":
    test = AutomatedButtonTextDebug()
    sys.exit(test.start_test())
