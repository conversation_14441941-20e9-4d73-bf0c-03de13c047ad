#!/usr/bin/env python3
"""
FINAL TEST: Mouse Rotation Text Update Fix
This test verifies that the text overlay updates during mouse rotation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import vtk

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class FinalMouseRotationTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_start_time = None
        self.initial_rotation = None
        self.last_rotation = None
        self.rotation_changes = 0
        
    def start_test(self):
        """Start the comprehensive test"""
        print("🎯 FINAL TEST: Mouse Rotation Text Update Fix")
        print("=" * 55)
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for GUI to initialize, then load model
        QTimer.singleShot(2000, self.load_test_model)
        
        return self.app.exec_()
        
    def load_test_model(self):
        """Load a test model"""
        print("🔧 Loading test model...")
        
        try:
            # Load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                return
                
            # Wait for model to load, then start testing
            QTimer.singleShot(3000, self.verify_fix)
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            
    def verify_fix(self):
        """Verify the fix is working"""
        print("\n🔍 VERIFYING FIX...")
        print("-" * 30)
        
        try:
            # Check interactor style
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                interactor = self.viewer.vtk_renderer_left.interactor
                if interactor:
                    style = interactor.GetInteractorStyle()
                    style_name = style.__class__.__name__
                    print(f"✅ Interactor style: {style_name}")
                    
                    if "TrackballActor" in style_name:
                        print("✅ TrackballActor style confirmed - mouse rotations will affect the model")
                    else:
                        print(f"⚠️  Style is {style_name}, not TrackballActor")
                else:
                    print("❌ No interactor found")
            else:
                print("❌ No VTK renderer found")
                
            # Check initial rotation values
            if hasattr(self.viewer, 'current_rot_left'):
                self.initial_rotation = self.viewer.current_rot_left.copy()
                self.last_rotation = self.initial_rotation.copy()
                print(f"✅ Initial rotation: X={self.initial_rotation['x']:.1f}° Y={self.initial_rotation['y']:.1f}° Z={self.initial_rotation['z']:.1f}°")
            else:
                print("❌ No rotation tracking found")
                
        except Exception as e:
            print(f"❌ Error verifying fix: {e}")
            
        # Start the interactive test
        self.start_interactive_test()
        
    def start_interactive_test(self):
        """Start the interactive test phase"""
        print("\n🖱️  INTERACTIVE TEST PHASE")
        print("=" * 40)
        print("INSTRUCTIONS:")
        print("1. Use your mouse to drag and rotate the model in the TOP viewer")
        print("2. The model should rotate (not the camera)")
        print("3. Watch the text overlay in the top-left corner")
        print("4. The rotation values should update in REAL-TIME as you drag")
        print("5. Test will monitor for 30 seconds, then provide results")
        print("6. Press Ctrl+C to exit early if needed")
        print()
        print("🔍 Monitoring rotation changes...")
        
        # Set up monitoring timer
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_rotation_changes)
        self.monitor_timer.start(500)  # Check every 500ms
        
        # Set up test completion timer
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.complete_test)
        self.test_timer.setSingleShot(True)
        self.test_timer.start(30000)  # 30 seconds
        
        import time
        self.test_start_time = time.time()
        
    def monitor_rotation_changes(self):
        """Monitor for rotation changes during mouse interaction"""
        try:
            if hasattr(self.viewer, 'current_rot_left') and self.last_rotation:
                current = self.viewer.current_rot_left
                
                # Check for changes
                x_change = abs(current['x'] - self.last_rotation['x'])
                y_change = abs(current['y'] - self.last_rotation['y'])
                z_change = abs(current['z'] - self.last_rotation['z'])
                
                total_change = x_change + y_change + z_change
                
                if total_change > 0.1:  # Detect small changes
                    self.rotation_changes += 1
                    print(f"🔄 Change #{self.rotation_changes}: X={current['x']:.1f}° Y={current['y']:.1f}° Z={current['z']:.1f}° (Δ{total_change:.1f}°)")
                    self.last_rotation = current.copy()
                    
        except Exception as e:
            # Don't spam errors during monitoring
            pass
            
    def complete_test(self):
        """Complete the test and show results"""
        self.monitor_timer.stop()
        
        import time
        test_duration = time.time() - self.test_start_time
        
        print("\n" + "=" * 50)
        print("🎯 TEST RESULTS")
        print("=" * 50)
        
        if self.rotation_changes > 0:
            print(f"✅ SUCCESS: Detected {self.rotation_changes} rotation changes during {test_duration:.1f}s test")
            print("✅ Mouse rotation text updates are WORKING!")
            print()
            print("🎉 PROBLEM SOLVED:")
            print("   - Text overlay updates during mouse rotation")
            print("   - TrackballActor style rotates the model (not camera)")
            print("   - Real-time VTK transformation values displayed")
            print("   - Interaction observers trigger text updates")
        else:
            print(f"❌ FAILURE: No rotation changes detected during {test_duration:.1f}s test")
            print("❌ Mouse rotation text updates are NOT working")
            print()
            print("🔧 POSSIBLE ISSUES:")
            print("   - Interactor style not properly set")
            print("   - Text update observers not working")
            print("   - VTK interaction events not firing")
            print("   - Model not responding to mouse input")
            
        # Show final rotation state
        if hasattr(self.viewer, 'current_rot_left') and self.initial_rotation:
            final = self.viewer.current_rot_left
            total_change = (abs(final['x'] - self.initial_rotation['x']) + 
                          abs(final['y'] - self.initial_rotation['y']) + 
                          abs(final['z'] - self.initial_rotation['z']))
            
            print(f"\n📊 ROTATION SUMMARY:")
            print(f"   Initial: X={self.initial_rotation['x']:.1f}° Y={self.initial_rotation['y']:.1f}° Z={self.initial_rotation['z']:.1f}°")
            print(f"   Final:   X={final['x']:.1f}° Y={final['y']:.1f}° Z={final['z']:.1f}°")
            print(f"   Total change: {total_change:.1f}°")
            
        print("\n👋 Test completed. Press Ctrl+C to exit.")

if __name__ == "__main__":
    test = FinalMouseRotationTest()
    try:
        sys.exit(test.start_test())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(0)
