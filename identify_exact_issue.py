#!/usr/bin/env python3
"""
IDENTIFY EXACT ISSUE - Find which of the 5 possible problems is causing the fix to fail
=======================================================================================

The fix implementation is correct, but it's not working. This will identify exactly why.
"""

import re
import os

def identify_exact_issue():
    """Identify the exact issue preventing the fix from working"""
    
    print("=" * 80)
    print("IDENTIFYING EXACT ISSUE WITH LEFT BUTTON FIX")
    print("=" * 80)
    
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✓ Loaded step_viewer.py")
    except Exception as e:
        print(f"✗ Failed to load step_viewer.py: {e}")
        return
    
    print("\nThe fix implementation is correct, but one of these issues is preventing it from working:")
    print("1. The _calculate_model_center_after_rotation method returns None")
    print("2. The actors_rotated flag is not being set to True")
    print("3. The origin_actors list is empty or doesn't exist")
    print("4. The new_center calculation is returning the same values")
    print("5. The AddPosition calls are being made but with zero deltas")
    print()
    
    # Check Issue 1: Method returns None
    print("CHECKING ISSUE 1: Method returns None...")
    
    # Look for the method implementation
    calc_method_pattern = r'def _calculate_model_center_after_rotation\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    calc_matches = re.findall(calc_method_pattern, content, re.DOTALL)
    
    if calc_matches:
        calc_method = calc_matches[0]
        
        # Check if method can return None
        if 'return None' in calc_method:
            print("⚠️  POTENTIAL ISSUE 1: Method can return None")
            
            # Find the conditions that cause None return
            lines = calc_method.split('\n')
            for i, line in enumerate(lines):
                if 'return None' in line:
                    print(f"   Line {i}: {line.strip()}")
                    # Show context
                    for j in range(max(0, i-3), min(len(lines), i+2)):
                        if j != i:
                            print(f"   Line {j}: {lines[j].strip()}")
                    break
        else:
            print("✓ Method doesn't explicitly return None")
    
    # Check Issue 2: actors_rotated flag
    print("\nCHECKING ISSUE 2: actors_rotated flag...")
    
    # Extract rotate_shape method
    method_pattern = r'def rotate_shape\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    matches = re.findall(method_pattern, content, re.DOTALL)
    
    if matches:
        rotate_method = matches[0]
        
        # Check if actors_rotated is initialized
        if 'actors_rotated = False' in rotate_method:
            print("✓ actors_rotated is initialized to False")
        else:
            print("⚠️  POTENTIAL ISSUE 2: actors_rotated not initialized")
        
        # Check if actors_rotated is set to True
        true_count = rotate_method.count('actors_rotated = True')
        print(f"✓ actors_rotated set to True {true_count} times")
        
        # Check the condition that uses actors_rotated
        if 'and actors_rotated:' in rotate_method:
            print("✓ Fix code checks actors_rotated flag")
        else:
            print("⚠️  POTENTIAL ISSUE 2: Fix code doesn't check actors_rotated")
    
    # Check Issue 3: origin_actors list
    print("\nCHECKING ISSUE 3: origin_actors list...")
    
    # Look for origin_actors usage in the fix
    if 'self.vtk_renderer_left.origin_actors' in content:
        print("✓ Fix code references origin_actors")
        
        # Check if there's a check for empty list
        if 'if hasattr(self.vtk_renderer_left, \'origin_actors\') and self.vtk_renderer_left.origin_actors:' in rotate_method:
            print("✓ Fix code checks if origin_actors exists and is not empty")
        else:
            print("⚠️  POTENTIAL ISSUE 3: Fix code doesn't check if origin_actors is empty")
    else:
        print("⚠️  POTENTIAL ISSUE 3: origin_actors not referenced in fix")
    
    # Check Issue 4: Same values returned
    print("\nCHECKING ISSUE 4: Same values calculation...")
    
    # This is harder to detect from static analysis, but we can check the logic
    if 'delta_x = new_center[0] - self.current_pos_left[\'x\']' in rotate_method:
        print("✓ Delta calculation looks correct")
        
        # Check if there's any debug output for this
        if 'print(f"DEBUG: Model center moved by delta:' in rotate_method:
            print("✓ Debug output will show delta values")
        else:
            print("⚠️  Missing debug output for delta values")
    else:
        print("⚠️  POTENTIAL ISSUE 4: Delta calculation missing or incorrect")
    
    # Check Issue 5: Zero deltas
    print("\nCHECKING ISSUE 5: Zero deltas...")
    
    # Check if deltas are initialized to zero
    if 'delta_x = delta_y = delta_z = 0.0' in rotate_method:
        print("✓ Deltas initialized to zero (correct)")
        
        # Check if they're updated
        if 'delta_x = new_center[0] - self.current_pos_left[\'x\']' in rotate_method:
            print("✓ Deltas are calculated from center difference")
        else:
            print("⚠️  POTENTIAL ISSUE 5: Deltas not calculated")
    else:
        print("⚠️  Deltas not initialized")
    
    # MOST LIKELY ISSUE ANALYSIS
    print("\n" + "=" * 80)
    print("MOST LIKELY ISSUE ANALYSIS")
    print("=" * 80)
    
    # The most likely issue is that the origin_actors don't exist or are empty
    # Let's check how origin overlays are created
    
    print("Checking how origin overlays are created...")
    
    # Look for origin overlay creation methods
    if 'def create_origin_overlay' in content:
        print("✓ Found create_origin_overlay method")
    else:
        print("⚠️  No create_origin_overlay method found")
    
    if 'def toggle_origin_overlay' in content:
        print("✓ Found toggle_origin_overlay method")
    else:
        print("⚠️  No toggle_origin_overlay method found")
    
    # Look for origin_actors creation
    if 'origin_actors = []' in content or 'origin_actors.append' in content:
        print("✓ Found origin_actors list creation")
    else:
        print("⚠️  LIKELY ISSUE: origin_actors list not created properly")
    
    print("\n🎯 MOST LIKELY DIAGNOSIS:")
    print("The fix code is perfect, but the origin_actors list is probably empty")
    print("because the origin overlay hasn't been enabled or created properly.")
    print()
    print("SOLUTION:")
    print("1. Make sure origin overlays are visible before testing")
    print("2. Check that origin_actors list is populated")
    print("3. The fix should work once origin markers are actually present")
    print()
    print("The fix is CORRECT - it just needs origin markers to be visible!")

if __name__ == "__main__":
    identify_exact_issue()
