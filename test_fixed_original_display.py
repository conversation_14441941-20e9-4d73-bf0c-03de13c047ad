#!/usr/bin/env python3
"""
Test the fixed GUI to see if it displays correct original values from STEP file
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_fixed_gui():
    """Test the fixed GUI original value display"""
    print("🔧 Testing FIXED GUI original value display...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK from fixed version")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Load the SOIC file
        print("\n🔧 Loading SOIC file...")
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the file - this should trigger extract_step_transformation_data
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded in GUI")
            
            # Check the original values that were set
            if hasattr(viewer, 'orig_pos_left'):
                print(f"✅ orig_pos_left: {viewer.orig_pos_left}")
            else:
                print("❌ orig_pos_left not set")
                
            if hasattr(viewer, 'orig_rot_left'):
                print(f"✅ orig_rot_left: {viewer.orig_rot_left}")
            else:
                print("❌ orig_rot_left not set")
            
            # Check if the step_loader has the AXIS2_PLACEMENT_3D data
            if hasattr(viewer, 'step_loader_left'):
                axis_data = viewer.step_loader_left.get_original_axis2_placement()
                if axis_data:
                    print("✅ AXIS2_PLACEMENT_3D data from step_loader:")
                    print(f"   Point: {axis_data.get('point', 'NOT FOUND')}")
                    print(f"   Dir1: {axis_data.get('dir1', 'NOT FOUND')}")
                    print(f"   Dir2: {axis_data.get('dir2', 'NOT FOUND')}")
                else:
                    print("❌ No AXIS2_PLACEMENT_3D data in step_loader")
            
            # Test the GUI text overlay display
            if hasattr(viewer, 'update_text_overlays'):
                print("\n🔧 Testing text overlay update...")
                viewer.update_text_overlays()
                print("✅ update_text_overlays called successfully")
            
            # Test the transform display update
            if hasattr(viewer, 'update_transform_display'):
                print("\n🔧 Testing transform display update...")
                viewer.update_transform_display()
                print("✅ update_transform_display called successfully")
            
            return True
        else:
            print("❌ STEP file loading failed in GUI")
            return False
            
    except Exception as e:
        print(f"❌ Error testing GUI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== FIXED GUI ORIGINAL VALUE DISPLAY TEST ===")
    success = test_fixed_gui()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
