#!/usr/bin/env python3
"""
Quick test to debug the crash when loading STEP file
"""

import sys
import traceback

try:
    print("🔧 Starting crash debug test...")
    
    # Import the main application
    from step_viewer import StepViewerTDK
    from PyQt5.QtWidgets import QApplication

    print("✅ Imports successful")

    # Create QApplication
    app = QApplication(sys.argv)
    print("✅ QApplication created")

    # Create main window
    window = StepViewerTDK()
    print("✅ StepViewerTDK window created")
    
    # Try to simulate loading a STEP file by calling the method that crashes
    print("🔧 Testing _get_actual_step_file_values method...")
    
    # Test the method that might be causing the crash
    result = window._get_actual_step_file_values("top")
    print(f"✅ _get_actual_step_file_values returned: {result}")
    
    print("✅ All tests passed - no crash detected")
    
except Exception as e:
    print(f"❌ ERROR FOUND: {e}")
    print("❌ FULL TRACEBACK:")
    traceback.print_exc()
    
print("🔧 Test complete")
