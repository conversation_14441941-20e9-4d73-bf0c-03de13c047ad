#!/usr/bin/env python3
"""
Simple test to verify LEFT 6 buttons now move origin markers correctly
This test will load a model, create origin overlay, and test rotation buttons
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

class LeftButtonOriginTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LEFT Button Origin Fix Tester")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(400)
        
        # Status display
        self.status_label = QLabel("Status: Ready to test LEFT button fix")
        left_layout.addWidget(self.status_label)
        
        # Instructions
        instructions = QLabel("""
TESTING THE FIX:
1. Load STEP file
2. Create origin overlay (red markers)
3. Test LEFT buttons (X+, Y+, Z+)
4. Origin markers should ROTATE with model
5. Compare with RIGHT buttons (should MOVE)

EXPECTED BEHAVIOR:
- LEFT buttons: Origin markers ROTATE
- RIGHT buttons: Origin markers MOVE
- Mouse drag: Origin markers ROTATE
        """)
        instructions.setStyleSheet("background-color: lightyellow; padding: 10px; font-size: 9px;")
        left_layout.addWidget(instructions)
        
        # Setup buttons
        setup_btn = QPushButton("1. Load STEP File + Create Origin")
        setup_btn.clicked.connect(self.setup_test)
        left_layout.addWidget(setup_btn)
        
        # LEFT buttons test (should ROTATE origin)
        left_group = QLabel("2. Test LEFT Buttons (should ROTATE origin):")
        left_group.setStyleSheet("font-weight: bold; color: red;")
        left_layout.addWidget(left_group)
        
        btn_x_plus = QPushButton("LEFT X+ (rotate - should rotate origin)")
        btn_x_plus.clicked.connect(lambda: self.test_left_button('x', 15))
        left_layout.addWidget(btn_x_plus)
        
        btn_y_plus = QPushButton("LEFT Y+ (rotate - should rotate origin)")
        btn_y_plus.clicked.connect(lambda: self.test_left_button('y', 15))
        left_layout.addWidget(btn_y_plus)
        
        btn_z_plus = QPushButton("LEFT Z+ (rotate - should rotate origin)")
        btn_z_plus.clicked.connect(lambda: self.test_left_button('z', 15))
        left_layout.addWidget(btn_z_plus)
        
        # RIGHT buttons test (should MOVE origin)
        right_group = QLabel("3. Test RIGHT Buttons (should MOVE origin):")
        right_group.setStyleSheet("font-weight: bold; color: green;")
        left_layout.addWidget(right_group)
        
        btn_x_move = QPushButton("RIGHT X+ (move - should move origin)")
        btn_x_move.clicked.connect(lambda: self.test_right_button('x', 1.0))
        left_layout.addWidget(btn_x_move)
        
        btn_y_move = QPushButton("RIGHT Y+ (move - should move origin)")
        btn_y_move.clicked.connect(lambda: self.test_right_button('y', 1.0))
        left_layout.addWidget(btn_y_move)
        
        btn_z_move = QPushButton("RIGHT Z+ (move - should move origin)")
        btn_z_move.clicked.connect(lambda: self.test_right_button('z', 1.0))
        left_layout.addWidget(btn_z_move)
        
        # Reset button
        reset_btn = QPushButton("4. Reset to Original")
        reset_btn.clicked.connect(self.reset_test)
        left_layout.addWidget(reset_btn)
        
        left_layout.addStretch()
        layout.addWidget(left_panel)
        
        # Right side - The actual viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
    def setup_test(self):
        """Load STEP file and create origin overlay"""
        self.status_label.setText("Status: Loading STEP file...")
        QApplication.processEvents()
        
        # Load STEP file
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if os.path.exists(step_file):
            success = self.viewer.load_step_file_direct(step_file)
            if success:
                self.status_label.setText("Status: Creating origin overlay...")
                QApplication.processEvents()
                
                # Create origin overlay
                self.viewer.create_origin_overlay()
                
                # Check if origin actors were created
                if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
                    count = len(self.viewer.vtk_renderer_left.origin_actors)
                    self.status_label.setText(f"Status: Ready! Origin overlay created ({count} actors)")
                    print(f"✅ Setup complete: {count} origin actors created")
                else:
                    self.status_label.setText("Status: Origin overlay creation failed")
                    print("❌ Origin overlay creation failed")
            else:
                self.status_label.setText("Status: STEP file load failed")
                print("❌ STEP file load failed")
        else:
            self.status_label.setText("Status: STEP file not found")
            print(f"❌ STEP file not found: {step_file}")
        
    def test_left_button(self, axis, degrees):
        """Test LEFT button (should ROTATE origin markers)"""
        print(f"\n🔧 TESTING LEFT BUTTON: {axis}+ rotation by {degrees}°")
        print("   Expected: Origin markers should ROTATE with the model")
        
        # Count origin actors before
        origin_count = 0
        if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
            origin_count = len(self.viewer.vtk_renderer_left.origin_actors)
            print(f"   Before rotation: {origin_count} origin actors present")
        
        # Set active viewer and rotate
        self.viewer.active_viewer = "top"
        self.viewer.rotate_shape(axis, degrees)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.2)
        
        # Update status
        self.status_label.setText(f"Status: LEFT {axis}+ tested - did origin ROTATE?")
        print(f"   ✅ LEFT {axis}+ rotation applied - check if origin markers rotated visually")
        
    def test_right_button(self, axis, amount):
        """Test RIGHT button (should MOVE origin markers)"""
        print(f"\n🔧 TESTING RIGHT BUTTON: {axis}+ movement by {amount}mm")
        print("   Expected: Origin markers should MOVE with the model")
        
        # Set active viewer and move
        self.viewer.active_viewer = "top"
        self.viewer.move_shape(axis, amount)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.2)
        
        # Update status
        self.status_label.setText(f"Status: RIGHT {axis}+ tested - did origin MOVE?")
        print(f"   ✅ RIGHT {axis}+ movement applied - check if origin markers moved visually")
        
    def reset_test(self):
        """Reset to original state"""
        self.status_label.setText("Status: Resetting...")
        QApplication.processEvents()
        
        self.viewer.reset_to_original()
        
        self.status_label.setText("Status: Reset complete - test again!")
        print("🔄 Reset to original state")

def main():
    print("🔥🔥🔥 TESTING LEFT BUTTON ORIGIN FIX 🔥🔥🔥")
    print("This test verifies that LEFT buttons now rotate origin markers like mouse rotation")
    
    app = QApplication(sys.argv)
    
    # Create the test window
    tester = LeftButtonOriginTester()
    tester.show()
    
    print("✅ Test window created")
    print("📋 Follow the numbered instructions in the left panel")
    print("👀 Watch the red origin markers - they should ROTATE with LEFT buttons and MOVE with RIGHT buttons")
    
    # Start the event loop
    app.exec_()

if __name__ == "__main__":
    main()
