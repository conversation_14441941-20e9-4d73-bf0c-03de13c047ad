#!/usr/bin/env python3
"""
Test the mouse rotation text update fix
This will verify that text overlays update properly during mouse rotation
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main viewer
from step_viewer_tdk_modular_fixed import StepViewerTDK

class MouseRotationTextUpdateTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_step)
        self.initial_rotation = None
        
    def start_test(self):
        """Start the test"""
        print("🧪 TESTING MOUSE ROTATION TEXT UPDATE FIX")
        print("=" * 50)
        print("This test will:")
        print("1. Load a STEP file")
        print("2. Monitor text overlay updates")
        print("3. Check if mouse rotations update the text")
        print("4. Verify the fixes are working")
        print()
        
        # Create and show the viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for G<PERSON> to initialize, then start test
        QTimer.singleShot(3000, self.begin_test)
        
        return self.app.exec_()
        
    def begin_test(self):
        """Begin the actual test sequence"""
        print("🔧 GUI initialized, starting test sequence...")
        self.timer.start(4000)  # Run test steps every 4 seconds
        
    def run_test_step(self):
        """Run each test step"""
        if self.test_step == 0:
            print("\n=== STEP 1: Load STEP file ===")
            # Try to load a test file
            test_files = ['test.step', 'SOIC16P127_1270X940X610L89X51.STEP']
            loaded = False
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"Loading {test_file}...")
                    self.viewer.load_step_file_direct(test_file)
                    loaded = True
                    break
            
            if not loaded:
                print("❌ No test STEP file found")
                print("Please place a STEP file named 'test.step' in the directory")
                self.timer.stop()
                QTimer.singleShot(2000, self.app.quit)
                return
                
        elif self.test_step == 1:
            print("\n=== STEP 2: Check initial text state ===")
            self.check_text_state("INITIAL")
            self.capture_initial_rotation()
            
        elif self.test_step == 2:
            print("\n=== STEP 3: Verify interactor style fix ===")
            self.check_interactor_style()
            
        elif self.test_step == 3:
            print("\n=== STEP 4: Test button rotation (should work) ===")
            print("Applying X+15° rotation using button...")
            self.viewer.rotate_shape('x', 15)
            
        elif self.test_step == 4:
            print("\n=== STEP 5: Check text after button rotation ===")
            self.check_text_state("AFTER BUTTON ROTATION")
            
        elif self.test_step == 5:
            print("\n=== STEP 6: Instructions for manual mouse test ===")
            print("🖱️  MANUAL TEST REQUIRED:")
            print("   1. Use your mouse to drag and rotate the model in the TOP viewer")
            print("   2. Watch the text overlay in the top-left corner")
            print("   3. The rotation values should update as you drag")
            print("   4. If using TrackballActor style, the model should rotate")
            print("   5. The text should show changing X, Y, Z rotation values")
            print()
            print("   ✅ SUCCESS: Text updates during mouse dragging")
            print("   ❌ FAILURE: Text stays the same during mouse dragging")
            print()
            print("Test will continue monitoring for 20 seconds...")
            
        elif self.test_step == 6:
            print("\n=== STEP 7: Monitor for rotation changes ===")
            self.monitor_rotation_changes()
            
        elif self.test_step == 7:
            print("\n=== STEP 8: Final verification ===")
            self.check_text_state("FINAL")
            
        elif self.test_step == 8:
            print("\n=== TEST COMPLETE ===")
            print("🎯 SUMMARY:")
            print("- Timer frequency reduced from 100ms to 500ms")
            print("- Interactor style changed to TrackballActor")
            print("- Debug output spam reduced")
            print("- Change detection implemented")
            print()
            print("If mouse rotation updates the text overlay, the fix is working!")
            print("If not, additional debugging may be needed.")
            self.timer.stop()
            QTimer.singleShot(3000, self.app.quit)
            
        self.test_step += 1
        
    def check_text_state(self, stage):
        """Check the current state of text overlays"""
        print(f"🔍 Text overlay state at {stage}:")
        
        if hasattr(self.viewer, 'combined_text_actor_left'):
            text_content = self.viewer.combined_text_actor_left.GetInput()
            print(f"  TOP text: '{text_content}'")
        else:
            print("  ❌ TOP text overlay not found")
            
    def capture_initial_rotation(self):
        """Capture initial rotation values for comparison"""
        try:
            if hasattr(self.viewer, 'current_rot_left'):
                self.initial_rotation = self.viewer.current_rot_left.copy()
                print(f"  Initial rotation captured: {self.initial_rotation}")
            else:
                print("  ❌ current_rot_left not found")
        except Exception as e:
            print(f"  ❌ Error capturing initial rotation: {e}")
            
    def check_interactor_style(self):
        """Check if the interactor style was changed correctly"""
        print("🔍 Checking interactor style:")
        
        try:
            if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
                if hasattr(self.viewer.vtk_renderer_left, 'interactor'):
                    interactor = self.viewer.vtk_renderer_left.interactor
                    style = interactor.GetInteractorStyle()
                    style_name = style.__class__.__name__
                    print(f"  TOP viewer interactor style: {style_name}")
                    
                    if 'TrackballActor' in style_name:
                        print("  ✅ Using TrackballActor style (rotates model)")
                    elif 'TrackballCamera' in style_name:
                        print("  ⚠️  Still using TrackballCamera style (rotates camera)")
                    else:
                        print(f"  ❓ Unknown style: {style_name}")
                else:
                    print("  ❌ Interactor not found")
            else:
                print("  ❌ VTK renderer not found")
        except Exception as e:
            print(f"  ❌ Error checking interactor style: {e}")
            
    def monitor_rotation_changes(self):
        """Monitor for rotation changes during manual testing"""
        try:
            if hasattr(self.viewer, 'current_rot_left') and self.initial_rotation:
                current = self.viewer.current_rot_left
                initial = self.initial_rotation
                
                # Check if rotation has changed significantly
                x_change = abs(current['x'] - initial['x'])
                y_change = abs(current['y'] - initial['y']) 
                z_change = abs(current['z'] - initial['z'])
                
                total_change = x_change + y_change + z_change
                
                print(f"  Rotation change since start:")
                print(f"    X: {initial['x']:.1f}° → {current['x']:.1f}° (Δ{x_change:.1f}°)")
                print(f"    Y: {initial['y']:.1f}° → {current['y']:.1f}° (Δ{y_change:.1f}°)")
                print(f"    Z: {initial['z']:.1f}° → {current['z']:.1f}° (Δ{z_change:.1f}°)")
                print(f"    Total change: {total_change:.1f}°")
                
                if total_change > 5.0:
                    print("  ✅ Significant rotation detected - mouse rotation is working!")
                elif total_change > 1.0:
                    print("  ⚠️  Small rotation detected - may be working")
                else:
                    print("  ❌ No significant rotation detected")
                    
        except Exception as e:
            print(f"  ❌ Error monitoring rotation: {e}")

if __name__ == "__main__":
    tester = MouseRotationTextUpdateTester()
    sys.exit(tester.start_test())
