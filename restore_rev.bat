@echo off
setlocal enabledelayedexpansion

if "%1"=="" (
    echo Usage: restore_rev.bat [revision_number]
    echo Example: restore_rev.bat 56
    echo.
    echo Available revisions in E:\Python\save:
    dir "E:\Python\save\*_rev*.py" /b | findstr /r "_rev[0-9][0-9]\.py$" | sort
    echo.
    pause
    exit /b 1
)

set REV=%1
set SAVE_DIR=E:\Python\save
set CURRENT_DIR=%~dp0

echo.
echo ========================================
echo RESTORING REVISION %REV%
echo ========================================
echo.

REM Check if revision files exist
set MAIN_FILE=%SAVE_DIR%\step_viewer_tdk_modular_fixed_rev%REV%.py
set VTK_FILE=%SAVE_DIR%\vtk_renderer_rev%REV%.py
set STEP_FILE=%SAVE_DIR%\step_loader_rev%REV%.py
set GUI_FILE=%SAVE_DIR%\gui_components_rev%REV%.py

echo Checking for revision %REV% files...

if not exist "%MAIN_FILE%" (
    echo ERROR: Main file not found: %MAIN_FILE%
    echo Available main program revisions:
    dir "%SAVE_DIR%\step_viewer_tdk_modular_fixed_rev*.py" /b
    pause
    exit /b 1
)

if not exist "%VTK_FILE%" (
    echo ERROR: VTK file not found: %VTK_FILE%
    echo Available VTK renderer revisions:
    dir "%SAVE_DIR%\vtk_renderer_rev*.py" /b
    pause
    exit /b 1
)

if not exist "%STEP_FILE%" (
    echo ERROR: Step loader file not found: %STEP_FILE%
    echo Available step loader revisions:
    dir "%SAVE_DIR%\step_loader_rev*.py" /b
    pause
    exit /b 1
)

if not exist "%GUI_FILE%" (
    echo ERROR: GUI components file not found: %GUI_FILE%
    echo Available GUI components revisions:
    dir "%SAVE_DIR%\gui_components_rev*.py" /b
    pause
    exit /b 1
)

echo All revision %REV% files found!
echo.

REM Backup current files first
echo Backing up current files...
copy "%CURRENT_DIR%step_viewer_tdk_modular_fixed.py" "%CURRENT_DIR%step_viewer_tdk_modular_fixed_backup.py" >nul 2>&1
copy "%CURRENT_DIR%vtk_renderer.py" "%CURRENT_DIR%vtk_renderer_backup.py" >nul 2>&1
copy "%CURRENT_DIR%step_loader.py" "%CURRENT_DIR%step_loader_backup.py" >nul 2>&1
copy "%CURRENT_DIR%gui_components.py" "%CURRENT_DIR%gui_components_backup.py" >nul 2>&1

REM Restore files
echo Restoring files from revision %REV%...
echo.

echo Copying: step_viewer_tdk_modular_fixed_rev%REV%.py -^> step_viewer_tdk_modular_fixed.py
copy "%MAIN_FILE%" "%CURRENT_DIR%step_viewer_tdk_modular_fixed.py"
if errorlevel 1 (
    echo ERROR: Failed to copy main file
    pause
    exit /b 1
)

echo Copying: vtk_renderer_rev%REV%.py -^> vtk_renderer.py
copy "%VTK_FILE%" "%CURRENT_DIR%vtk_renderer.py"
if errorlevel 1 (
    echo ERROR: Failed to copy VTK file
    pause
    exit /b 1
)

echo Copying: step_loader_rev%REV%.py -^> step_loader.py
copy "%STEP_FILE%" "%CURRENT_DIR%step_loader.py"
if errorlevel 1 (
    echo ERROR: Failed to copy step loader file
    pause
    exit /b 1
)

echo Copying: gui_components_rev%REV%.py -^> gui_components.py
copy "%GUI_FILE%" "%CURRENT_DIR%gui_components.py"
if errorlevel 1 (
    echo ERROR: Failed to copy GUI components file
    pause
    exit /b 1
)

echo.
echo ========================================
echo RESTORATION COMPLETE!
echo ========================================
echo.
echo Restored revision %REV% files:
echo - step_viewer_tdk_modular_fixed.py
echo - vtk_renderer.py
echo - step_loader.py
echo - gui_components.py
echo.
echo Backup files created:
echo - step_viewer_tdk_modular_fixed_backup.py
echo - vtk_renderer_backup.py
echo - step_loader_backup.py
echo - gui_components_backup.py
echo.

REM Clear Python cache
echo Clearing Python cache...
if exist "__pycache__" rmdir /s /q "__pycache__" >nul 2>&1

echo.
echo Ready to test! Run: python step_viewer_tdk_modular_fixed.py
echo.
pause
