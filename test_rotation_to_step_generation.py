#!/usr/bin/env python3
"""
Test Script: Rotation to STEP File Generation
Tests the complete workflow from loading a model, applying rotations, 
and generating STEP files with different save methods.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

class RotationToStepTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.test_results = []
        self.original_file = None
        
    def log_result(self, step, success, message):
        """Log test results"""
        status = "✅" if success else "❌"
        result = f"{status} {step}: {message}"
        print(result)
        self.test_results.append((step, success, message))
        
    def step1_load_model(self):
        """Step 1: Load a STEP file into the viewer"""
        print("\n🔧 Step 1: Loading STEP file...")
        
        # Find available STEP files
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            self.log_result("Load Model", False, "No STEP files found")
            return False
            
        self.original_file = step_files[0]
        print(f"   Loading: {self.original_file}")
        
        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.step_loader_left.load_step_file(self.original_file)
        
        if success:
            # Display in VTK renderer
            self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
            self.viewer.update_text_overlays()
            
            # Get initial rotation values
            initial_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            print(f"   Initial rotations: X={initial_rot['x']}°, Y={initial_rot['y']}°, Z={initial_rot['z']}°")
            
            self.log_result("Load Model", True, f"Loaded {self.original_file}")
            return True
        else:
            self.log_result("Load Model", False, f"Failed to load {self.original_file}")
            return False
            
    def step2_apply_rotations(self):
        """Step 2: Apply specific rotations to test the system"""
        print("\n🔧 Step 2: Applying test rotations...")
        
        test_rotations = [
            ('x', 30),
            ('y', 45), 
            ('z', 60)
        ]
        
        try:
            for axis, degrees in test_rotations:
                print(f"   Rotating {axis.upper()} by {degrees}°...")
                self.viewer.rotate_shape(axis, degrees)
                self.app.processEvents()
                time.sleep(0.3)

            # Update display to show current values
            self.viewer.update_text_overlays()
            
            # Get current rotation values
            current_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            print(f"   Final rotations: X={current_rot['x']}°, Y={current_rot['y']}°, Z={current_rot['z']}°")
            
            self.log_result("Apply Rotations", True, "Applied X=30°, Y=45°, Z=60°")
            return True
            
        except Exception as e:
            self.log_result("Apply Rotations", False, f"Error: {e}")
            return False
            
    def step3_test_save_methods(self):
        """Step 3: Test all available save methods"""
        print("\n🔧 Step 3: Testing STEP file generation methods...")
        
        # Method 1: Green Button (save with transformations)
        self.test_green_button_save()
        
        # Method 2: Blue Button (save original)
        self.test_blue_button_save()
        
        # Method 3: Direct OpenCASCADE save
        self.test_opencascade_save()
        
    def test_green_button_save(self):
        """Test the Green Button save method"""
        print("\n   🟢 Testing GREEN BUTTON save (with transformations)...")
        
        try:
            test_file = "test_green_save.step"
            if os.path.exists(test_file):
                os.remove(test_file)
                
            # Get current transformation values
            current_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            
            print(f"      Current rotations: {current_rot}")
            print(f"      Current position: {current_pos}")
            
            # Call the save method directly
            loader = self.viewer.step_loader_left
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            
            success = self.viewer._save_step_with_transformations(
                test_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success and os.path.exists(test_file):
                file_size = os.path.getsize(test_file)
                self.log_result("Green Button Save", True, f"Created {test_file} ({file_size:,} bytes)")
            else:
                self.log_result("Green Button Save", False, "File not created")
                
        except Exception as e:
            self.log_result("Green Button Save", False, f"Error: {e}")
            
    def test_blue_button_save(self):
        """Test the Blue Button save method"""
        print("\n   🔵 Testing BLUE BUTTON save (original file)...")
        
        try:
            test_file = "test_blue_save.step"
            if os.path.exists(test_file):
                os.remove(test_file)
                
            # Copy original file (blue button behavior)
            import shutil
            if hasattr(self.viewer.step_loader_left, 'current_filename'):
                shutil.copy2(self.viewer.step_loader_left.current_filename, test_file)
                
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    self.log_result("Blue Button Save", True, f"Created {test_file} ({file_size:,} bytes)")
                else:
                    self.log_result("Blue Button Save", False, "File not created")
            else:
                self.log_result("Blue Button Save", False, "No original filename available")
                
        except Exception as e:
            self.log_result("Blue Button Save", False, f"Error: {e}")
            
    def test_opencascade_save(self):
        """Test direct OpenCASCADE save method"""
        print("\n   ⚙️ Testing OPENCASCADE direct save...")
        
        try:
            test_file = "test_opencascade_save.step"
            if os.path.exists(test_file):
                os.remove(test_file)
                
            # Get transformation values
            loader = self.viewer.step_loader_left
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            current_rot = getattr(self.viewer, 'model_rot_left', {'x': 0, 'y': 0, 'z': 0})
            orig_pos = getattr(self.viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(self.viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            
            # Calculate deltas
            delta_pos = {
                'x': current_pos['x'] - orig_pos['x'],
                'y': current_pos['y'] - orig_pos['y'],
                'z': current_pos['z'] - orig_pos['z']
            }
            delta_rot = {
                'x': current_rot['x'] - orig_rot['x'],
                'y': current_rot['y'] - orig_rot['y'],
                'z': current_rot['z'] - orig_rot['z']
            }
            
            print(f"      Delta rotations: {delta_rot}")
            print(f"      Delta position: {delta_pos}")
            
            # Try OpenCASCADE transformation
            success = self.viewer._save_step_opencascade_transform(
                test_file, loader, delta_pos, delta_rot
            )
            
            if success and os.path.exists(test_file):
                file_size = os.path.getsize(test_file)
                self.log_result("OpenCASCADE Save", True, f"Created {test_file} ({file_size:,} bytes)")
            else:
                self.log_result("OpenCASCADE Save", False, "Transformation failed")
                
        except Exception as e:
            self.log_result("OpenCASCADE Save", False, f"Error: {e}")
            
    def step4_verify_files(self):
        """Step 4: Verify the generated files"""
        print("\n🔧 Step 4: Verifying generated STEP files...")
        
        test_files = [
            "test_green_save.step",
            "test_blue_save.step", 
            "test_opencascade_save.step"
        ]
        
        for filename in test_files:
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"   ✅ {filename}: {file_size:,} bytes")
                
                # Try to load it back to verify it's valid
                try:
                    from step_loader import STEPLoader
                    test_loader = STEPLoader()
                    result = test_loader.load_step_file(filename)
                    if isinstance(result, tuple) and len(result) >= 2:
                        success = result[1] if len(result) == 3 else result[0]
                        if success:
                            print(f"      ✅ File loads successfully")
                        else:
                            print(f"      ❌ File failed to load")
                    else:
                        print(f"      ❌ Unexpected load result")
                except Exception as e:
                    print(f"      ❌ Load test failed: {e}")
            else:
                print(f"   ❌ {filename}: Not found")
                
    def run_complete_test(self):
        """Run the complete rotation-to-STEP workflow test"""
        print("=" * 70)
        print("ROTATION TO STEP FILE GENERATION TEST")
        print("=" * 70)
        
        # Show the viewer
        self.viewer.show()
        self.app.processEvents()
        time.sleep(1)
        
        # Run test sequence
        if not self.step1_load_model():
            return False
            
        if not self.step2_apply_rotations():
            return False
            
        self.step3_test_save_methods()
        self.step4_verify_files()
        
        # Print summary
        print("\n" + "=" * 70)
        print("TEST RESULTS SUMMARY")
        print("=" * 70)
        
        for step, success, message in self.test_results:
            status = "✅" if success else "❌"
            print(f"{status} {step}: {message}")
            
        print("=" * 70)
        print("ROTATION TO STEP GENERATION TEST COMPLETE")
        
        return True

if __name__ == "__main__":
    test = RotationToStepTest()
    test.run_complete_test()
    
    # Keep application running briefly to see results
    QTimer.singleShot(8000, test.app.quit)
    test.app.exec_()
