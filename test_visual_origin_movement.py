#!/usr/bin/env python3
"""
Test that the visual origin markers (green sphere + arrows) move with the model
when rotation buttons are clicked.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🔧 TESTING VISUAL ORIGIN MARKER MOVEMENT")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    def test_origin_movement():
        print("\n3. TESTING ORIGIN MARKER MOVEMENT")
        
        # Get initial position of green origin markers
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            initial_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Initial green sphere position: {initial_pos}")
            
            # Click X+ rotation button
            print("   Clicking X+ rotation button...")
            viewer.rotate_shape('x', 15)
            
            # Check new position after a delay
            QTimer.singleShot(500, check_movement)
        else:
            print("   ❌ No green origin sphere found")
    
    def check_movement():
        print("\n4. CHECKING MOVEMENT RESULTS")
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            final_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Final green sphere position: {final_pos}")
            
            # Check if position changed
            initial_pos = (0.0, 0.0, 0.0)  # Assuming it started at origin
            moved = (abs(final_pos[0] - initial_pos[0]) > 0.001 or 
                    abs(final_pos[1] - initial_pos[1]) > 0.001 or 
                    abs(final_pos[2] - initial_pos[2]) > 0.001)
            
            if moved:
                print("   ✅ Green origin markers DID move - fix is working!")
                print(f"   Movement: ({final_pos[0]:.3f}, {final_pos[1]:.3f}, {final_pos[2]:.3f})")
            else:
                print("   ❌ Green origin markers did NOT move - fix is not working")
                
            # Also check the yellow text values
            current_pos = viewer.current_pos_left
            print(f"   Yellow text Origin values: X={current_pos['x']:.3f}, Y={current_pos['y']:.3f}, Z={current_pos['z']:.3f}")
        else:
            print("   ❌ No green origin sphere found after rotation")
            
        print("\n5. MANUAL VERIFICATION:")
        print("   Look at the 3D viewer:")
        print("   - Green sphere and arrows should have moved with the model")
        print("   - Yellow text Origin values should match the new position")
        print("   - Red semicircle and arrows should stay at (0,0,0)")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(3000, test_origin_movement)
    
    print("\n👀 WATCH THE 3D VIEWER:")
    print("   - This test will automatically rotate the model")
    print("   - Watch if the GREEN origin markers move with the model")
    print("   - RED markers should stay at (0,0,0)")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
