#!/usr/bin/env python3
"""
Test to check cursor text visibility after loading STEP file
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_cursor_visibility():
    """Test cursor text visibility after STEP file loading"""
    print("🔧 Testing cursor text visibility...")
    
    try:
        # Import the fixed GUI class
        from step_viewer_tdk_modular_fixed import StepViewerTDK
        print("✅ Successfully imported StepViewerTDK")
        
        # Create application (but don't show GUI)
        app = QApplication([])
        
        # Create the viewer instance
        viewer = StepViewerTDK()
        print("✅ GUI instance created")
        
        # Check initial cursor text state
        print("\n🔧 Checking INITIAL cursor text state...")
        if hasattr(viewer, 'cursor_text_actor_left'):
            visibility = viewer.cursor_text_actor_left.GetVisibility()
            input_text = viewer.cursor_text_actor_left.GetInput()
            print(f"✅ TOP cursor text actor exists")
            print(f"   Initial visibility: {visibility}")
            print(f"   Initial text: {repr(input_text)}")
        else:
            print("❌ TOP cursor text actor not found initially")
        
        if hasattr(viewer, 'cursor_text_actor_right'):
            visibility = viewer.cursor_text_actor_right.GetVisibility()
            input_text = viewer.cursor_text_actor_right.GetInput()
            print(f"✅ BOTTOM cursor text actor exists")
            print(f"   Initial visibility: {visibility}")
            print(f"   Initial text: {repr(input_text)}")
        else:
            print("❌ BOTTOM cursor text actor not found initially")
        
        # Load STEP file in TOP viewer
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"\n🔧 Loading STEP file in TOP viewer: {step_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if success:
            print("✅ STEP file loaded in TOP viewer")
            
            # Check cursor text state AFTER loading
            print("\n🔧 Checking cursor text state AFTER loading...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility = viewer.cursor_text_actor_left.GetVisibility()
                input_text = viewer.cursor_text_actor_left.GetInput()
                position = viewer.cursor_text_actor_left.GetPosition()
                font_size = viewer.cursor_text_actor_left.GetTextProperty().GetFontSize()
                color = viewer.cursor_text_actor_left.GetTextProperty().GetColor()
                
                print(f"✅ TOP cursor text actor after loading:")
                print(f"   Visibility: {visibility} (1=visible, 0=hidden)")
                print(f"   Text: {repr(input_text)}")
                print(f"   Position: {position}")
                print(f"   Font size: {font_size}")
                print(f"   Color: {color}")
                
                if visibility == 0:
                    print("❌ PROBLEM: TOP cursor text is HIDDEN after loading!")
                elif visibility == 1:
                    print("✅ GOOD: TOP cursor text is VISIBLE after loading")
                    if not input_text.startswith("CURSOR:"):
                        print("❌ PROBLEM: TOP cursor text content is wrong!")
                    else:
                        print("✅ GOOD: TOP cursor text content is correct")
            else:
                print("❌ TOP cursor text actor not found after loading")
            
            if hasattr(viewer, 'cursor_text_actor_right'):
                visibility = viewer.cursor_text_actor_right.GetVisibility()
                input_text = viewer.cursor_text_actor_right.GetInput()
                
                print(f"✅ BOTTOM cursor text actor after loading:")
                print(f"   Visibility: {visibility} (1=visible, 0=hidden)")
                print(f"   Text: {repr(input_text)}")
                
                if visibility == 0:
                    print("❌ PROBLEM: BOTTOM cursor text is HIDDEN after loading!")
                elif visibility == 1:
                    print("✅ GOOD: BOTTOM cursor text is VISIBLE after loading")
            else:
                print("❌ BOTTOM cursor text actor not found after loading")
            
            # Force update text overlays to see what happens
            print("\n🔧 Forcing text overlay update...")
            viewer.update_text_overlays()
            
            # Check again after forced update
            print("\n🔧 Checking cursor text state AFTER forced update...")
            if hasattr(viewer, 'cursor_text_actor_left'):
                visibility = viewer.cursor_text_actor_left.GetVisibility()
                input_text = viewer.cursor_text_actor_left.GetInput()
                print(f"   TOP cursor after update: visibility={visibility}, text={repr(input_text)}")
            
            if hasattr(viewer, 'cursor_text_actor_right'):
                visibility = viewer.cursor_text_actor_right.GetVisibility()
                input_text = viewer.cursor_text_actor_right.GetInput()
                print(f"   BOTTOM cursor after update: visibility={visibility}, text={repr(input_text)}")
            
            return True
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== CURSOR VISIBILITY TEST ===")
    success = test_cursor_visibility()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILED'}")
