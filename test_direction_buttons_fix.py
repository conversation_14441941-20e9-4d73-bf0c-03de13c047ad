#!/usr/bin/env python3
"""
Test script to verify the LEFT 6 buttons (Direction) now move world origin correctly
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

class DirectionButtonFixTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Direction Button Fix Tester")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left panel - controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(350)
        
        # Status display
        self.status_label = QLabel("Status: Ready to test the fix")
        left_layout.addWidget(self.status_label)
        
        # Instructions
        instructions = QLabel("""
TESTING INSTRUCTIONS:
1. Load a STEP file
2. Create origin overlay (red markers)
3. Test LEFT buttons (should rotate origin)
4. Test RIGHT buttons (should move origin)
5. Both should move the world origin now!
        """)
        instructions.setStyleSheet("background-color: lightyellow; padding: 10px; font-size: 10px;")
        left_layout.addWidget(instructions)
        
        # Load and setup buttons
        load_btn = QPushButton("1. Load STEP File")
        load_btn.clicked.connect(self.load_step_file)
        left_layout.addWidget(load_btn)
        
        origin_btn = QPushButton("2. Create Origin Overlay")
        origin_btn.clicked.connect(self.create_origin)
        left_layout.addWidget(origin_btn)
        
        # LEFT buttons test (Direction - should now work)
        left_group = QLabel("3. Test LEFT Buttons (Direction - FIXED):")
        left_group.setStyleSheet("font-weight: bold; color: red;")
        left_layout.addWidget(left_group)
        
        left_test_layout = QVBoxLayout()
        
        btn_left_x_plus = QPushButton("LEFT X+ (rotate, should move origin)")
        btn_left_x_plus.clicked.connect(lambda: self.test_left_button('x', 15))
        left_test_layout.addWidget(btn_left_x_plus)
        
        btn_left_y_plus = QPushButton("LEFT Y+ (rotate, should move origin)")
        btn_left_y_plus.clicked.connect(lambda: self.test_left_button('y', 15))
        left_test_layout.addWidget(btn_left_y_plus)
        
        btn_left_z_plus = QPushButton("LEFT Z+ (rotate, should move origin)")
        btn_left_z_plus.clicked.connect(lambda: self.test_left_button('z', 15))
        left_test_layout.addWidget(btn_left_z_plus)
        
        left_layout.addLayout(left_test_layout)
        
        # RIGHT buttons test (View Direction - already worked)
        right_group = QLabel("4. Test RIGHT Buttons (View Direction - already worked):")
        right_group.setStyleSheet("font-weight: bold; color: green;")
        left_layout.addWidget(right_group)
        
        right_test_layout = QVBoxLayout()
        
        btn_right_x_plus = QPushButton("RIGHT X+ (move, should move origin)")
        btn_right_x_plus.clicked.connect(lambda: self.test_right_button('x', 1.0))
        right_test_layout.addWidget(btn_right_x_plus)
        
        btn_right_y_plus = QPushButton("RIGHT Y+ (move, should move origin)")
        btn_right_y_plus.clicked.connect(lambda: self.test_right_button('y', 1.0))
        right_test_layout.addWidget(btn_right_y_plus)
        
        btn_right_z_plus = QPushButton("RIGHT Z+ (move, should move origin)")
        btn_right_z_plus.clicked.connect(lambda: self.test_right_button('z', 1.0))
        right_test_layout.addWidget(btn_right_z_plus)
        
        left_layout.addLayout(right_test_layout)
        
        # Reset button
        reset_btn = QPushButton("5. Reset Position")
        reset_btn.clicked.connect(self.reset_position)
        left_layout.addWidget(reset_btn)
        
        left_layout.addStretch()
        layout.addWidget(left_panel)
        
        # Right side - The actual viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
    def load_step_file(self):
        """Load a STEP file"""
        self.status_label.setText("Status: Loading STEP file...")
        QApplication.processEvents()
        
        # Use the viewer's load function
        self.viewer.load_step_file()
        
        self.status_label.setText("Status: STEP file loaded - now create origin overlay")
        
    def create_origin(self):
        """Create origin overlay"""
        self.status_label.setText("Status: Creating origin overlay...")
        QApplication.processEvents()
        
        # Create origin overlay
        self.viewer.create_origin_overlay()
        
        # Check if origin actors were created
        if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
            count = len(self.viewer.vtk_renderer_left.origin_actors)
            self.status_label.setText(f"Status: Origin overlay created ({count} actors) - now test buttons!")
        else:
            self.status_label.setText("Status: Origin overlay creation failed")
        
    def test_left_button(self, axis, degrees):
        """Test LEFT button (Direction - calls rotate_shape)"""
        print(f"\n🔧 TESTING LEFT BUTTON: {axis}+ rotation by {degrees}°")
        print("   This should now ROTATE the world origin (red markers)")
        
        # Set active viewer and rotate
        self.viewer.active_viewer = "top"
        self.viewer.rotate_shape(axis, degrees)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Update status
        self.status_label.setText(f"Status: LEFT {axis}+ tested - did origin rotate?")
        
    def test_right_button(self, axis, amount):
        """Test RIGHT button (View Direction - calls move_shape)"""
        print(f"\n🔧 TESTING RIGHT BUTTON: {axis}+ movement by {amount}mm")
        print("   This should MOVE the world origin (red markers)")
        
        # Set active viewer and move
        self.viewer.active_viewer = "top"
        self.viewer.move_shape(axis, amount)
        
        # Allow time for updates
        QApplication.processEvents()
        time.sleep(0.1)
        
        # Update status
        self.status_label.setText(f"Status: RIGHT {axis}+ tested - did origin move?")
        
    def reset_position(self):
        """Reset to original position"""
        self.status_label.setText("Status: Resetting position...")
        QApplication.processEvents()
        
        self.viewer.reset_to_original()
        
        self.status_label.setText("Status: Position reset - test again!")

def main():
    print("🔥🔥🔥 TESTING DIRECTION BUTTON FIX 🔥🔥🔥")
    print("This test verifies that LEFT buttons now move world origin like RIGHT buttons")
    
    app = QApplication(sys.argv)
    
    # Create the test window
    tester = DirectionButtonFixTester()
    tester.show()
    
    print("✅ Test window created")
    print("📋 Follow the numbered instructions in the left panel")
    
    # Start the event loop
    app.exec_()

if __name__ == "__main__":
    main()
