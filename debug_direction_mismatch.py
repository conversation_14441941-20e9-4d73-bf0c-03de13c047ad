#!/usr/bin/env python3
"""
Debug Direction Mismatch - Investigate why Direction and REF. Direction don't match
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from step_viewer import StepViewerTDK

class DirectionMismatchDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.original_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        self.saved_file = 'test_coordinate_verification.STEP'
        
    def run_debug(self):
        print("🔧 DEBUGGING DIRECTION MISMATCH")
        print("=" * 50)
        
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        QTimer.singleShot(1000, self.step1_load_original)
        self.app.exec_()
        
    def step1_load_original(self):
        print("\n1. Loading original file and checking Direction values...")
        
        success = self.viewer.load_step_file_direct(self.original_file)
        if not success:
            print(f"❌ Failed to load {self.original_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.original_file}")
        
        # Get original STEP file Direction values
        if hasattr(self.viewer.step_loader_left, 'get_original_axis2_placement'):
            axis_data = self.viewer.step_loader_left.get_original_axis2_placement()
            if axis_data:
                print(f"📊 ORIGINAL STEP FILE Direction values:")
                print(f"   Dir1: {axis_data.get('dir1', 'Not found')}")
                print(f"   Dir2: {axis_data.get('dir2', 'Not found')}")
            else:
                print("❌ No AXIS2_PLACEMENT_3D data found in original file")
        
        # Get display values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        print(f"📺 ORIGINAL DISPLAY: {display_text}")
        
        QTimer.singleShot(1000, self.step2_transform_and_check)
        
    def step2_transform_and_check(self):
        print("\n2. Applying transformation and checking Direction values...")
        
        # Apply transformation
        self.viewer.rotate_shape('x', 45)
        print("✅ Applied 45° X rotation")
        
        # Get transformed display values
        display_text = self.viewer.combined_text_actor_left.GetInput()
        print(f"📺 TRANSFORMED DISPLAY: {display_text}")
        
        # Check what the save method would calculate
        print(f"\n🔧 CHECKING SAVE CALCULATION:")
        
        # Get the values that would be saved
        current_pos = self.viewer._extract_position_from_display("top")
        current_rot = self.viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
        orig_rot = self.viewer.orig_rot_left if hasattr(self.viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
        
        print(f"   current_pos: {current_pos}")
        print(f"   current_rot: {current_rot}")
        print(f"   orig_pos: {orig_pos}")
        print(f"   orig_rot: {orig_rot}")
        
        # Calculate deltas
        delta_pos = {
            'x': current_pos['x'] - orig_pos['x'],
            'y': current_pos['y'] - orig_pos['y'],
            'z': current_pos['z'] - orig_pos['z']
        }
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        
        print(f"   delta_pos: {delta_pos}")
        print(f"   delta_rot: {delta_rot}")
        
        QTimer.singleShot(1000, self.step3_save_and_examine)
        
    def step3_save_and_examine(self):
        print("\n3. Saving file and examining STEP content...")
        
        # Save the file
        try:
            self.viewer.active_viewer = "top"
            loader = self.viewer.step_loader_left
            current_pos = self.viewer._extract_position_from_display("top")
            current_rot = self.viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = self.viewer.orig_pos_left if hasattr(self.viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
            orig_rot = self.viewer.orig_rot_left if hasattr(self.viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
            
            success = self.viewer._save_step_with_transformations(
                self.saved_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            
            if success:
                print(f"✅ Saved to {self.saved_file}")
            else:
                print("❌ Save failed")
                self.app.quit()
                return
                
        except Exception as e:
            print(f"❌ Save error: {e}")
            self.app.quit()
            return
            
        # Examine the saved STEP file content
        try:
            with open(self.saved_file, 'r') as f:
                content = f.read()
                
            # Look for AXIS2_PLACEMENT_3D entries
            import re
            axis_pattern = r'#(\d+)\s*=\s*AXIS2_PLACEMENT_3D\s*\([^)]+\)\s*;'
            axis_matches = re.findall(axis_pattern, content)
            
            print(f"🔧 Found {len(axis_matches)} AXIS2_PLACEMENT_3D entries in saved file:")
            
            for axis_id in axis_matches[:3]:  # Show first 3
                # Find the full entry
                full_pattern = f'#{axis_id}\\s*=\\s*AXIS2_PLACEMENT_3D\\s*\\([^)]+\\)\\s*;'
                full_match = re.search(full_pattern, content)
                if full_match:
                    print(f"   #{axis_id}: {full_match.group(0)}")
                    
        except Exception as e:
            print(f"❌ Error examining STEP file: {e}")
            
        QTimer.singleShot(1000, self.step4_load_and_compare)
        
    def step4_load_and_compare(self):
        print("\n4. Loading saved file and comparing Direction values...")
        
        # Set active viewer to bottom
        self.viewer.active_viewer = "bottom"
        
        # Load the saved file
        success = self.viewer.load_step_file_direct(self.saved_file)
        
        if not success:
            print(f"❌ Failed to load {self.saved_file}")
            self.app.quit()
            return
            
        print(f"✅ Loaded {self.saved_file} into BOTTOM viewer")
        
        # Get saved file STEP data
        if hasattr(self.viewer.step_loader_right, 'get_original_axis2_placement'):
            axis_data = self.viewer.step_loader_right.get_original_axis2_placement()
            if axis_data:
                print(f"📊 SAVED STEP FILE Direction values:")
                print(f"   Dir1: {axis_data.get('dir1', 'Not found')}")
                print(f"   Dir2: {axis_data.get('dir2', 'Not found')}")
            else:
                print("❌ No AXIS2_PLACEMENT_3D data found in saved file")
        
        # Get display values from both viewers
        top_display = self.viewer.combined_text_actor_left.GetInput()
        bottom_display = self.viewer.combined_text_actor_right.GetInput()
        
        print(f"\n📺 FINAL COMPARISON:")
        print(f"   TOP DISPLAY:    {top_display}")
        print(f"   BOTTOM DISPLAY: {bottom_display}")
        
        # Extract and compare Direction values
        import re
        direction_pattern = r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        ref_direction_pattern = r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
        
        top_dir = re.search(direction_pattern, top_display)
        top_ref = re.search(ref_direction_pattern, top_display)
        bottom_dir = re.search(direction_pattern, bottom_display)
        bottom_ref = re.search(ref_direction_pattern, bottom_display)
        
        if top_dir and bottom_dir:
            print(f"\n🔧 DIRECTION COMPARISON:")
            print(f"   TOP Direction:    ({top_dir.group(1)}, {top_dir.group(2)}, {top_dir.group(3)})")
            print(f"   BOTTOM Direction: ({bottom_dir.group(1)}, {bottom_dir.group(2)}, {bottom_dir.group(3)})")
            
            if (abs(float(top_dir.group(1)) - float(bottom_dir.group(1))) < 0.01 and
                abs(float(top_dir.group(2)) - float(bottom_dir.group(2))) < 0.01 and
                abs(float(top_dir.group(3)) - float(bottom_dir.group(3))) < 0.01):
                print("   ✅ Direction values MATCH")
            else:
                print("   ❌ Direction values DO NOT MATCH")
                
        if top_ref and bottom_ref:
            print(f"\n🔧 REF. DIRECTION COMPARISON:")
            print(f"   TOP REF. Direction:    ({top_ref.group(1)}, {top_ref.group(2)}, {top_ref.group(3)})")
            print(f"   BOTTOM REF. Direction: ({bottom_ref.group(1)}, {bottom_ref.group(2)}, {bottom_ref.group(3)})")
            
            if (abs(float(top_ref.group(1)) - float(bottom_ref.group(1))) < 0.01 and
                abs(float(top_ref.group(2)) - float(bottom_ref.group(2))) < 0.01 and
                abs(float(top_ref.group(3)) - float(bottom_ref.group(3))) < 0.01):
                print("   ✅ REF. Direction values MATCH")
            else:
                print("   ❌ REF. Direction values DO NOT MATCH")
                
        QTimer.singleShot(3000, self.cleanup_and_exit)
        
    def cleanup_and_exit(self):
        print("\n" + "=" * 50)
        print("DIRECTION MISMATCH DEBUG COMPLETE")
        print("=" * 50)
        
        self.app.quit()

if __name__ == '__main__':
    debugger = DirectionMismatchDebugger()
    debugger.run_debug()
