#!/usr/bin/env python3
"""
Simple test to verify what happens when left buttons are clicked
"""

print("=" * 80)
print("SIMPLE LEFT BUTTON TEST")
print("=" * 80)
print()
print("This test will help us see exactly what's happening.")
print()
print("INSTRUCTIONS:")
print("1. Run: python step_viewer.py")
print("2. Load any STEP file")
print("3. Click 'Toggle Origin Overlay' to show origin markers")
print("4. Note the current position of origin markers")
print("5. Click a left rotation button (e.g., X+ or Y+)")
print("6. Watch the console output carefully")
print("7. Check if origin markers moved")
print()
print("WHAT TO LOOK FOR IN CONSOLE:")
print("✓ 'DEBUG: Rotating X origin actors by Ydeg on Z-axis'")
print("✓ 'DEBUG: Calculated new model center: (X, Y, Z)'")
print("✓ 'DEBUG: Model center moved by delta: (X, Y, Z)'")
print("✓ 'DEBUG: Updated current_pos_left to: {...}'")
print("✓ 'DEBUG: Moved origin actor by delta: (X, Y, Z)'")
print()
print("WHAT SHOULD HAPPEN:")
print("- Origin markers should rotate AND move to follow the model")
print("- They should behave exactly like when you use right buttons")
print("- The debug messages should show non-zero delta values")
print()
print("IF IT'S NOT WORKING:")
print("- You'll see 'DEBUG: No actor found to calculate center'")
print("- Or delta values will be (0.000, 0.000, 0.000)")
print("- Or you won't see the debug messages at all")
print()
print("POSSIBLE ISSUES:")
print("1. The _calculate_model_center_after_rotation method isn't finding actors")
print("2. The new center calculation is wrong")
print("3. The delta calculation is wrong")
print("4. The AddPosition calls aren't working")
print()
print("=" * 80)
print("READY TO TEST - Start step_viewer.py now!")
print("=" * 80)
