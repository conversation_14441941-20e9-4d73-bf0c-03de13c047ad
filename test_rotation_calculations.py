#!/usr/bin/env python3
"""
Test script to calculate expected VTK transformation values
for X+15° and Y+15° rotations
"""

import vtk
import math

def test_rotation_calculations():
    """Calculate expected VTK values for button rotations"""
    print("🎯 Testing VTK Rotation Calculations")
    print("=" * 50)
    
    # Create a test actor
    actor = vtk.vtkActor()
    
    # Test 1: X+15° rotation
    print("\n1️⃣ X+15° Rotation:")
    print("-" * 30)
    
    # Reset actor
    actor.SetOrientation(0, 0, 0)
    initial_orientation = actor.GetOrientation()
    print(f"Initial orientation: X={initial_orientation[0]:.2f}° Y={initial_orientation[1]:.2f}° Z={initial_orientation[2]:.2f}°")
    
    # Apply X+15° rotation
    actor.RotateWXYZ(15, 1, 0, 0)  # 15 degrees around X-axis
    x15_orientation = actor.GetOrientation()
    print(f"After X+15°:        X={x15_orientation[0]:.2f}° Y={x15_orientation[1]:.2f}° Z={x15_orientation[2]:.2f}°")
    
    # Test 2: Y+15° rotation
    print("\n2️⃣ Y+15° Rotation:")
    print("-" * 30)
    
    # Reset actor
    actor.SetOrientation(0, 0, 0)
    initial_orientation = actor.GetOrientation()
    print(f"Initial orientation: X={initial_orientation[0]:.2f}° Y={initial_orientation[1]:.2f}° Z={initial_orientation[2]:.2f}°")
    
    # Apply Y+15° rotation
    actor.RotateWXYZ(15, 0, 1, 0)  # 15 degrees around Y-axis
    y15_orientation = actor.GetOrientation()
    print(f"After Y+15°:        X={y15_orientation[0]:.2f}° Y={y15_orientation[1]:.2f}° Z={y15_orientation[2]:.2f}°")
    
    # Test 3: Combined X+15° then Y+15°
    print("\n3️⃣ Combined X+15° then Y+15°:")
    print("-" * 30)
    
    # Reset actor
    actor.SetOrientation(0, 0, 0)
    print(f"Initial orientation: X={actor.GetOrientation()[0]:.2f}° Y={actor.GetOrientation()[1]:.2f}° Z={actor.GetOrientation()[2]:.2f}°")
    
    # Apply X+15° first
    actor.RotateWXYZ(15, 1, 0, 0)
    after_x = actor.GetOrientation()
    print(f"After X+15°:        X={after_x[0]:.2f}° Y={after_x[1]:.2f}° Z={after_x[2]:.2f}°")
    
    # Then apply Y+15°
    actor.RotateWXYZ(15, 0, 1, 0)
    after_xy = actor.GetOrientation()
    print(f"After X+15°,Y+15°:  X={after_xy[0]:.2f}° Y={after_xy[1]:.2f}° Z={after_xy[2]:.2f}°")
    
    # Test 4: Using Transform matrix approach
    print("\n4️⃣ Using Transform Matrix:")
    print("-" * 30)
    
    transform = vtk.vtkTransform()
    transform.Identity()
    
    # Apply X+15°
    transform.RotateWXYZ(15, 1, 0, 0)
    matrix = transform.GetMatrix()
    
    # Extract Euler angles from matrix
    # This is how VTK internally converts matrix back to orientation
    test_actor = vtk.vtkActor()
    test_actor.SetUserTransform(transform)
    matrix_orientation = test_actor.GetOrientation()
    print(f"Matrix X+15°:       X={matrix_orientation[0]:.2f}° Y={matrix_orientation[1]:.2f}° Z={matrix_orientation[2]:.2f}°")
    
    # Apply Y+15° to the transform
    transform.RotateWXYZ(15, 0, 1, 0)
    test_actor.SetUserTransform(transform)
    matrix_xy_orientation = test_actor.GetOrientation()
    print(f"Matrix X+15°,Y+15°: X={matrix_xy_orientation[0]:.2f}° Y={matrix_xy_orientation[1]:.2f}° Z={matrix_xy_orientation[2]:.2f}°")
    
    # Test 5: Expected values summary
    print("\n5️⃣ Expected Values Summary:")
    print("=" * 50)
    print("When you click rotation buttons in the main program:")
    print(f"• X+15° button should show: X={x15_orientation[0]:.2f} Y={x15_orientation[1]:.2f} Z={x15_orientation[2]:.2f}")
    print(f"• Y+15° button should show: X={y15_orientation[0]:.2f} Y={y15_orientation[1]:.2f} Z={y15_orientation[2]:.2f}")
    print(f"• X+15° then Y+15° should show: X={after_xy[0]:.2f} Y={after_xy[1]:.2f} Z={after_xy[2]:.2f}")
    
    # Test 6: Check coordinate system differences
    print("\n6️⃣ Coordinate System Check:")
    print("-" * 30)
    
    # VTK uses different coordinate system conventions
    # Test with negative values to see the actual display
    actor.SetOrientation(0, 0, 0)
    actor.RotateWXYZ(15, 1, 0, 0)
    vtk_x = actor.GetOrientation()
    
    # The main program might negate values for display
    print(f"VTK GetOrientation():     X={vtk_x[0]:.2f}° Y={vtk_x[1]:.2f}° Z={vtk_x[2]:.2f}°")
    print(f"Negated for display:      X={-vtk_x[0]:.2f}° Y={-vtk_x[1]:.2f}° Z={-vtk_x[2]:.2f}°")
    
    return {
        'x15': x15_orientation,
        'y15': y15_orientation,
        'xy15': after_xy
    }

if __name__ == "__main__":
    results = test_rotation_calculations()
    
    print("\n🎯 QUICK REFERENCE:")
    print("=" * 50)
    print("Copy these expected values to compare with your main program:")
    print(f"X+15°: X={results['x15'][0]:.2f} Y={results['x15'][1]:.2f} Z={results['x15'][2]:.2f}")
    print(f"Y+15°: X={results['y15'][0]:.2f} Y={results['y15'][1]:.2f} Z={results['y15'][2]:.2f}")
    print(f"X+15° then Y+15°: X={results['xy15'][0]:.2f} Y={results['xy15'][1]:.2f} Z={results['xy15'][2]:.2f}")
