#!/usr/bin/env python3
"""
Debug what happens when you actually click the GUI rotation buttons
to see why the origin markers aren't moving.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QPushButton
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import <PERSON><PERSON><PERSON><PERSON>TDK

def main():
    print("🔧 DEBUGGING REAL GUI BUTTON CLICKS")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    def debug_button_click():
        print("\n3. DEBUGGING BUTTON CLICK")
        
        # Check if green origin markers exist
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            initial_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Initial green sphere position: {initial_pos}")
            
            # Find the actual X+ button in the GUI
            buttons = viewer.findChildren(QPushButton)
            x_plus_button = None
            for button in buttons:
                if button.text() == "X+":
                    x_plus_button = button
                    break
            
            if x_plus_button:
                print(f"   Found X+ button: {x_plus_button}")
                print("   Clicking X+ button...")
                
                # Actually click the button (this should trigger the real GUI code path)
                x_plus_button.click()
                
                # Check position after click
                QTimer.singleShot(1000, check_after_click)
            else:
                print("   ❌ Could not find X+ button in GUI")
        else:
            print("   ❌ No green origin sphere found")
    
    def check_after_click():
        print("\n4. CHECKING AFTER BUTTON CLICK")
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            final_pos = renderer.part_origin_sphere.GetPosition()
            print(f"   Final green sphere position: {final_pos}")
            
            # Check current_pos_left values
            current_pos = viewer.current_pos_left
            print(f"   current_pos_left: X={current_pos['x']:.6f}, Y={current_pos['y']:.6f}, Z={current_pos['z']:.6f}")
            
            # Check if the _update_origin_position_after_rotation method was called
            if hasattr(viewer, '_rotation_update_marker'):
                print(f"   _rotation_update_marker: {viewer._rotation_update_marker}")
                if hasattr(viewer, '_rotation_update_values'):
                    print(f"   _rotation_update_values: {viewer._rotation_update_values}")
            else:
                print("   ❌ _rotation_update_marker not found - method may not have been called")
                
            # Check if position actually changed
            initial_pos = (0.0, 0.0, 0.0)  # Assuming it started at origin
            moved = (abs(final_pos[0] - initial_pos[0]) > 0.001 or 
                    abs(final_pos[1] - initial_pos[1]) > 0.001 or 
                    abs(final_pos[2] - initial_pos[2]) > 0.001)
            
            if moved:
                print("   ✅ Green origin markers DID move")
            else:
                print("   ❌ Green origin markers did NOT move")
                print("   🔍 This suggests the fix code path is not being executed")
        else:
            print("   ❌ No green origin sphere found after click")
            
        print("\n5. NEXT STEPS:")
        print("   If markers didn't move, the issue is likely:")
        print("   - GUI buttons call a different method than rotate_shape()")
        print("   - The _update_origin_position_after_rotation() is not being called")
        print("   - There's a different code path for GUI vs direct method calls")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(3000, debug_button_click)
    
    print("\n👀 WATCH THE CONSOLE OUTPUT:")
    print("   - This will show exactly what happens when GUI button is clicked")
    print("   - Look for debug messages from the rotation methods")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
