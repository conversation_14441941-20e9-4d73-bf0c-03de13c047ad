#!/usr/bin/env python3
"""
Test the Fixed Blue Button Save Method
Tests that Method 2 now saves the current displayed geometry instead of copying the original file
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the main viewer
from step_viewer_tdk_modular import StepViewerTDK

class TestFixedBlueSave:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        
    def run_test(self):
        """Test the fixed blue button save method"""
        print("=" * 70)
        print("TESTING FIXED BLUE BUTTON SAVE METHOD")
        print("=" * 70)
        
        # Show the viewer
        self.viewer.show()
        self.app.processEvents()
        time.sleep(1)
        
        # Step 1: Load a STEP file
        print("\n🔧 Step 1: Loading STEP file...")
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found")
            return False
            
        test_file = step_files[0]
        print(f"   Loading: {test_file}")
        
        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.step_loader_left.load_step_file(test_file)
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        # Display in VTK renderer
        self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
        self.viewer.update_text_overlays()
        print("✅ STEP file loaded and displayed")
        
        # Step 2: Apply some rotations
        print("\n🔧 Step 2: Applying rotations...")
        rotations = [('x', 25), ('y', 35), ('z', 50)]
        
        for axis, degrees in rotations:
            print(f"   Rotating {axis.upper()} by {degrees}°...")
            self.viewer.rotate_shape(axis, degrees)
            self.app.processEvents()
            time.sleep(0.3)
            
        print("✅ Rotations applied - model now shows transformed geometry")
        
        # Step 3: Test the FIXED Blue Button save
        print("\n🔧 Step 3: Testing FIXED Blue Button save method...")
        
        test_output = "test_fixed_blue_save_output"
        
        # Call the fixed save method directly
        try:
            loader = self.viewer.step_loader_left
            vtk_renderer = self.viewer.vtk_renderer_left
            
            print("   Calling _save_current_displayed_geometry()...")
            success = self.viewer._save_current_displayed_geometry(
                test_output + ".step", loader, vtk_renderer
            )
            
            if success:
                # Check what files were created
                created_files = []
                for ext in ['.step', '.stl']:
                    test_file_path = test_output + ext
                    if os.path.exists(test_file_path):
                        file_size = os.path.getsize(test_file_path)
                        created_files.append((test_file_path, file_size))
                        print(f"   ✅ Created: {test_file_path} ({file_size:,} bytes)")
                
                if created_files:
                    print(f"✅ FIXED Blue Button: Successfully saved current display!")
                    print(f"   Files created: {len(created_files)}")
                    
                    # Try to load the saved file to verify it's valid
                    for file_path, file_size in created_files:
                        if file_path.endswith('.step'):
                            print(f"   Testing STEP file load...")
                            from step_loader import STEPLoader
                            test_loader = STEPLoader()
                            result = test_loader.load_step_file(file_path)
                            if isinstance(result, tuple) and len(result) >= 2:
                                load_success = result[1] if len(result) == 3 else result[0]
                                if load_success:
                                    print(f"   ✅ Saved STEP file loads successfully")
                                else:
                                    print(f"   ❌ Saved STEP file failed to load")
                        elif file_path.endswith('.stl'):
                            print(f"   ✅ STL file created (contains exact displayed geometry)")
                else:
                    print("❌ No files were created")
                    return False
            else:
                print("❌ FIXED Blue Button: Save method returned False")
                return False
                
        except Exception as e:
            print(f"❌ Error testing fixed blue button: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 70)
        print("FIXED BLUE BUTTON TEST RESULTS:")
        print("✅ Method 2 now saves CURRENT DISPLAYED GEOMETRY")
        print("✅ No longer copies original file")
        print("✅ Captures the visual transformations applied to the model")
        print("=" * 70)
        
        return True

if __name__ == "__main__":
    test = TestFixedBlueSave()
    test.run_test()
    
    # Keep application running briefly to see results
    QTimer.singleShot(5000, test.app.quit)
    test.app.exec_()
