#!/usr/bin/env python3
"""
SYSTEMATIC TEST: Find out exactly what origin actors exist
No guessing - just check what's actually there
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK

def check_origin_actors(viewer):
    """Check exactly what origin actors exist"""
    print("\n🔍 CHECKING WHAT ORIGIN ACTORS ACTUALLY EXIST:")
    print("=" * 60)
    
    # Check left renderer
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        print("📍 LEFT RENDERER:")
        renderer = viewer.vtk_renderer_left
        
        # World origin actors (red markers at 0,0,0)
        print("  🔴 WORLD ORIGIN ACTORS:")
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"    ✅ origin_actors: {len(renderer.origin_actors)} actors")
            for i, actor in enumerate(renderer.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"      Actor {i}: pos={pos}, orient={orient}")
        else:
            print("    ❌ origin_actors: NOT FOUND")
            
        if hasattr(renderer, 'origin_actor') and renderer.origin_actor:
            pos = renderer.origin_actor.GetPosition()
            orient = renderer.origin_actor.GetOrientation()
            print(f"    ✅ origin_actor: pos={pos}, orient={orient}")
        else:
            print("    ❌ origin_actor: NOT FOUND")
        
        # Part origin actors (green markers at STEP file origin)
        print("  🟢 PART ORIGIN ACTORS:")
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            pos = renderer.part_origin_sphere.GetPosition()
            orient = renderer.part_origin_sphere.GetOrientation()
            print(f"    ✅ part_origin_sphere: pos={pos}, orient={orient}")
        else:
            print("    ❌ part_origin_sphere: NOT FOUND")
            
        if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
            pos = renderer.part_origin_x_arrow.GetPosition()
            orient = renderer.part_origin_x_arrow.GetOrientation()
            print(f"    ✅ part_origin_x_arrow: pos={pos}, orient={orient}")
        else:
            print("    ❌ part_origin_x_arrow: NOT FOUND")
            
        if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
            pos = renderer.part_origin_y_arrow.GetPosition()
            orient = renderer.part_origin_y_arrow.GetOrientation()
            print(f"    ✅ part_origin_y_arrow: pos={pos}, orient={orient}")
        else:
            print("    ❌ part_origin_y_arrow: NOT FOUND")
            
        if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
            pos = renderer.part_origin_z_arrow.GetPosition()
            orient = renderer.part_origin_z_arrow.GetOrientation()
            print(f"    ✅ part_origin_z_arrow: pos={pos}, orient={orient}")
        else:
            print("    ❌ part_origin_z_arrow: NOT FOUND")
    else:
        print("❌ LEFT RENDERER: NOT FOUND")

def main():
    print("🎯 SYSTEMATIC ORIGIN ACTOR DETECTION TEST")
    print("=" * 60)
    print("This will tell us exactly what origin actors exist")
    print("and their exact attribute names - no more guessing!")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load STEP file
    step_file = "e:/Python/3d-view/debug_save_test.STEP"
    if not os.path.exists(step_file):
        print(f"❌ ERROR: STEP file not found: {step_file}")
        return False
        
    print(f"\n📁 Loading STEP file: {step_file}")
    success = viewer.load_step_file_direct(step_file)
    if not success:
        print("❌ ERROR: Failed to load STEP file")
        return False
        
    print("✅ STEP file loaded successfully")
    
    # Wait for loading to complete
    app.processEvents()
    time.sleep(2)
    
    # Check what exists BEFORE rotation
    check_origin_actors(viewer)
    
    # Apply rotation
    print(f"\n🔄 Applying X+ rotation (15 degrees)...")
    viewer.rotate_shape('x', 15)
    app.processEvents()
    time.sleep(1)
    
    # Check what exists AFTER rotation
    check_origin_actors(viewer)
    
    # Keep viewer open briefly
    print(f"\n👁️ Keeping viewer open for 3 seconds...")
    QTimer.singleShot(3000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ TEST COMPLETE: Now we know exactly what origin actors exist!")
    else:
        print("\n❌ TEST FAILED: Could not determine origin actor structure")
