#!/usr/bin/env python3
"""
Test the actual GUI buttons to see what happens when they're clicked.
This will help us understand why the automated test passed but the real GUI doesn't work.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🔧 TESTING REAL GUI BUTTONS")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model if available
    if os.path.exists("test.step"):
        print("2. Loading test model...")
        success = viewer.load_step_file_direct("test.step")
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test.step")
            return
    else:
        print("⚠️ No test.step file found")
        return
    
    def test_gui_button():
        print("\n3. TESTING GUI BUTTON CLICK")
        print("   Looking for X+ rotation button...")
        
        # Get origin values before
        before = viewer.current_pos_left.copy()
        print(f"   Before: X={before['x']:.3f}, Y={before['y']:.3f}, Z={before['z']:.3f}")
        
        # Find and click the actual GUI button
        try:
            # Look for QPushButton widgets
            from PyQt5.QtWidgets import QPushButton
            buttons = viewer.findChildren(QPushButton)
            print(f"   Found {len(buttons)} buttons in the GUI")
            
            # Find the X+ button
            x_plus_button = None
            for button in buttons:
                if button.text() == "X+":
                    x_plus_button = button
                    break
            
            if x_plus_button:
                print(f"   Found X+ button: {x_plus_button.text()}")
                print("   Clicking X+ button...")
                
                # Actually click the button
                x_plus_button.click()
                
                # Check results after a delay
                QTimer.singleShot(500, check_results)
            else:
                print("   ❌ Could not find X+ button")
                # Try direct method call as fallback
                print("   Trying direct rotate_shape call...")
                viewer.rotate_shape('x', 15)
                QTimer.singleShot(500, check_results)
                
        except Exception as e:
            print(f"   ❌ Error clicking button: {e}")
    
    def check_results():
        print("\n4. CHECKING RESULTS")
        after = viewer.current_pos_left.copy()
        print(f"   After:  X={after['x']:.3f}, Y={after['y']:.3f}, Z={after['z']:.3f}")
        
        # Check if values changed
        before = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Assuming initial values
        changed = (abs(after['x'] - before['x']) > 0.001 or 
                  abs(after['y'] - before['y']) > 0.001 or 
                  abs(after['z'] - before['z']) > 0.001)
        
        if changed:
            print("   ✅ Origin values DID change - fix is working!")
        else:
            print("   ❌ Origin values did NOT change - fix is not working")
            
        print("\n5. MANUAL VERIFICATION:")
        print("   Look at the yellow text in the 3D viewer")
        print("   Check if 'Origin (X = ... Y = ... Z = ...)' values changed")
    
    # Start test after viewer is fully loaded
    QTimer.singleShot(2000, test_gui_button)
    
    print("\n👀 WATCH THE 3D VIEWER:")
    print("   - This test will automatically click the X+ button")
    print("   - Watch the yellow text overlay for Origin value changes")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
