#!/usr/bin/env python3
"""
Debug the save flow step by step to find where position gets lost
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import vtk

def debug_save_flow():
    """Debug the complete save flow step by step"""
    print("🔍 DEBUGGING SAVE FLOW - Step by step analysis")
    
    # Create application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load the test file
    test_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"\n📁 STEP 1: Loading test file: {test_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if not success:
        print("❌ Failed to load test file")
        return False
    
    # Get original display values
    original_display = viewer._get_display_text("top")
    print(f"📊 ORIGINAL DISPLAY: {original_display}")
    
    print(f"\n🔄 STEP 2: Applying rotation (X=45°)")
    viewer.rotate_shape('x', 45.0)
    
    # Get rotated display values
    rotated_display = viewer._get_display_text("top")
    print(f"📊 ROTATED DISPLAY: {rotated_display}")
    
    print(f"\n🔍 STEP 3: Examining VTK actor transformations")
    vtk_renderer = viewer.vtk_renderer_left
    
    # Check what actors exist
    if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
        print(f"✅ Found {len(vtk_renderer.step_actors)} step_actors")
        first_actor = vtk_renderer.step_actors[0]
        
        # Check UserTransform
        user_transform = first_actor.GetUserTransform()
        if user_transform:
            print(f"✅ Actor has UserTransform")
            matrix = vtk.vtkMatrix4x4()
            user_transform.GetMatrix(matrix)
            print(f"🔧 UserTransform Matrix:")
            for i in range(4):
                row = [matrix.GetElement(i, j) for j in range(4)]
                print(f"   [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
        else:
            print(f"❌ Actor has NO UserTransform")
        
        # Check Position and Orientation
        position = first_actor.GetPosition()
        orientation = first_actor.GetOrientation()
        print(f"🔧 Actor Position: {position}")
        print(f"🔧 Actor Orientation: {orientation}")
        
    elif hasattr(vtk_renderer, 'step_actor') and vtk_renderer.step_actor:
        print(f"✅ Found single step_actor")
        actor = vtk_renderer.step_actor
        
        # Check UserTransform
        user_transform = actor.GetUserTransform()
        if user_transform:
            print(f"✅ Actor has UserTransform")
            matrix = vtk.vtkMatrix4x4()
            user_transform.GetMatrix(matrix)
            print(f"🔧 UserTransform Matrix:")
            for i in range(4):
                row = [matrix.GetElement(i, j) for j in range(4)]
                print(f"   [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
        else:
            print(f"❌ Actor has NO UserTransform")
        
        # Check Position and Orientation
        position = actor.GetPosition()
        orientation = actor.GetOrientation()
        print(f"🔧 Actor Position: {position}")
        print(f"🔧 Actor Orientation: {orientation}")
    else:
        print(f"❌ NO step actors found!")
    
    print(f"\n🔍 STEP 4: Examining step_loader data")
    step_loader = viewer.step_loader_left
    
    # Check current polydata
    if hasattr(step_loader, 'current_polydata') and step_loader.current_polydata:
        polydata = step_loader.current_polydata
        print(f"✅ Found current_polydata with {polydata.GetNumberOfPoints()} points")
        
        # Get bounds
        bounds = polydata.GetBounds()
        print(f"🔧 Polydata bounds: X=[{bounds[0]:.3f}, {bounds[1]:.3f}] Y=[{bounds[2]:.3f}, {bounds[3]:.3f}] Z=[{bounds[4]:.3f}, {bounds[5]:.3f}]")
    else:
        print(f"❌ NO current_polydata found!")
    
    # Check axis data
    if hasattr(step_loader, 'axis_data') and step_loader.axis_data:
        axis_data = step_loader.axis_data
        print(f"✅ Found axis_data: {axis_data}")
    else:
        print(f"❌ NO axis_data found!")
    
    print(f"\n🔍 STEP 5: Testing position extraction from display")
    current_pos = viewer._extract_position_from_display("top")
    print(f"🔧 Extracted position: {current_pos}")
    
    print(f"\n💾 STEP 6: Testing save process")
    save_file = "debug_save_test.STEP"

    # Get the required parameters for save method
    loader = viewer.step_loader_left
    current_rot = viewer._extract_rotation_from_vtk_actor("top")
    orig_rot = viewer.orig_rot_left if hasattr(viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
    orig_pos = viewer.orig_pos_left if hasattr(viewer, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}

    print(f"🔧 Save parameters:")
    print(f"   Current Position: {current_pos}")
    print(f"   Current Rotation: {current_rot}")
    print(f"   Original Position: {orig_pos}")
    print(f"   Original Rotation: {orig_rot}")

    # Test the new simple save approach
    try:
        # Extract current display values (what user sees)
        display_text = viewer._get_display_text("top")
        current_axis_data = viewer._extract_axis_data_from_display(display_text)

        print(f"🔧 Current display axis data:")
        print(f"   Point: {current_axis_data['point']}")
        print(f"   Dir1: {current_axis_data['dir1']}")
        print(f"   Dir2: {current_axis_data['dir2']}")

        # Save with current display values
        success = viewer._save_step_with_current_values(save_file, loader, current_axis_data)
        print(f"🔧 Save result: {success}")
    except Exception as e:
        print(f"❌ Save exception: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🔍 STEP 7: Examining saved file")
    if os.path.exists(save_file):
        print(f"✅ Saved file exists: {save_file}")
        
        # Load it back to see what we get
        viewer.active_viewer = "bottom"
        success = viewer.load_step_file_direct(save_file)
        
        if success:
            loaded_display = viewer._get_display_text("bottom")
            print(f"📊 LOADED DISPLAY: {loaded_display}")
            
            # Compare values
            print(f"\n📊 COMPARISON:")
            print(f"   ORIGINAL: {original_display}")
            print(f"   ROTATED:  {rotated_display}")
            print(f"   LOADED:   {loaded_display}")
        else:
            print(f"❌ Failed to load saved file")
    else:
        print(f"❌ Saved file does not exist")
    
    return True

if __name__ == "__main__":
    debug_save_flow()
