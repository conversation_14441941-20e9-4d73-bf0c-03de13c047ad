#!/usr/bin/env python3
"""
Quick test to verify the left button origin movement fix
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_left_button_fix():
    """Test the left button fix by examining the updated code"""
    
    print("=" * 80)
    print("TESTING LEFT BUTTON ORIGIN MOVEMENT FIX")
    print("=" * 80)
    
    # Read the updated step_viewer.py
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✓ Loaded step_viewer.py")
    except Exception as e:
        print(f"✗ Failed to load step_viewer.py: {e}")
        return
    
    # Check for the new fix code
    print("\n1. CHECKING FOR NEW FIX CODE...")
    
    # Look for the new method
    if '_calculate_model_center_after_rotation' in content:
        print("✓ Found _calculate_model_center_after_rotation method")
    else:
        print("✗ Missing _calculate_model_center_after_rotation method")
    
    # Look for delta calculations
    if 'delta_x = new_center[0] - self.current_pos_left' in content:
        print("✓ Found delta calculation for left viewer")
    else:
        print("✗ Missing delta calculation for left viewer")
    
    if 'delta_x = new_center[0] - self.current_pos_right' in content:
        print("✓ Found delta calculation for right viewer")
    else:
        print("✗ Missing delta calculation for right viewer")
    
    # Look for AddPosition calls
    if 'origin_actor.AddPosition(delta_x, delta_y, delta_z)' in content:
        print("✓ Found AddPosition calls with delta values")
    else:
        print("✗ Missing AddPosition calls with delta values")
    
    # Look for current_pos updates
    if "self.current_pos_left['x'] = new_center[0]" in content:
        print("✓ Found current_pos_left updates")
    else:
        print("✗ Missing current_pos_left updates")
    
    if "self.current_pos_right['x'] = new_center[0]" in content:
        print("✓ Found current_pos_right updates")
    else:
        print("✗ Missing current_pos_right updates")
    
    print("\n2. ANALYZING THE APPROACH...")
    print("The fix should work like this:")
    print("1. After rotation, calculate new model center position")
    print("2. Calculate delta = new_center - old_current_pos")
    print("3. Update current_pos to new_center")
    print("4. Move origin actors by delta using AddPosition()")
    print("5. This matches how right buttons work!")
    
    print("\n3. COMPARISON WITH RIGHT BUTTONS...")
    
    # Count AddPosition calls in move_shape vs rotate_shape
    move_shape_addpos = content.count('origin_actor.AddPosition(amount')
    rotate_shape_addpos = content.count('origin_actor.AddPosition(delta_x')
    
    print(f"move_shape() AddPosition calls: {move_shape_addpos}")
    print(f"rotate_shape() AddPosition calls: {rotate_shape_addpos}")
    
    if rotate_shape_addpos > 0:
        print("✓ rotate_shape() now uses AddPosition like move_shape()")
    else:
        print("✗ rotate_shape() still not using AddPosition")
    
    print("\n" + "=" * 80)
    print("CONCLUSION")
    print("=" * 80)
    
    if (rotate_shape_addpos > 0 and 
        '_calculate_model_center_after_rotation' in content and
        "self.current_pos_left['x'] = new_center[0]" in content):
        print("✅ THE FIX APPEARS TO BE CORRECTLY IMPLEMENTED!")
        print("   The left buttons should now work like the right buttons.")
        print("   Origin markers should follow the model movement after rotation.")
    else:
        print("❌ THE FIX IS INCOMPLETE OR MISSING!")
        print("   Need to check the implementation details.")

if __name__ == "__main__":
    test_left_button_fix()
