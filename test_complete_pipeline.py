#!/usr/bin/env python3
"""
Test script to verify the complete pipeline from STEP file load to text display
"""

import sys
import os
import time

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_complete_pipeline():
    """Test the complete pipeline with GUI"""
    print("🔧 Testing complete pipeline with GUI...")
    
    try:
        # Import Qt modules
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # Import main viewer
        import step_viewer_tdk_modular_fixed
        
        print("✅ Modules imported successfully")
        
        # Create QApplication
        app = QApplication(sys.argv)
        print("✅ QApplication created")
        
        # Create viewer
        viewer = step_viewer_tdk_modular_fixed.StepViewerTDK()
        print("✅ StepViewerTDK created")
        
        # Show viewer
        viewer.show()
        print("✅ Viewer shown")
        
        # Set up a timer to load STEP file after GUI is ready
        def load_step_file():
            print("🔧 Timer triggered - loading STEP file...")
            step_file = "SOIC16P127_1270X940X610L89X51.STEP"
            
            if os.path.exists(step_file):
                print(f"✅ STEP file found: {step_file}")
                
                # Set active viewer to top
                viewer.active_viewer = "top"
                print("✅ Active viewer set to 'top'")
                
                # Load the file
                print("🔧 Calling load_step_file_direct...")
                viewer.load_step_file_direct(step_file)
                
                # Force text update after a short delay
                def force_text_update():
                    print("🔧 Forcing text update...")
                    viewer.update_text_overlays()
                    
                    # Check final state
                    if hasattr(viewer, 'step_loader_left'):
                        axis_data = viewer.step_loader_left.get_original_axis2_placement()
                        print(f"🔧 Final check - AXIS2_PLACEMENT_3D data: {axis_data}")
                    
                    # Exit after test
                    QTimer.singleShot(2000, app.quit)
                
                QTimer.singleShot(2000, force_text_update)
                
            else:
                print(f"❌ STEP file not found: {step_file}")
                app.quit()
        
        # Load STEP file after GUI is ready
        QTimer.singleShot(1000, load_step_file)
        
        # Run the application
        print("🔧 Starting Qt event loop...")
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing complete pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Complete Pipeline Debug Test")
    print("=" * 50)
    
    result = test_complete_pipeline()
    
    print("\n" + "=" * 50)
    print(f"🔧 TEST RESULT: {'✅ PASS' if result else '❌ FAIL'}")
