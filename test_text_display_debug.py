#!/usr/bin/env python3
"""
Test script to debug text display integration in the main viewer
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_text_display_integration():
    """Test the text display integration with AXIS2_PLACEMENT_3D data"""
    print("🔧 Testing text display integration...")
    
    try:
        # Import required modules
        from step_loader import STEPLoader
        print("✅ STEPLoader imported")
        
        # Create a mock viewer class to test the text display logic
        class MockViewer:
            def __init__(self):
                self.step_loader_left = STEPLoader()
                print("✅ Mock viewer created with step_loader_left")
                
            def test_axis_data_retrieval(self):
                """Test the exact logic used in update_text_overlays"""
                print("\n🔧 Testing AXIS2_PLACEMENT_3D data retrieval logic...")
                
                # Load STEP file first
                step_file = "SOIC16P127_1270X940X610L89X51.STEP"
                if not os.path.exists(step_file):
                    print(f"❌ STEP file not found: {step_file}")
                    return False
                    
                success, message = self.step_loader_left.load_step_file(step_file)
                print(f"STEP file load: success={success}")
                
                if not success:
                    print(f"❌ Failed to load STEP file: {message}")
                    return False
                
                # Test the exact condition used in update_text_overlays
                print("\n🔧 Testing hasattr conditions...")
                print(f"   hasattr(self, 'step_loader_left'): {hasattr(self, 'step_loader_left')}")
                print(f"   self.step_loader_left is not None: {self.step_loader_left is not None}")
                print(f"   hasattr(self.step_loader_left, 'get_original_axis2_placement'): {hasattr(self.step_loader_left, 'get_original_axis2_placement')}")
                
                # Test the data retrieval
                if hasattr(self, 'step_loader_left') and self.step_loader_left and hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    print("✅ All conditions met - retrieving axis data...")
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    
                    if axis_data:
                        print("✅ AXIS2_PLACEMENT_3D data retrieved successfully!")
                        print(f"   Point: {axis_data['point']}")
                        print(f"   Dir1: {axis_data['dir1']}")
                        print(f"   Dir2: {axis_data['dir2']}")
                        
                        # Test the text formatting
                        original_axis_text = f"\nOriginal top:\nPoint: {axis_data['point']}\nDir1: {axis_data['dir1']}\nDir2: {axis_data['dir2']}"
                        print(f"\n🔧 Formatted text:\n{original_axis_text}")
                        
                        return True
                    else:
                        print("❌ axis_data is None!")
                        return False
                else:
                    print("❌ Conditions not met!")
                    return False
        
        # Create mock viewer and test
        mock_viewer = MockViewer()
        result = mock_viewer.test_axis_data_retrieval()
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing text display integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_viewer_method():
    """Test the actual update_text_overlays method"""
    print("\n🔧 Testing actual viewer update_text_overlays method...")
    
    try:
        # This would require GUI initialization, so we'll just check the method exists
        import step_viewer_tdk_modular_fixed
        
        # Check if the method exists and has our modifications
        viewer_class = step_viewer_tdk_modular_fixed.StepViewerTDK
        if hasattr(viewer_class, 'update_text_overlays'):
            print("✅ update_text_overlays method exists")
            
            # Read the method source to verify our changes
            import inspect
            source = inspect.getsource(viewer_class.update_text_overlays)
            
            if 'get_original_axis2_placement' in source:
                print("✅ Method contains get_original_axis2_placement call")
            else:
                print("❌ Method does not contain get_original_axis2_placement call")
                return False
                
            if 'Original top:' in source:
                print("✅ Method contains 'Original top:' text")
            else:
                print("❌ Method does not contain 'Original top:' text")
                return False
                
            return True
        else:
            print("❌ update_text_overlays method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing actual viewer method: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Text Display Integration Debug Test")
    print("=" * 50)
    
    # Test 1: Mock integration test
    test1_result = test_text_display_integration()
    
    # Test 2: Actual method verification
    test2_result = test_actual_viewer_method()
    
    print("\n" + "=" * 50)
    print("🔧 TEST RESULTS:")
    print(f"   Text integration test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Actual method test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("✅ All tests passed!")
        print("\n🔧 DIAGNOSIS: The code should be working. Issue might be:")
        print("   1. Text overlay not visible due to positioning")
        print("   2. Text overlay not being updated/rendered")
        print("   3. Model not loaded when text is updated")
    else:
        print("❌ Some tests failed - debugging needed")
