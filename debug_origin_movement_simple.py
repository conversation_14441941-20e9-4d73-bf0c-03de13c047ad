#!/usr/bin/env python3
"""
SIMPLE AUTOMATED DEBUG: Left Button Origin Movement Issue
=========================================================

This program directly analyzes the rotate_shape() method in step_viewer.py
to identify exactly why origin markers don't follow the model movement.
"""

import re
import os

def analyze_rotate_shape_method():
    """Analyze the rotate_shape method to find the origin movement issue"""
    
    print("=" * 80)
    print("AUTOMATED ORIGIN MOVEMENT DEBUG ANALYSIS")
    print("=" * 80)
    
    # Read the step_viewer.py file
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        print("✓ Successfully loaded step_viewer.py")
    except Exception as e:
        print(f"✗ Failed to load step_viewer.py: {e}")
        return
    
    # Find the rotate_shape method
    print("\n1. LOCATING rotate_shape() METHOD...")
    
    # Look for the method definition
    rotate_method_pattern = r'def rotate_shape\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    matches = re.findall(rotate_method_pattern, content, re.DOTALL)
    
    if not matches:
        print("✗ Could not find rotate_shape() method")
        return
    
    method_body = matches[0]
    print(f"✓ Found rotate_shape() method ({len(method_body)} characters)")
    
    # Analyze the method for origin-related code
    print("\n2. ANALYZING ORIGIN MOVEMENT CODE...")
    
    # Check for origin actor position updates
    origin_position_patterns = [
        r'SetPosition.*origin',
        r'origin.*SetPosition',
        r'current_pos_left.*origin',
        r'origin.*current_pos_left'
    ]
    
    origin_updates_found = []
    for pattern in origin_position_patterns:
        matches = re.findall(pattern, method_body, re.IGNORECASE)
        if matches:
            origin_updates_found.extend(matches)
    
    if origin_updates_found:
        print(f"✓ Found {len(origin_updates_found)} origin position updates:")
        for update in origin_updates_found:
            print(f"   - {update}")
    else:
        print("✗ NO origin position updates found in rotate_shape()")
    
    # Check for current_pos updates
    print("\n3. CHECKING current_pos UPDATES...")
    
    current_pos_patterns = [
        r'current_pos_left\[.*?\]\s*=',
        r'current_pos_left\[.*?\]\s*\+=',
        r'current_pos_left\[.*?\]\s*-=',
        r'_update_origin_position_after_rotation'
    ]
    
    pos_updates_found = []
    for pattern in current_pos_patterns:
        matches = re.findall(pattern, method_body, re.IGNORECASE)
        if matches:
            pos_updates_found.extend(matches)
    
    if pos_updates_found:
        print(f"✓ Found {len(pos_updates_found)} current_pos updates:")
        for update in pos_updates_found:
            print(f"   - {update}")
    else:
        print("✗ NO current_pos updates found in rotate_shape()")
    
    # Check for origin actor access
    print("\n4. CHECKING ORIGIN ACTOR ACCESS...")
    
    origin_actor_patterns = [
        r'origin_actors',
        r'part_origin_sphere',
        r'part_origin_.*_arrow',
        r'world_origin'
    ]
    
    actor_access_found = []
    for pattern in origin_actor_patterns:
        matches = re.findall(pattern, method_body, re.IGNORECASE)
        if matches:
            actor_access_found.extend(matches)
    
    if actor_access_found:
        print(f"✓ Found {len(actor_access_found)} origin actor references:")
        for access in set(actor_access_found):  # Remove duplicates
            print(f"   - {access}")
    else:
        print("✗ NO origin actor references found in rotate_shape()")
    
    # Look for the specific fix I implemented
    print("\n5. CHECKING FOR IMPLEMENTED FIX...")
    
    fix_patterns = [
        r'Moving origin actors to new position',
        r'SetPosition.*current_pos_left',
        r'CRITICAL FIX.*origin actors'
    ]
    
    fix_found = []
    for pattern in fix_patterns:
        matches = re.findall(pattern, method_body, re.IGNORECASE)
        if matches:
            fix_found.extend(matches)
    
    if fix_found:
        print(f"✓ Found implemented fix code:")
        for fix in fix_found:
            print(f"   - {fix}")
    else:
        print("✗ Implemented fix code NOT found")
    
    # Extract and show the relevant parts of the method
    print("\n6. SHOWING RELEVANT CODE SECTIONS...")
    
    # Split method into lines for analysis
    lines = method_body.split('\n')
    
    # Look for lines containing origin-related code
    origin_lines = []
    for i, line in enumerate(lines):
        if any(keyword in line.lower() for keyword in ['origin', 'current_pos', 'setposition']):
            origin_lines.append((i, line.strip()))
    
    if origin_lines:
        print("   Origin-related code lines:")
        for line_num, line in origin_lines:
            print(f"   {line_num:3d}: {line}")
    else:
        print("   No origin-related code found")
    
    # Final diagnosis
    print("\n" + "=" * 80)
    print("DIAGNOSIS")
    print("=" * 80)
    
    if not origin_updates_found and not pos_updates_found:
        print("🎯 ISSUE IDENTIFIED:")
        print("   The rotate_shape() method does NOT update origin marker positions")
        print("   after rotation. This is why they don't follow the model.")
        print("\n💡 SOLUTION NEEDED:")
        print("   Add code to move origin actors to the new transformed position")
        print("   after each rotation operation.")
        
        print("\n📝 REQUIRED CODE STRUCTURE:")
        print("   1. Update current_pos_left values with new model center")
        print("   2. Call SetPosition() on all origin actors")
        print("   3. Render the updated scene")
        
    elif pos_updates_found and not origin_updates_found:
        print("🎯 ISSUE IDENTIFIED:")
        print("   current_pos values are updated but origin actors are NOT moved")
        print("   to the new positions.")
        print("\n💡 SOLUTION NEEDED:")
        print("   Add SetPosition() calls for origin actors after rotation.")
        
    elif origin_updates_found and pos_updates_found:
        print("✓ Code appears to have both position updates and origin movement.")
        print("   The issue may be in the implementation details or timing.")
        print("\n🔍 NEED TO CHECK:")
        print("   - Are the position calculations correct?")
        print("   - Are all origin actors being updated?")
        print("   - Is the rendering happening after updates?")
        
    else:
        print("❓ Unclear issue - need manual inspection of the code.")

def check_move_shape_comparison():
    """Check how move_shape works for comparison"""
    
    print("\n" + "=" * 80)
    print("COMPARING WITH move_shape() METHOD")
    print("=" * 80)
    
    try:
        with open('step_viewer.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"✗ Failed to load step_viewer.py: {e}")
        return
    
    # Find the move_shape method
    move_method_pattern = r'def move_shape\(self.*?\n(.*?)(?=\n    def|\nclass|\Z)'
    matches = re.findall(move_method_pattern, content, re.DOTALL)
    
    if not matches:
        print("✗ Could not find move_shape() method")
        return
    
    method_body = matches[0]
    print(f"✓ Found move_shape() method ({len(method_body)} characters)")
    
    # Check for origin position updates in move_shape
    origin_position_patterns = [
        r'SetPosition.*origin',
        r'origin.*SetPosition',
        r'AddPosition.*origin',
        r'origin.*AddPosition'
    ]
    
    origin_updates_found = []
    for pattern in origin_position_patterns:
        matches = re.findall(pattern, method_body, re.IGNORECASE)
        if matches:
            origin_updates_found.extend(matches)
    
    if origin_updates_found:
        print(f"✓ move_shape() HAS {len(origin_updates_found)} origin position updates:")
        for update in origin_updates_found:
            print(f"   - {update}")
        print("\n💡 This confirms that move_shape() correctly updates origin positions")
        print("   while rotate_shape() does not!")
    else:
        print("✗ move_shape() also has no origin position updates")

if __name__ == "__main__":
    analyze_rotate_shape_method()
    check_move_shape_comparison()
    
    print("\n" + "=" * 80)
    print("DEBUG COMPLETE")
    print("=" * 80)
    print("This analysis shows exactly what's missing from rotate_shape().")
    print("The fix needs to be implemented in the rotate_shape() method.")
