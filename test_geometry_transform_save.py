#!/usr/bin/env python3
"""
Test the new geometry transformation save functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import vtk

def test_geometry_transform_save():
    """Test that the geometry transformation save works correctly"""
    print("🧪 Testing geometry transformation save functionality...")
    
    # Create application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load the test file
    test_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"📁 Loading test file: {test_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if not success:
        print("❌ Failed to load test file")
        return False
    
    print("✅ Test file loaded successfully")
    
    # Apply some rotations to the model
    print("🔄 Applying rotations to the model...")
    viewer.rotate_shape('x', 45.0)  # Rotate 45° around X-axis
    viewer.rotate_shape('y', 30.0)  # Rotate 30° around Y-axis
    
    # Get the display values before saving
    display_text = viewer._get_display_text("top")
    print(f"📊 Display values before save: {display_text}")
    
    # Save the transformed model
    save_file = "test_transformed_geometry.STEP"
    print(f"💾 Saving transformed model to: {save_file}")
    
    success = viewer._save_step_with_transformations(save_file)
    
    if not success:
        print("❌ Save failed")
        return False
    
    print("✅ Save completed successfully")
    
    # Load the saved file in the bottom viewer to verify
    print("🔍 Loading saved file for verification...")
    viewer.active_viewer = "bottom"
    success = viewer.load_step_file_direct(save_file)
    
    if not success:
        print("❌ Failed to load saved file")
        return False
    
    print("✅ Saved file loaded successfully")
    
    # Compare the display values
    bottom_display_text = viewer._get_display_text("bottom")
    print(f"📊 Display values after load: {bottom_display_text}")
    
    # Extract key values for comparison
    import re
    
    def extract_values(text):
        origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
        direction_match = re.search(r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
        ref_direction_match = re.search(r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
        
        if origin_match and direction_match and ref_direction_match:
            return {
                'origin': (float(origin_match.group(1)), float(origin_match.group(2)), float(origin_match.group(3))),
                'direction': (float(direction_match.group(1)), float(direction_match.group(2)), float(direction_match.group(3))),
                'ref_direction': (float(ref_direction_match.group(1)), float(ref_direction_match.group(2)), float(ref_direction_match.group(3)))
            }
        return None
    
    top_values = extract_values(display_text)
    bottom_values = extract_values(bottom_display_text)
    
    if not top_values or not bottom_values:
        print("❌ Could not extract values for comparison")
        return False
    
    print(f"🔍 TOP values: {top_values}")
    print(f"🔍 BOTTOM values: {bottom_values}")
    
    # Check if values match (with some tolerance for floating point precision)
    def values_match(v1, v2, tolerance=0.001):
        return all(abs(a - b) < tolerance for a, b in zip(v1, v2))
    
    origin_match = values_match(top_values['origin'], bottom_values['origin'])
    direction_match = values_match(top_values['direction'], bottom_values['direction'])
    ref_direction_match = values_match(top_values['ref_direction'], bottom_values['ref_direction'])
    
    print(f"📊 Origin match: {origin_match}")
    print(f"📊 Direction match: {direction_match}")
    print(f"📊 REF. Direction match: {ref_direction_match}")
    
    if origin_match and direction_match and ref_direction_match:
        print("🎉 SUCCESS: All values match! Geometry transformation save is working correctly.")
        return True
    else:
        print("❌ FAILURE: Values do not match. Geometry transformation save needs more work.")
        return False

if __name__ == "__main__":
    success = test_geometry_transform_save()
    sys.exit(0 if success else 1)
