#!/usr/bin/env python3
"""
Test QVTKRenderWindowInteractor availability
"""

print("🔍 Testing QVTKRenderWindowInteractor availability...")

try:
    # Try the same import as in vtk_renderer.py
    try:
        from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        print("✅ QVTKRenderWindowInteractor imported from vtkmodules.qt")
        qvtk_available = True
    except ImportError:
        try:
            from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
            print("✅ QVTKRenderWindowInteractor imported from vtk.qt")
            qvtk_available = True
        except ImportError:
            print("❌ QVTKRenderWindowInteractor import failed")
            QVTKRenderWindowInteractor = None
            qvtk_available = False
    
    print(f"QVTKRenderWindowInteractor = {QVTKRenderWindowInteractor}")
    print(f"bool(QVTKRenderWindowInteractor) = {bool(QVTKRenderWindowInteractor)}")
    
    if QVTKRenderWindowInteractor:
        print("✅ QVTKRenderWindowInteractor condition would be TRUE")
        print("   SafeModelRotationStyle SHOULD be instantiated")
    else:
        print("❌ QVTKRenderWindowInteractor condition would be FALSE")
        print("   SafeModelRotationStyle would NOT be instantiated")
        print("   This explains why mouse rotation isn't working!")
        
    # Test instantiation
    if qvtk_available:
        print("\n🔧 Testing QVTKRenderWindowInteractor instantiation...")
        try:
            widget = QVTKRenderWindowInteractor()
            print("✅ QVTKRenderWindowInteractor can be instantiated")
            widget.close()
        except Exception as e:
            print(f"❌ QVTKRenderWindowInteractor instantiation failed: {e}")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    print(f"Traceback: {traceback.format_exc()}")
