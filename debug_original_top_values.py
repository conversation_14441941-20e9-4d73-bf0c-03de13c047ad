#!/usr/bin/env python3
"""
Debug why Original TOP values are still wrong in the GUI
"""

import sys
import os

def test_step_loader_data_format():
    """Test the exact format of data returned by step_loader"""
    print("🔧 Testing step_loader data format...")
    
    try:
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        success, message = loader.load_step_file("SOIC16P127_1270X940X610L89X51.STEP")
        
        if success:
            print("✅ STEP file loaded")
            
            # Get the raw data
            axis_data = loader.get_original_axis2_placement()
            print(f"🔧 Raw axis_data: {axis_data}")
            print(f"🔧 Type of axis_data: {type(axis_data)}")
            
            if axis_data:
                for key, value in axis_data.items():
                    print(f"🔧 {key}: {value} (type: {type(value)})")
                
                # Test the exact formatting used in the GUI
                original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                
                print(f"\n🔧 Formatted text:\n{original_axis_text}")
                
                # Check if the values match what we expect
                expected_point = "(-4.19, -3.6673, 0.4914)"
                actual_point = str(axis_data['point'])
                
                print(f"\n🔧 Expected point: {expected_point}")
                print(f"🔧 Actual point: {actual_point}")
                print(f"🔧 Points match: {expected_point == actual_point}")
                
                return True
            else:
                print("❌ No axis_data returned")
                return False
        else:
            print(f"❌ STEP file loading failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_update_text_overlays_logic():
    """Test the exact logic from update_text_overlays method"""
    print("\n🔧 Testing GUI update_text_overlays logic...")
    
    try:
        # Mock the step_loader_left with real data
        class MockStepLoaderLeft:
            def get_original_axis2_placement(self):
                # Return the exact same format as the real step_loader
                return {
                    'point': '(-4.19, -3.6673, 0.4914)',
                    'dir1': '(0.0, 0.9104, -0.4138)',
                    'dir2': '(0.0, 0.4138, 0.9104)'
                }
        
        # Simulate the exact code from update_text_overlays
        step_loader_left = MockStepLoaderLeft()
        
        # This is the exact code from the fixed update_text_overlays method
        original_axis_text = ""
        if hasattr(step_loader_left, 'get_original_axis2_placement'):
            axis_data = step_loader_left.get_original_axis2_placement()
            if axis_data:
                print("✅ USING REAL STEP FILE: Getting actual Original top values from STEP file")
                original_axis_text = f"""\nOriginal top:
Point: {axis_data['point']}
Dir1: {axis_data['dir1']}
Dir2: {axis_data['dir2']}"""
                print(f"✅ REAL STEP DATA: original_axis_text set to: {repr(original_axis_text)}")
            else:
                print("🔧 WARNING: No AXIS2_PLACEMENT_3D data found in STEP file")
                original_axis_text = "\nOriginal top: No coordinate system data found"
        else:
            print("🔧 WARNING: step_loader_left does not have get_original_axis2_placement method")
            original_axis_text = "\nOriginal top: Method not available"
        
        # Test cursor display
        cursor_text = "CURSOR: X=0.10 Y=0.05 Z=-0.00"
        cursor_display = f"{cursor_text}{original_axis_text}"
        
        print(f"\n🔧 Final cursor_display:\n{cursor_display}")
        
        # Check if this matches what we expect
        expected_display = """CURSOR: X=0.10 Y=0.05 Z=-0.00
Original top:
Point: (-4.19, -3.6673, 0.4914)
Dir1: (0.0, 0.9104, -0.4138)
Dir2: (0.0, 0.4138, 0.9104)"""
        
        print(f"\n🔧 Expected display:\n{expected_display}")
        print(f"\n🔧 Displays match: {cursor_display == expected_display}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_if_hardcoded_values_still_exist():
    """Check if there are still hardcoded values somewhere in the code"""
    print("\n🔧 Checking for remaining hardcoded values...")
    
    try:
        # Read the main program file
        with open('step_viewer_tdk_modular_fixed.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the old hardcoded values
        old_hardcoded_patterns = [
            "(-4.190000000000000, -3.667300000000000, 0.491400000000000)",
            "(0.000000000000000, 0.910400000000000, -0.413800000000000)",
            "(0.000000000000000, 0.413800000000000, 0.910400000000000)",
            "DIRECT OVERRIDE: Forcing correct Original top values"
        ]
        
        found_hardcoded = []
        for pattern in old_hardcoded_patterns:
            if pattern in content:
                found_hardcoded.append(pattern)
        
        if found_hardcoded:
            print("❌ Found remaining hardcoded values:")
            for pattern in found_hardcoded:
                print(f"   - {pattern}")
            return False
        else:
            print("✅ No hardcoded values found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking file: {e}")
        return False

def main():
    """Run all debug tests"""
    print("🔥 DEBUG ORIGINAL TOP VALUES 🔥")
    print("=" * 50)
    
    # Test 1: Step loader data format
    print("1. TESTING STEP LOADER DATA FORMAT")
    print("-" * 40)
    result1 = test_step_loader_data_format()
    
    # Test 2: GUI logic
    print("\n2. TESTING GUI LOGIC")
    print("-" * 40)
    result2 = test_gui_update_text_overlays_logic()
    
    # Test 3: Check for hardcoded values
    print("\n3. CHECKING FOR HARDCODED VALUES")
    print("-" * 40)
    result3 = check_if_hardcoded_values_still_exist()
    
    # Summary
    print("\n" + "=" * 50)
    print("DEBUG RESULTS:")
    print("=" * 50)
    print(f"Step Loader Data: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"GUI Logic: {'✅ PASS' if result2 else '❌ FAIL'}")
    print(f"No Hardcoded Values: {'✅ PASS' if result3 else '❌ FAIL'}")
    
    if not result3:
        print("\n⚠️  HARDCODED VALUES STILL EXIST - This is the problem!")
    elif result1 and result2:
        print("\n🤔 Logic seems correct - the issue might be elsewhere")
    else:
        print("\n⚠️  Issues found in step loader or GUI logic")

if __name__ == "__main__":
    main()
